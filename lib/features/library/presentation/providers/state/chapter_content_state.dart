import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

part 'chapter_content_state.freezed.dart';

@freezed
abstract class ChapterContentState with _$ChapterContentState {
  factory ChapterContentState({
    List<ContentIndexData>? pronunciationContent,
    List<ContentIndexData>? conversationContent,
    List<ContentIndexData>? listeningContent,
    List<ContentIndexData>? speakingContent,
  }) = _ChapterContentState;

  // Allow custom getters / setters
  const ChapterContentState._();
}
