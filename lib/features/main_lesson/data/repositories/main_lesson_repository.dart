import 'package:selfeng/features/main_lesson/data/datasource/main_lesson_local_datasource.dart';
import 'package:selfeng/features/main_lesson/data/datasource/main_lesson_remote_datasource.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class MainLessonRepositoryImpl extends MainLessonRepository {
  final MainLessonLocalDatasource localDatasource;
  final MainLessonRemoteDatasource remoteDatasource;
  MainLessonRepositoryImpl({
    required this.localDatasource,
    required this.remoteDatasource,
  });

  @override
  Future<Either<AppException, PronunciationResultFormatted>>
  checkPronunciation({
    required AudioPath audio,
    required String text,
    bool autoDelete = true,
  }) {
    return remoteDatasource.checkPronunciation(
      audio: audio,
      text: text,
      autoDelete: autoDelete,
    );
  }

  @override
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  ) {
    return remoteDatasource.getChapterData(level, chapter);
  }

  @override
  Future<Either<AppException, List<ChapterIndexData>>> getChapterIndex({
    required String level,
  }) {
    return remoteDatasource.getChapterIndex(level: level);
  }

  @override
  Future<Either<AppException, List<ChapterData>>> getChapters(Level level) {
    return remoteDatasource.getChapters(level);
  }

  @override
  Future<Either<AppException, ConversationPart>> getConversation({
    required String path,
  }) {
    return remoteDatasource.getConversation(path: path);
  }

  @override
  Future<Either<AppException, List<ConversationPart>>> getConversationList(
    List<String> paths,
  ) {
    return remoteDatasource.getConversationList(paths);
  }

  @override
  Future<Either<AppException, ContentIndexData>> getFirstLesson() {
    return remoteDatasource.getFirstLesson();
  }

  @override
  Future<Either<AppException, ListeningPart>> getListening({
    required String path,
  }) {
    return remoteDatasource.getListening(path: path);
  }

  @override
  Future<Either<AppException, List<ListeningPart>>> getListeningList(
    List<String> paths,
  ) {
    return remoteDatasource.getListeningList(paths);
  }

  @override
  Future<Either<AppException, List<ContentIndexData>>> getPathIndex({
    required String level,
    required String chapter,
    required SectionType section,
  }) {
    return remoteDatasource.getPathIndex(
      level: level,
      chapter: chapter,
      section: section,
    );
  }

  @override
  Future<Either<AppException, PronunciationSubPart>> getPronunciation({
    required String path,
  }) {
    return remoteDatasource.getPronunciation(path: path);
  }

  @override
  Future<Either<AppException, SpeakingPart>> getSpeaking({
    required String path,
  }) {
    return remoteDatasource.getSpeaking(path: path);
  }

  @override
  Future<Either<AppException, List<SpeakingPart>>> getSpeakingList(
    List<String> paths,
  ) {
    return remoteDatasource.getSpeakingList(paths);
  }

  @override
  Future<Either<AppException, dynamic>> saveAnswer(answer) {
    return remoteDatasource.saveAnswer(answer);
  }

  @override
  Future<Either<AppException, dynamic>> uploadAudio({required String path}) {
    return remoteDatasource.uploadAudio(path: path);
  }

  @override
  Future<Either<AppException, bool>> saveIntro({required String lessonName}) {
    return localDatasource.saveIntro(lessonName: lessonName);
  }

  @override
  Future<Either<AppException, bool>> isIntro({required String lessonName}) {
    return localDatasource.isIntro(lessonName: lessonName);
  }
}
