import 'package:device_info_plus/device_info_plus.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/platform_service/domain/repositories/platform_service.dart';
import 'package:selfeng/shared/data/local/shared_prefs_storage_service.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';

// Service Mocks

/// Mock implementation of PlatformService for testing
class MockPlatformService extends Mock implements PlatformService {}

/// Mock implementation of SharedPrefsService for testing
class MockSharedPrefsService extends Mock implements SharedPrefsService {}

/// Mock implementation of StorageService for testing
class MockStorageService extends Mock implements StorageService {}

// Device Info Mocks

/// Mock implementation of DeviceInfoPlugin for testing
class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

/// Mock implementation of AndroidDeviceInfo for testing
class MockAndroidDeviceInfo extends Mock implements AndroidDeviceInfo {}

/// Mock implementation of IosDeviceInfo for testing
class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}
