import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/questionnaire/domain/models/questionnaire_model.dart';

part 'questionnaire_state.freezed.dart';

@freezed
abstract class QuestionnaireState with _$QuestionnaireState {
  factory QuestionnaireState({
    @Default([]) List<QuestionnaireModel> questions,
    dynamic selectedAnswerQ1,
    dynamic selectedAnswerQ2,
    dynamic selectedAnswerQ3,
    dynamic selectedAnswerQ4,
    dynamic selectedAnswerQ5,
    dynamic selectedAnswerQ6,
    dynamic selectedAnswerQ7,
    dynamic selectedAnswerQ8,
    @Default(0) int currentQuestion,
    ScrollController? controllerScroll,
  }) = _QuestionnaireState;

  int get questionLength => questions.length - 1;

  // Allow custom getters / setters
  const QuestionnaireState._();
}

ScrollController defaultController(value) => ScrollController();
