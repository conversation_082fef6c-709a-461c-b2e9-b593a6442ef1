import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/fcm_service/domain/repositories/fcm_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../../mocks/service_mocks.dart';
import '../../../../mocks/repository_mocks.dart';
import '../../../../mocks/firebase_mocks.dart';

void main() {
  group('FCMServiceRepository', () {
    late FCMServiceRepository repository;
    late MockUserDataServiceRepository mockUserDataService;
    late MockFirebaseMessaging mockFirebaseMessaging;
    late MockDeviceInfoPlugin mockDeviceInfo;
    late MockFirestoreServiceRepository mockFirestoreService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockAndroidDeviceInfo mockAndroidDeviceInfo;
    late MockIosDeviceInfo mockIosDeviceInfo;
    late MockPlatformService mockPlatformService;

    setUp(() {
      mockUserDataService = MockUserDataServiceRepository();
      mockFirebaseMessaging = MockFirebaseMessaging();
      mockDeviceInfo = MockDeviceInfoPlugin();
      mockFirestoreService = mockUserDataService.dataSource;
      mockFirebaseAuth = mockFirestoreService.firebaseAuth;
      mockUser = MockUser();
      mockAndroidDeviceInfo = MockAndroidDeviceInfo();
      mockIosDeviceInfo = MockIosDeviceInfo();
      mockPlatformService = MockPlatformService();

      repository = FCMServiceRepository(
        mockUserDataService,
        mockFirebaseMessaging,
        mockDeviceInfo,
        mockPlatformService,
      );

      // Default mocks
      when(() => mockUser.uid).thenReturn('test_uid');
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(
        () => mockFirebaseMessaging.getToken(),
      ).thenAnswer((_) async => 'test_fcm_token');
      when(() => mockAndroidDeviceInfo.id).thenReturn('test_android_id');
      when(
        () => mockIosDeviceInfo.identifierForVendor,
      ).thenReturn('test_ios_id');
      when(
        () => mockDeviceInfo.androidInfo,
      ).thenAnswer((_) async => mockAndroidDeviceInfo);
      when(
        () => mockDeviceInfo.iosInfo,
      ).thenAnswer((_) async => mockIosDeviceInfo);
      when(
        () => mockUserDataService.saveFCMToken(
          token: any(named: 'token'),
          deviceId: any(named: 'deviceId'),
        ),
      ).thenAnswer((_) async => const Right(null));
      when(
        () => mockFirebaseMessaging.onTokenRefresh,
      ).thenAnswer((_) => Stream.empty());
    });

    group('initializeFCMToken', () {
      test(
        'should successfully initialize and save FCM token for authenticated user on Android',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseMessaging.getToken()).called(1);
          verify(() => mockDeviceInfo.androidInfo).called(1);
          verify(
            () => mockUserDataService.saveFCMToken(
              token: 'test_fcm_token',
              deviceId: 'test_android_id',
            ),
          ).called(1);
        },
      );

      test(
        'should successfully initialize and save FCM token for authenticated user on iOS',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(false);
          when(() => mockPlatformService.isIOS).thenReturn(true);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseMessaging.getToken()).called(1);
          verify(() => mockDeviceInfo.iosInfo).called(1);
          verify(
            () => mockUserDataService.saveFCMToken(
              token: 'test_fcm_token',
              deviceId: 'test_ios_id',
            ),
          ).called(1);
        },
      );

      test(
        'should skip FCM token save when user is not authenticated',
        () async {
          // Arrange
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseAuth.currentUser).called(1);
          verifyNever(() => mockFirebaseMessaging.getToken());
        },
      );

      test('should return Left when FCM token is empty', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => '');

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect(
          (result as Left).value.identifier,
          'FCM token initialization failed',
        );
      });

      test('should return Left when getting FCM token fails', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenThrow(Exception('Failure'));

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'FCM token retrieval failed');
      });

      test('should return Left when device ID is empty', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(() => mockAndroidDeviceInfo.id).thenReturn('');

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Device ID retrieval failed');
      });
    });

    group('getFCMToken', () {
      test('should return FCM token on non-web platform', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => 'mobile_token');
        // Act
        final result = await repository.getFCMToken();
        // Assert
        expect((result as Right).value, 'mobile_token');
      });

      test('should return empty string when FCM token is null', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => null);
        // Act
        final result = await repository.getFCMToken();
        // Assert
        expect((result as Right).value, '');
      });

      test(
        'should return Left with AppException when getting FCM token fails',
        () async {
          // Arrange
          when(
            () => mockFirebaseMessaging.getToken(),
          ).thenThrow(Exception('Failed to get token'));
          // Act
          final result = await repository.getFCMToken();
          // Assert
          expect(result, isA<Left<AppException, String>>());
          expect(
            (result as Left<AppException, String>).value.identifier,
            equals('FCM token retrieval failed'),
          );
        },
      );
    });

    group('getDeviceId', () {
      test('should return Android device ID', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect((result as Right).value, 'test_android_id');
      });

      test('should return iOS device ID', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(true);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect((result as Right).value, 'test_ios_id');
      });

      test('should return Left for unsupported platform', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect(result, isA<Left<AppException, String>>());
        expect(
          (result as Left).value.identifier,
          equals('Unsupported platform'),
        );
      });
    });

    group('requestPermissions', () {
      test('should request notification permissions successfully', () async {
        // Arrange
        final notificationSettings = MockNotificationSettings();
        when(
          () => mockFirebaseMessaging.requestPermission(
            alert: true,
            announcement: true,
            badge: true,
            carPlay: true,
            criticalAlert: true,
            provisional: false,
            sound: true,
          ),
        ).thenAnswer((_) async => notificationSettings);
        // Act
        final result = await repository.requestPermissions();
        // Assert
        expect(result, isA<Right<AppException, NotificationSettings>>());
        verify(
          () => mockFirebaseMessaging.requestPermission(
            alert: true,
            announcement: true,
            badge: true,
            carPlay: true,
            criticalAlert: true,
            provisional: false,
            sound: true,
          ),
        ).called(1);
      });

      test(
        'should return Left with AppException when requesting permissions fails',
        () async {
          // Arrange
          when(
            () => mockFirebaseMessaging.requestPermission(
              alert: any(named: 'alert'),
              announcement: any(named: 'announcement'),
              badge: any(named: 'badge'),
              carPlay: any(named: 'carPlay'),
              criticalAlert: any(named: 'criticalAlert'),
              provisional: any(named: 'provisional'),
              sound: any(named: 'sound'),
            ),
          ).thenThrow(Exception('Permission request failed'));
          // Act
          final result = await repository.requestPermissions();
          // Assert
          expect(result, isA<Left<AppException, NotificationSettings>>());
          expect(
            (result as Left<AppException, NotificationSettings>)
                .value
                .identifier,
            equals('Permission request failed'),
          );
        },
      );
    });
  });
}
