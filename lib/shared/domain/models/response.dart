import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class Response {
  final int statusCode;
  final String? statusMessage;
  final dynamic data;
  final dynamic message;
  final String? refreshToken;

  Response({
    required this.statusCode,
    this.statusMessage,
    this.data = const {},
    this.message = const {},
    this.refreshToken,
  });
  @override
  String toString() {
    return 'statusCode=$statusCode\nstatusMessage=$statusMessage\n data=$data';
  }
}

extension ResponseExtension on Response {
  Right<AppException, Response> get toRight => Right(this);
}
