import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/presentation/providers/state/chapter_content_state.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

class ChapterContentStateNotifier extends StateNotifier<ChapterContentState> {
  ChapterContentStateNotifier() : super(ChapterContentState());

  void setPronunciation(List<ContentIndexData> data) {
    state = state.copyWith(pronunciationContent: data);
  }

  void updatePronunciation(ContentIndexData data) {
    final currentList = state.pronunciationContent;
    if (currentList != null) {
      final pronunciationIndex = currentList.indexWhere(
        (item) => item.contentPath == data.contentPath,
      );
      if (pronunciationIndex != -1) {
        final updatedList = List<ContentIndexData>.from(currentList);
        final originalItem = updatedList[pronunciationIndex];
        updatedList[pronunciationIndex] = originalItem.copyWith(
          hasResult: true,
          isBookmarked: data.isBookmarked,
        );
        state = state.copyWith(pronunciationContent: updatedList);
      }
    }
  }

  void setConversation(List<ContentIndexData> data) {
    state = state.copyWith(conversationContent: data);
  }

  void updateConversation(ContentIndexData data) {
    final currentList = state.conversationContent;
    if (currentList != null) {
      final conversationIndex = currentList.indexWhere(
        (item) => item.contentPath == data.contentPath,
      );
      if (conversationIndex != -1) {
        final updatedList = List<ContentIndexData>.from(currentList);
        final originalItem = updatedList[conversationIndex];
        updatedList[conversationIndex] = originalItem.copyWith(
          hasResult: true,
          isBookmarked: data.isBookmarked,
        );
        state = state.copyWith(conversationContent: updatedList);
      }
    }
  }

  void setListening(List<ContentIndexData> data) {
    state = state.copyWith(listeningContent: data);
  }

  void updateListening(ContentIndexData data) {
    final currentList = state.listeningContent;
    if (currentList != null) {
      final listeningIndex = currentList.indexWhere(
        (item) => item.contentPath == data.contentPath,
      );
      if (listeningIndex != -1) {
        final updatedList = List<ContentIndexData>.from(currentList);
        final originalItem = updatedList[listeningIndex];
        updatedList[listeningIndex] = originalItem.copyWith(
          hasResult: true,
          isBookmarked: data.isBookmarked,
        );
        state = state.copyWith(listeningContent: updatedList);
      }
    }
  }

  void setSpeaking(List<ContentIndexData> data) {
    state = state.copyWith(speakingContent: data);
  }

  void updateSpeaking(ContentIndexData data, SpeakingStage stage) {
    final currentList = state.speakingContent;
    if (currentList != null) {
      final speakingIndex = currentList.indexWhere(
        (item) => item.contentPath == data.contentPath,
      );
      if (speakingIndex != -1) {
        final updatedList = List<ContentIndexData>.from(currentList);
        final originalItem = updatedList[speakingIndex];
        switch (stage) {
          case SpeakingStage.stage1:
            updatedList[speakingIndex] = originalItem.copyWith(
              firstStage: true,
              isBookmarked: data.isBookmarked,
            );
            break;
          case SpeakingStage.stage2:
            updatedList[speakingIndex] = originalItem.copyWith(
              secondStage: true,
              isBookmarked: data.isBookmarked,
            );
            break;
          case SpeakingStage.stage3:
            updatedList[speakingIndex] = originalItem.copyWith(
              thirdStage: true,
              isBookmarked: data.isBookmarked,
            );
            break;
          default:
            break;
        }
        // updatedList[speakingIndex] = originalItem.copyWith(hasResult: true);
        state = state.copyWith(speakingContent: updatedList);
      }
    }
  }
}

@riverpod
StateNotifierProvider<ChapterContentStateNotifier, ChapterContentState>
chapterContentStateProvider = StateNotifierProvider(
  (ref) => ChapterContentStateNotifier(),
);
