// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_word.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WordObject {

 String get word; double? get verticalPosition; double? get horizontalPosition; double? get speed; bool get selected;
/// Create a copy of WordObject
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WordObjectCopyWith<WordObject> get copyWith => _$WordObjectCopyWithImpl<WordObject>(this as WordObject, _$identity);

  /// Serializes this WordObject to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WordObject&&(identical(other.word, word) || other.word == word)&&(identical(other.verticalPosition, verticalPosition) || other.verticalPosition == verticalPosition)&&(identical(other.horizontalPosition, horizontalPosition) || other.horizontalPosition == horizontalPosition)&&(identical(other.speed, speed) || other.speed == speed)&&(identical(other.selected, selected) || other.selected == selected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,word,verticalPosition,horizontalPosition,speed,selected);

@override
String toString() {
  return 'WordObject(word: $word, verticalPosition: $verticalPosition, horizontalPosition: $horizontalPosition, speed: $speed, selected: $selected)';
}


}

/// @nodoc
abstract mixin class $WordObjectCopyWith<$Res>  {
  factory $WordObjectCopyWith(WordObject value, $Res Function(WordObject) _then) = _$WordObjectCopyWithImpl;
@useResult
$Res call({
 String word, double? verticalPosition, double? horizontalPosition, double? speed, bool selected
});




}
/// @nodoc
class _$WordObjectCopyWithImpl<$Res>
    implements $WordObjectCopyWith<$Res> {
  _$WordObjectCopyWithImpl(this._self, this._then);

  final WordObject _self;
  final $Res Function(WordObject) _then;

/// Create a copy of WordObject
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? word = null,Object? verticalPosition = freezed,Object? horizontalPosition = freezed,Object? speed = freezed,Object? selected = null,}) {
  return _then(_self.copyWith(
word: null == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String,verticalPosition: freezed == verticalPosition ? _self.verticalPosition : verticalPosition // ignore: cast_nullable_to_non_nullable
as double?,horizontalPosition: freezed == horizontalPosition ? _self.horizontalPosition : horizontalPosition // ignore: cast_nullable_to_non_nullable
as double?,speed: freezed == speed ? _self.speed : speed // ignore: cast_nullable_to_non_nullable
as double?,selected: null == selected ? _self.selected : selected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [WordObject].
extension WordObjectPatterns on WordObject {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WordObject value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WordObject() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WordObject value)  $default,){
final _that = this;
switch (_that) {
case _WordObject():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WordObject value)?  $default,){
final _that = this;
switch (_that) {
case _WordObject() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String word,  double? verticalPosition,  double? horizontalPosition,  double? speed,  bool selected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WordObject() when $default != null:
return $default(_that.word,_that.verticalPosition,_that.horizontalPosition,_that.speed,_that.selected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String word,  double? verticalPosition,  double? horizontalPosition,  double? speed,  bool selected)  $default,) {final _that = this;
switch (_that) {
case _WordObject():
return $default(_that.word,_that.verticalPosition,_that.horizontalPosition,_that.speed,_that.selected);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String word,  double? verticalPosition,  double? horizontalPosition,  double? speed,  bool selected)?  $default,) {final _that = this;
switch (_that) {
case _WordObject() when $default != null:
return $default(_that.word,_that.verticalPosition,_that.horizontalPosition,_that.speed,_that.selected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WordObject implements WordObject {
   _WordObject({required this.word, this.verticalPosition, this.horizontalPosition, this.speed, this.selected = false});
  factory _WordObject.fromJson(Map<String, dynamic> json) => _$WordObjectFromJson(json);

@override final  String word;
@override final  double? verticalPosition;
@override final  double? horizontalPosition;
@override final  double? speed;
@override@JsonKey() final  bool selected;

/// Create a copy of WordObject
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WordObjectCopyWith<_WordObject> get copyWith => __$WordObjectCopyWithImpl<_WordObject>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WordObjectToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WordObject&&(identical(other.word, word) || other.word == word)&&(identical(other.verticalPosition, verticalPosition) || other.verticalPosition == verticalPosition)&&(identical(other.horizontalPosition, horizontalPosition) || other.horizontalPosition == horizontalPosition)&&(identical(other.speed, speed) || other.speed == speed)&&(identical(other.selected, selected) || other.selected == selected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,word,verticalPosition,horizontalPosition,speed,selected);

@override
String toString() {
  return 'WordObject(word: $word, verticalPosition: $verticalPosition, horizontalPosition: $horizontalPosition, speed: $speed, selected: $selected)';
}


}

/// @nodoc
abstract mixin class _$WordObjectCopyWith<$Res> implements $WordObjectCopyWith<$Res> {
  factory _$WordObjectCopyWith(_WordObject value, $Res Function(_WordObject) _then) = __$WordObjectCopyWithImpl;
@override @useResult
$Res call({
 String word, double? verticalPosition, double? horizontalPosition, double? speed, bool selected
});




}
/// @nodoc
class __$WordObjectCopyWithImpl<$Res>
    implements _$WordObjectCopyWith<$Res> {
  __$WordObjectCopyWithImpl(this._self, this._then);

  final _WordObject _self;
  final $Res Function(_WordObject) _then;

/// Create a copy of WordObject
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? word = null,Object? verticalPosition = freezed,Object? horizontalPosition = freezed,Object? speed = freezed,Object? selected = null,}) {
  return _then(_WordObject(
word: null == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String,verticalPosition: freezed == verticalPosition ? _self.verticalPosition : verticalPosition // ignore: cast_nullable_to_non_nullable
as double?,horizontalPosition: freezed == horizontalPosition ? _self.horizontalPosition : horizontalPosition // ignore: cast_nullable_to_non_nullable
as double?,speed: freezed == speed ? _self.speed : speed // ignore: cast_nullable_to_non_nullable
as double?,selected: null == selected ? _self.selected : selected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$MemoryFlashTopic {

 int? get order; String? get title;@JsonKey(name: 'icon_url') String? get icon;@JsonKey(name: 'image_url') String? get image; String? get references;
/// Create a copy of MemoryFlashTopic
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemoryFlashTopicCopyWith<MemoryFlashTopic> get copyWith => _$MemoryFlashTopicCopyWithImpl<MemoryFlashTopic>(this as MemoryFlashTopic, _$identity);

  /// Serializes this MemoryFlashTopic to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemoryFlashTopic&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.image, image) || other.image == image)&&(identical(other.references, references) || other.references == references));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,title,icon,image,references);

@override
String toString() {
  return 'MemoryFlashTopic(order: $order, title: $title, icon: $icon, image: $image, references: $references)';
}


}

/// @nodoc
abstract mixin class $MemoryFlashTopicCopyWith<$Res>  {
  factory $MemoryFlashTopicCopyWith(MemoryFlashTopic value, $Res Function(MemoryFlashTopic) _then) = _$MemoryFlashTopicCopyWithImpl;
@useResult
$Res call({
 int? order, String? title,@JsonKey(name: 'icon_url') String? icon,@JsonKey(name: 'image_url') String? image, String? references
});




}
/// @nodoc
class _$MemoryFlashTopicCopyWithImpl<$Res>
    implements $MemoryFlashTopicCopyWith<$Res> {
  _$MemoryFlashTopicCopyWithImpl(this._self, this._then);

  final MemoryFlashTopic _self;
  final $Res Function(MemoryFlashTopic) _then;

/// Create a copy of MemoryFlashTopic
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? order = freezed,Object? title = freezed,Object? icon = freezed,Object? image = freezed,Object? references = freezed,}) {
  return _then(_self.copyWith(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,references: freezed == references ? _self.references : references // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [MemoryFlashTopic].
extension MemoryFlashTopicPatterns on MemoryFlashTopic {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MemoryFlashTopic value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MemoryFlashTopic() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MemoryFlashTopic value)  $default,){
final _that = this;
switch (_that) {
case _MemoryFlashTopic():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MemoryFlashTopic value)?  $default,){
final _that = this;
switch (_that) {
case _MemoryFlashTopic() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? order,  String? title, @JsonKey(name: 'icon_url')  String? icon, @JsonKey(name: 'image_url')  String? image,  String? references)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MemoryFlashTopic() when $default != null:
return $default(_that.order,_that.title,_that.icon,_that.image,_that.references);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? order,  String? title, @JsonKey(name: 'icon_url')  String? icon, @JsonKey(name: 'image_url')  String? image,  String? references)  $default,) {final _that = this;
switch (_that) {
case _MemoryFlashTopic():
return $default(_that.order,_that.title,_that.icon,_that.image,_that.references);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? order,  String? title, @JsonKey(name: 'icon_url')  String? icon, @JsonKey(name: 'image_url')  String? image,  String? references)?  $default,) {final _that = this;
switch (_that) {
case _MemoryFlashTopic() when $default != null:
return $default(_that.order,_that.title,_that.icon,_that.image,_that.references);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MemoryFlashTopic implements MemoryFlashTopic {
   _MemoryFlashTopic({this.order, this.title, @JsonKey(name: 'icon_url') this.icon, @JsonKey(name: 'image_url') this.image, this.references});
  factory _MemoryFlashTopic.fromJson(Map<String, dynamic> json) => _$MemoryFlashTopicFromJson(json);

@override final  int? order;
@override final  String? title;
@override@JsonKey(name: 'icon_url') final  String? icon;
@override@JsonKey(name: 'image_url') final  String? image;
@override final  String? references;

/// Create a copy of MemoryFlashTopic
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemoryFlashTopicCopyWith<_MemoryFlashTopic> get copyWith => __$MemoryFlashTopicCopyWithImpl<_MemoryFlashTopic>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemoryFlashTopicToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemoryFlashTopic&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.image, image) || other.image == image)&&(identical(other.references, references) || other.references == references));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,title,icon,image,references);

@override
String toString() {
  return 'MemoryFlashTopic(order: $order, title: $title, icon: $icon, image: $image, references: $references)';
}


}

/// @nodoc
abstract mixin class _$MemoryFlashTopicCopyWith<$Res> implements $MemoryFlashTopicCopyWith<$Res> {
  factory _$MemoryFlashTopicCopyWith(_MemoryFlashTopic value, $Res Function(_MemoryFlashTopic) _then) = __$MemoryFlashTopicCopyWithImpl;
@override @useResult
$Res call({
 int? order, String? title,@JsonKey(name: 'icon_url') String? icon,@JsonKey(name: 'image_url') String? image, String? references
});




}
/// @nodoc
class __$MemoryFlashTopicCopyWithImpl<$Res>
    implements _$MemoryFlashTopicCopyWith<$Res> {
  __$MemoryFlashTopicCopyWithImpl(this._self, this._then);

  final _MemoryFlashTopic _self;
  final $Res Function(_MemoryFlashTopic) _then;

/// Create a copy of MemoryFlashTopic
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? order = freezed,Object? title = freezed,Object? icon = freezed,Object? image = freezed,Object? references = freezed,}) {
  return _then(_MemoryFlashTopic(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,references: freezed == references ? _self.references : references // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$MemoryFlash {

 int? get order; String? get topic; int? get countdown; String? get references;@JsonKey(name: 'list_text', fromJson: fromMapToString) List<String> get listText;
/// Create a copy of MemoryFlash
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemoryFlashCopyWith<MemoryFlash> get copyWith => _$MemoryFlashCopyWithImpl<MemoryFlash>(this as MemoryFlash, _$identity);

  /// Serializes this MemoryFlash to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemoryFlash&&(identical(other.order, order) || other.order == order)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.countdown, countdown) || other.countdown == countdown)&&(identical(other.references, references) || other.references == references)&&const DeepCollectionEquality().equals(other.listText, listText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,topic,countdown,references,const DeepCollectionEquality().hash(listText));

@override
String toString() {
  return 'MemoryFlash(order: $order, topic: $topic, countdown: $countdown, references: $references, listText: $listText)';
}


}

/// @nodoc
abstract mixin class $MemoryFlashCopyWith<$Res>  {
  factory $MemoryFlashCopyWith(MemoryFlash value, $Res Function(MemoryFlash) _then) = _$MemoryFlashCopyWithImpl;
@useResult
$Res call({
 int? order, String? topic, int? countdown, String? references,@JsonKey(name: 'list_text', fromJson: fromMapToString) List<String> listText
});




}
/// @nodoc
class _$MemoryFlashCopyWithImpl<$Res>
    implements $MemoryFlashCopyWith<$Res> {
  _$MemoryFlashCopyWithImpl(this._self, this._then);

  final MemoryFlash _self;
  final $Res Function(MemoryFlash) _then;

/// Create a copy of MemoryFlash
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? order = freezed,Object? topic = freezed,Object? countdown = freezed,Object? references = freezed,Object? listText = null,}) {
  return _then(_self.copyWith(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,countdown: freezed == countdown ? _self.countdown : countdown // ignore: cast_nullable_to_non_nullable
as int?,references: freezed == references ? _self.references : references // ignore: cast_nullable_to_non_nullable
as String?,listText: null == listText ? _self.listText : listText // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [MemoryFlash].
extension MemoryFlashPatterns on MemoryFlash {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MemoryFlash value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MemoryFlash() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MemoryFlash value)  $default,){
final _that = this;
switch (_that) {
case _MemoryFlash():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MemoryFlash value)?  $default,){
final _that = this;
switch (_that) {
case _MemoryFlash() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? order,  String? topic,  int? countdown,  String? references, @JsonKey(name: 'list_text', fromJson: fromMapToString)  List<String> listText)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MemoryFlash() when $default != null:
return $default(_that.order,_that.topic,_that.countdown,_that.references,_that.listText);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? order,  String? topic,  int? countdown,  String? references, @JsonKey(name: 'list_text', fromJson: fromMapToString)  List<String> listText)  $default,) {final _that = this;
switch (_that) {
case _MemoryFlash():
return $default(_that.order,_that.topic,_that.countdown,_that.references,_that.listText);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? order,  String? topic,  int? countdown,  String? references, @JsonKey(name: 'list_text', fromJson: fromMapToString)  List<String> listText)?  $default,) {final _that = this;
switch (_that) {
case _MemoryFlash() when $default != null:
return $default(_that.order,_that.topic,_that.countdown,_that.references,_that.listText);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MemoryFlash implements MemoryFlash {
   _MemoryFlash({this.order, this.topic, this.countdown, this.references, @JsonKey(name: 'list_text', fromJson: fromMapToString) final  List<String> listText = const []}): _listText = listText;
  factory _MemoryFlash.fromJson(Map<String, dynamic> json) => _$MemoryFlashFromJson(json);

@override final  int? order;
@override final  String? topic;
@override final  int? countdown;
@override final  String? references;
 final  List<String> _listText;
@override@JsonKey(name: 'list_text', fromJson: fromMapToString) List<String> get listText {
  if (_listText is EqualUnmodifiableListView) return _listText;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_listText);
}


/// Create a copy of MemoryFlash
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemoryFlashCopyWith<_MemoryFlash> get copyWith => __$MemoryFlashCopyWithImpl<_MemoryFlash>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemoryFlashToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemoryFlash&&(identical(other.order, order) || other.order == order)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.countdown, countdown) || other.countdown == countdown)&&(identical(other.references, references) || other.references == references)&&const DeepCollectionEquality().equals(other._listText, _listText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,topic,countdown,references,const DeepCollectionEquality().hash(_listText));

@override
String toString() {
  return 'MemoryFlash(order: $order, topic: $topic, countdown: $countdown, references: $references, listText: $listText)';
}


}

/// @nodoc
abstract mixin class _$MemoryFlashCopyWith<$Res> implements $MemoryFlashCopyWith<$Res> {
  factory _$MemoryFlashCopyWith(_MemoryFlash value, $Res Function(_MemoryFlash) _then) = __$MemoryFlashCopyWithImpl;
@override @useResult
$Res call({
 int? order, String? topic, int? countdown, String? references,@JsonKey(name: 'list_text', fromJson: fromMapToString) List<String> listText
});




}
/// @nodoc
class __$MemoryFlashCopyWithImpl<$Res>
    implements _$MemoryFlashCopyWith<$Res> {
  __$MemoryFlashCopyWithImpl(this._self, this._then);

  final _MemoryFlash _self;
  final $Res Function(_MemoryFlash) _then;

/// Create a copy of MemoryFlash
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? order = freezed,Object? topic = freezed,Object? countdown = freezed,Object? references = freezed,Object? listText = null,}) {
  return _then(_MemoryFlash(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as String?,countdown: freezed == countdown ? _self.countdown : countdown // ignore: cast_nullable_to_non_nullable
as int?,references: freezed == references ? _self.references : references // ignore: cast_nullable_to_non_nullable
as String?,listText: null == listText ? _self._listText : listText // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
