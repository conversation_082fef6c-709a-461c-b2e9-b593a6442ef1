// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'questionnaire_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuestionnaireModel {

 String? get questionId;@JsonKey(includeToJson: false) bool get status;@JsonKey(includeToJson: false) List<Choices>? get choices;@JsonKey(includeToJson: false) int? get order;@JsonKey(includeToJson: false) Map<String, dynamic>? get question;@JsonKey(includeToJson: false) int? get type;@JsonKey(name: 'multiple_answer', includeToJson: false) bool get multipleAnswer; dynamic get answer;
/// Create a copy of QuestionnaireModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QuestionnaireModelCopyWith<QuestionnaireModel> get copyWith => _$QuestionnaireModelCopyWithImpl<QuestionnaireModel>(this as QuestionnaireModel, _$identity);

  /// Serializes this QuestionnaireModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is QuestionnaireModel&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.choices, choices)&&(identical(other.order, order) || other.order == order)&&const DeepCollectionEquality().equals(other.question, question)&&(identical(other.type, type) || other.type == type)&&(identical(other.multipleAnswer, multipleAnswer) || other.multipleAnswer == multipleAnswer)&&const DeepCollectionEquality().equals(other.answer, answer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,questionId,status,const DeepCollectionEquality().hash(choices),order,const DeepCollectionEquality().hash(question),type,multipleAnswer,const DeepCollectionEquality().hash(answer));

@override
String toString() {
  return 'QuestionnaireModel(questionId: $questionId, status: $status, choices: $choices, order: $order, question: $question, type: $type, multipleAnswer: $multipleAnswer, answer: $answer)';
}


}

/// @nodoc
abstract mixin class $QuestionnaireModelCopyWith<$Res>  {
  factory $QuestionnaireModelCopyWith(QuestionnaireModel value, $Res Function(QuestionnaireModel) _then) = _$QuestionnaireModelCopyWithImpl;
@useResult
$Res call({
 String? questionId,@JsonKey(includeToJson: false) bool status,@JsonKey(includeToJson: false) List<Choices>? choices,@JsonKey(includeToJson: false) int? order,@JsonKey(includeToJson: false) Map<String, dynamic>? question,@JsonKey(includeToJson: false) int? type,@JsonKey(name: 'multiple_answer', includeToJson: false) bool multipleAnswer, dynamic answer
});




}
/// @nodoc
class _$QuestionnaireModelCopyWithImpl<$Res>
    implements $QuestionnaireModelCopyWith<$Res> {
  _$QuestionnaireModelCopyWithImpl(this._self, this._then);

  final QuestionnaireModel _self;
  final $Res Function(QuestionnaireModel) _then;

/// Create a copy of QuestionnaireModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? questionId = freezed,Object? status = null,Object? choices = freezed,Object? order = freezed,Object? question = freezed,Object? type = freezed,Object? multipleAnswer = null,Object? answer = freezed,}) {
  return _then(_self.copyWith(
questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,choices: freezed == choices ? _self.choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choices>?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,question: freezed == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as int?,multipleAnswer: null == multipleAnswer ? _self.multipleAnswer : multipleAnswer // ignore: cast_nullable_to_non_nullable
as bool,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}

}


/// Adds pattern-matching-related methods to [QuestionnaireModel].
extension QuestionnaireModelPatterns on QuestionnaireModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _QuestionnaireModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _QuestionnaireModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _QuestionnaireModel value)  $default,){
final _that = this;
switch (_that) {
case _QuestionnaireModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _QuestionnaireModel value)?  $default,){
final _that = this;
switch (_that) {
case _QuestionnaireModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? questionId, @JsonKey(includeToJson: false)  bool status, @JsonKey(includeToJson: false)  List<Choices>? choices, @JsonKey(includeToJson: false)  int? order, @JsonKey(includeToJson: false)  Map<String, dynamic>? question, @JsonKey(includeToJson: false)  int? type, @JsonKey(name: 'multiple_answer', includeToJson: false)  bool multipleAnswer,  dynamic answer)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _QuestionnaireModel() when $default != null:
return $default(_that.questionId,_that.status,_that.choices,_that.order,_that.question,_that.type,_that.multipleAnswer,_that.answer);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? questionId, @JsonKey(includeToJson: false)  bool status, @JsonKey(includeToJson: false)  List<Choices>? choices, @JsonKey(includeToJson: false)  int? order, @JsonKey(includeToJson: false)  Map<String, dynamic>? question, @JsonKey(includeToJson: false)  int? type, @JsonKey(name: 'multiple_answer', includeToJson: false)  bool multipleAnswer,  dynamic answer)  $default,) {final _that = this;
switch (_that) {
case _QuestionnaireModel():
return $default(_that.questionId,_that.status,_that.choices,_that.order,_that.question,_that.type,_that.multipleAnswer,_that.answer);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? questionId, @JsonKey(includeToJson: false)  bool status, @JsonKey(includeToJson: false)  List<Choices>? choices, @JsonKey(includeToJson: false)  int? order, @JsonKey(includeToJson: false)  Map<String, dynamic>? question, @JsonKey(includeToJson: false)  int? type, @JsonKey(name: 'multiple_answer', includeToJson: false)  bool multipleAnswer,  dynamic answer)?  $default,) {final _that = this;
switch (_that) {
case _QuestionnaireModel() when $default != null:
return $default(_that.questionId,_that.status,_that.choices,_that.order,_that.question,_that.type,_that.multipleAnswer,_that.answer);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _QuestionnaireModel implements QuestionnaireModel {
  const _QuestionnaireModel({this.questionId, @JsonKey(includeToJson: false) this.status = false, @JsonKey(includeToJson: false) final  List<Choices>? choices, @JsonKey(includeToJson: false) this.order, @JsonKey(includeToJson: false) final  Map<String, dynamic>? question, @JsonKey(includeToJson: false) this.type, @JsonKey(name: 'multiple_answer', includeToJson: false) this.multipleAnswer = false, this.answer}): _choices = choices,_question = question;
  factory _QuestionnaireModel.fromJson(Map<String, dynamic> json) => _$QuestionnaireModelFromJson(json);

@override final  String? questionId;
@override@JsonKey(includeToJson: false) final  bool status;
 final  List<Choices>? _choices;
@override@JsonKey(includeToJson: false) List<Choices>? get choices {
  final value = _choices;
  if (value == null) return null;
  if (_choices is EqualUnmodifiableListView) return _choices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(includeToJson: false) final  int? order;
 final  Map<String, dynamic>? _question;
@override@JsonKey(includeToJson: false) Map<String, dynamic>? get question {
  final value = _question;
  if (value == null) return null;
  if (_question is EqualUnmodifiableMapView) return _question;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeToJson: false) final  int? type;
@override@JsonKey(name: 'multiple_answer', includeToJson: false) final  bool multipleAnswer;
@override final  dynamic answer;

/// Create a copy of QuestionnaireModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QuestionnaireModelCopyWith<_QuestionnaireModel> get copyWith => __$QuestionnaireModelCopyWithImpl<_QuestionnaireModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$QuestionnaireModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _QuestionnaireModel&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._choices, _choices)&&(identical(other.order, order) || other.order == order)&&const DeepCollectionEquality().equals(other._question, _question)&&(identical(other.type, type) || other.type == type)&&(identical(other.multipleAnswer, multipleAnswer) || other.multipleAnswer == multipleAnswer)&&const DeepCollectionEquality().equals(other.answer, answer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,questionId,status,const DeepCollectionEquality().hash(_choices),order,const DeepCollectionEquality().hash(_question),type,multipleAnswer,const DeepCollectionEquality().hash(answer));

@override
String toString() {
  return 'QuestionnaireModel(questionId: $questionId, status: $status, choices: $choices, order: $order, question: $question, type: $type, multipleAnswer: $multipleAnswer, answer: $answer)';
}


}

/// @nodoc
abstract mixin class _$QuestionnaireModelCopyWith<$Res> implements $QuestionnaireModelCopyWith<$Res> {
  factory _$QuestionnaireModelCopyWith(_QuestionnaireModel value, $Res Function(_QuestionnaireModel) _then) = __$QuestionnaireModelCopyWithImpl;
@override @useResult
$Res call({
 String? questionId,@JsonKey(includeToJson: false) bool status,@JsonKey(includeToJson: false) List<Choices>? choices,@JsonKey(includeToJson: false) int? order,@JsonKey(includeToJson: false) Map<String, dynamic>? question,@JsonKey(includeToJson: false) int? type,@JsonKey(name: 'multiple_answer', includeToJson: false) bool multipleAnswer, dynamic answer
});




}
/// @nodoc
class __$QuestionnaireModelCopyWithImpl<$Res>
    implements _$QuestionnaireModelCopyWith<$Res> {
  __$QuestionnaireModelCopyWithImpl(this._self, this._then);

  final _QuestionnaireModel _self;
  final $Res Function(_QuestionnaireModel) _then;

/// Create a copy of QuestionnaireModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? questionId = freezed,Object? status = null,Object? choices = freezed,Object? order = freezed,Object? question = freezed,Object? type = freezed,Object? multipleAnswer = null,Object? answer = freezed,}) {
  return _then(_QuestionnaireModel(
questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,choices: freezed == choices ? _self._choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choices>?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,question: freezed == question ? _self._question : question // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as int?,multipleAnswer: null == multipleAnswer ? _self.multipleAnswer : multipleAnswer // ignore: cast_nullable_to_non_nullable
as bool,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}


}


/// @nodoc
mixin _$Choices {

 String? get image;@JsonKey(name: 'image_url') String? get imageUrl; String? get value; Map<String, dynamic>? get text;
/// Create a copy of Choices
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChoicesCopyWith<Choices> get copyWith => _$ChoicesCopyWithImpl<Choices>(this as Choices, _$identity);

  /// Serializes this Choices to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Choices&&(identical(other.image, image) || other.image == image)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.value, value) || other.value == value)&&const DeepCollectionEquality().equals(other.text, text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,image,imageUrl,value,const DeepCollectionEquality().hash(text));

@override
String toString() {
  return 'Choices(image: $image, imageUrl: $imageUrl, value: $value, text: $text)';
}


}

/// @nodoc
abstract mixin class $ChoicesCopyWith<$Res>  {
  factory $ChoicesCopyWith(Choices value, $Res Function(Choices) _then) = _$ChoicesCopyWithImpl;
@useResult
$Res call({
 String? image,@JsonKey(name: 'image_url') String? imageUrl, String? value, Map<String, dynamic>? text
});




}
/// @nodoc
class _$ChoicesCopyWithImpl<$Res>
    implements $ChoicesCopyWith<$Res> {
  _$ChoicesCopyWithImpl(this._self, this._then);

  final Choices _self;
  final $Res Function(Choices) _then;

/// Create a copy of Choices
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? image = freezed,Object? imageUrl = freezed,Object? value = freezed,Object? text = freezed,}) {
  return _then(_self.copyWith(
image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String?,text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [Choices].
extension ChoicesPatterns on Choices {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Choices value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Choices() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Choices value)  $default,){
final _that = this;
switch (_that) {
case _Choices():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Choices value)?  $default,){
final _that = this;
switch (_that) {
case _Choices() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? image, @JsonKey(name: 'image_url')  String? imageUrl,  String? value,  Map<String, dynamic>? text)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Choices() when $default != null:
return $default(_that.image,_that.imageUrl,_that.value,_that.text);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? image, @JsonKey(name: 'image_url')  String? imageUrl,  String? value,  Map<String, dynamic>? text)  $default,) {final _that = this;
switch (_that) {
case _Choices():
return $default(_that.image,_that.imageUrl,_that.value,_that.text);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? image, @JsonKey(name: 'image_url')  String? imageUrl,  String? value,  Map<String, dynamic>? text)?  $default,) {final _that = this;
switch (_that) {
case _Choices() when $default != null:
return $default(_that.image,_that.imageUrl,_that.value,_that.text);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Choices implements Choices {
  const _Choices({this.image, @JsonKey(name: 'image_url') this.imageUrl, this.value, final  Map<String, dynamic>? text}): _text = text;
  factory _Choices.fromJson(Map<String, dynamic> json) => _$ChoicesFromJson(json);

@override final  String? image;
@override@JsonKey(name: 'image_url') final  String? imageUrl;
@override final  String? value;
 final  Map<String, dynamic>? _text;
@override Map<String, dynamic>? get text {
  final value = _text;
  if (value == null) return null;
  if (_text is EqualUnmodifiableMapView) return _text;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of Choices
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChoicesCopyWith<_Choices> get copyWith => __$ChoicesCopyWithImpl<_Choices>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChoicesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Choices&&(identical(other.image, image) || other.image == image)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.value, value) || other.value == value)&&const DeepCollectionEquality().equals(other._text, _text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,image,imageUrl,value,const DeepCollectionEquality().hash(_text));

@override
String toString() {
  return 'Choices(image: $image, imageUrl: $imageUrl, value: $value, text: $text)';
}


}

/// @nodoc
abstract mixin class _$ChoicesCopyWith<$Res> implements $ChoicesCopyWith<$Res> {
  factory _$ChoicesCopyWith(_Choices value, $Res Function(_Choices) _then) = __$ChoicesCopyWithImpl;
@override @useResult
$Res call({
 String? image,@JsonKey(name: 'image_url') String? imageUrl, String? value, Map<String, dynamic>? text
});




}
/// @nodoc
class __$ChoicesCopyWithImpl<$Res>
    implements _$ChoicesCopyWith<$Res> {
  __$ChoicesCopyWithImpl(this._self, this._then);

  final _Choices _self;
  final $Res Function(_Choices) _then;

/// Create a copy of Choices
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? image = freezed,Object? imageUrl = freezed,Object? value = freezed,Object? text = freezed,}) {
  return _then(_Choices(
image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,value: freezed == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String?,text: freezed == text ? _self._text : text // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
