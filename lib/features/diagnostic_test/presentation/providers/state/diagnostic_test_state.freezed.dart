// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'diagnostic_test_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DiagnosticTestState {

 List<QuestionModel> get questions; QuestionResultModel? get result; int get currentPage; bool get expandedResult; ScrollController? get controllerScroll;
/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DiagnosticTestStateCopyWith<DiagnosticTestState> get copyWith => _$DiagnosticTestStateCopyWithImpl<DiagnosticTestState>(this as DiagnosticTestState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DiagnosticTestState&&const DeepCollectionEquality().equals(other.questions, questions)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.controllerScroll, controllerScroll) || other.controllerScroll == controllerScroll));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(questions),result,currentPage,expandedResult,controllerScroll);

@override
String toString() {
  return 'DiagnosticTestState(questions: $questions, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, controllerScroll: $controllerScroll)';
}


}

/// @nodoc
abstract mixin class $DiagnosticTestStateCopyWith<$Res>  {
  factory $DiagnosticTestStateCopyWith(DiagnosticTestState value, $Res Function(DiagnosticTestState) _then) = _$DiagnosticTestStateCopyWithImpl;
@useResult
$Res call({
 List<QuestionModel> questions, QuestionResultModel? result, int currentPage, bool expandedResult, ScrollController? controllerScroll
});


$QuestionResultModelCopyWith<$Res>? get result;

}
/// @nodoc
class _$DiagnosticTestStateCopyWithImpl<$Res>
    implements $DiagnosticTestStateCopyWith<$Res> {
  _$DiagnosticTestStateCopyWithImpl(this._self, this._then);

  final DiagnosticTestState _self;
  final $Res Function(DiagnosticTestState) _then;

/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? questions = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? controllerScroll = freezed,}) {
  return _then(_self.copyWith(
questions: null == questions ? _self.questions : questions // ignore: cast_nullable_to_non_nullable
as List<QuestionModel>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,controllerScroll: freezed == controllerScroll ? _self.controllerScroll : controllerScroll // ignore: cast_nullable_to_non_nullable
as ScrollController?,
  ));
}
/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}


/// Adds pattern-matching-related methods to [DiagnosticTestState].
extension DiagnosticTestStatePatterns on DiagnosticTestState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DiagnosticTestState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DiagnosticTestState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DiagnosticTestState value)  $default,){
final _that = this;
switch (_that) {
case _DiagnosticTestState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DiagnosticTestState value)?  $default,){
final _that = this;
switch (_that) {
case _DiagnosticTestState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<QuestionModel> questions,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  ScrollController? controllerScroll)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DiagnosticTestState() when $default != null:
return $default(_that.questions,_that.result,_that.currentPage,_that.expandedResult,_that.controllerScroll);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<QuestionModel> questions,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  ScrollController? controllerScroll)  $default,) {final _that = this;
switch (_that) {
case _DiagnosticTestState():
return $default(_that.questions,_that.result,_that.currentPage,_that.expandedResult,_that.controllerScroll);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<QuestionModel> questions,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  ScrollController? controllerScroll)?  $default,) {final _that = this;
switch (_that) {
case _DiagnosticTestState() when $default != null:
return $default(_that.questions,_that.result,_that.currentPage,_that.expandedResult,_that.controllerScroll);case _:
  return null;

}
}

}

/// @nodoc


class _DiagnosticTestState extends DiagnosticTestState {
   _DiagnosticTestState({final  List<QuestionModel> questions = const [], this.result, this.currentPage = 0, this.expandedResult = false, this.controllerScroll}): _questions = questions,super._();
  

 final  List<QuestionModel> _questions;
@override@JsonKey() List<QuestionModel> get questions {
  if (_questions is EqualUnmodifiableListView) return _questions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_questions);
}

@override final  QuestionResultModel? result;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  bool expandedResult;
@override final  ScrollController? controllerScroll;

/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DiagnosticTestStateCopyWith<_DiagnosticTestState> get copyWith => __$DiagnosticTestStateCopyWithImpl<_DiagnosticTestState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DiagnosticTestState&&const DeepCollectionEquality().equals(other._questions, _questions)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.controllerScroll, controllerScroll) || other.controllerScroll == controllerScroll));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_questions),result,currentPage,expandedResult,controllerScroll);

@override
String toString() {
  return 'DiagnosticTestState(questions: $questions, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, controllerScroll: $controllerScroll)';
}


}

/// @nodoc
abstract mixin class _$DiagnosticTestStateCopyWith<$Res> implements $DiagnosticTestStateCopyWith<$Res> {
  factory _$DiagnosticTestStateCopyWith(_DiagnosticTestState value, $Res Function(_DiagnosticTestState) _then) = __$DiagnosticTestStateCopyWithImpl;
@override @useResult
$Res call({
 List<QuestionModel> questions, QuestionResultModel? result, int currentPage, bool expandedResult, ScrollController? controllerScroll
});


@override $QuestionResultModelCopyWith<$Res>? get result;

}
/// @nodoc
class __$DiagnosticTestStateCopyWithImpl<$Res>
    implements _$DiagnosticTestStateCopyWith<$Res> {
  __$DiagnosticTestStateCopyWithImpl(this._self, this._then);

  final _DiagnosticTestState _self;
  final $Res Function(_DiagnosticTestState) _then;

/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? questions = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? controllerScroll = freezed,}) {
  return _then(_DiagnosticTestState(
questions: null == questions ? _self._questions : questions // ignore: cast_nullable_to_non_nullable
as List<QuestionModel>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,controllerScroll: freezed == controllerScroll ? _self.controllerScroll : controllerScroll // ignore: cast_nullable_to_non_nullable
as ScrollController?,
  ));
}

/// Create a copy of DiagnosticTestState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

// dart format on
