import 'certificate_level.dart';

class Certificate {
  final String id;
  final String title;
  final String description;
  final DateTime dateIssued;
  final String certificateUrl; // Represents page 1
  final String? certificateUrlPage2; // Optional page 2
  final CertificateLevel level;

  // Additional fields from Firestore structure
  final CertificateScores? scores;
  final CertificatePredicates? predicates;

  Certificate({
    required this.id,
    required this.title,
    required this.description,
    required this.dateIssued,
    required this.certificateUrl,
    this.certificateUrlPage2,
    required this.level,
    this.scores,
    this.predicates,
  });
}

class CertificateScores {
  final double? pronunciation;
  final double? listening;
  final double? speaking;

  CertificateScores({this.pronunciation, this.listening, this.speaking});
}

class CertificatePredicates {
  final String? pronunciation;
  final String? listening;
  final String? speaking;

  CertificatePredicates({this.pronunciation, this.listening, this.speaking});
}
