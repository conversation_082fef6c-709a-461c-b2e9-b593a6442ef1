import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/questionnaire/domain/models/questionnaire_model.dart';
import 'package:selfeng/features/questionnaire/domain/providers/questionnaire_provider.dart';
import 'package:selfeng/features/questionnaire/domain/repositories/questionnaire_repository.dart';

import 'state/questionnaire_state.dart';

part 'questionnaire_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class QuestionnaireController extends _$QuestionnaireController {
  late QuestionnaireRepository questionnaireRepository;

  @override
  FutureOr<QuestionnaireState> build() {
    questionnaireRepository = ref.watch(questionnaireRepositoryProvider);
    return init();
  }

  FutureOr<QuestionnaireState> init() async {
    List<QuestionnaireModel> respond = [];
    state = const AsyncLoading();
    final result = await questionnaireRepository.getListQuestionnaire();
    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) {
        respond = data;
      },
    );
    return QuestionnaireState(
      questions: respond,
      controllerScroll: ScrollController(),
    );
  }

  Future<void> saveAnswer() async {
    final result = await questionnaireRepository.saveAnswer(
      state.value!.questions.map((item) => item.toJson()).toList(),
    );
    result.fold((failure) {
      state = AsyncError(failure.message, StackTrace.current);
    }, (data) {});
  }

  void answer(index, value) {
    List<QuestionnaireModel> temp = List.from(state.value!.questions);
    if (temp[index].multipleAnswer) {
      if (temp[index].answer == null) {
        temp[index] = temp[index].copyWith(answer: []);
      }
      List<String> tempAnswer = List.from(temp[index].answer);
      if (tempAnswer.contains(value)) {
        tempAnswer.remove(value);
      } else {
        tempAnswer.add(value);
      }
      temp[index] = temp[index].copyWith(answer: tempAnswer);
    } else {
      temp[index] = temp[index].copyWith(answer: value);
    }
    state = AsyncData(state.value!.copyWith(questions: temp));
  }

  Future nextPage(context, double width) async {
    if (state.value!.currentQuestion < state.value!.questionLength) {
      state = AsyncData(
        state.value!.copyWith(
          currentQuestion: state.value!.currentQuestion + 1,
        ),
      );
      state.value?.controllerScroll?.animateTo(
        (state.value?.currentQuestion ?? 0) * (width - 32),
        duration: const Duration(seconds: 1),
        curve: Curves.ease,
      );
    }
  }

  Future backPage(double width) async {
    if (state.value!.currentQuestion != 0) {
      state = AsyncData(
        state.value!.copyWith(
          currentQuestion: state.value!.currentQuestion - 1,
        ),
      );
    }
    state.value?.controllerScroll?.animateTo(
      (state.value?.currentQuestion ?? 0) * (width - 32),
      duration: const Duration(seconds: 1),
      curve: Curves.ease,
    );
  }
}
