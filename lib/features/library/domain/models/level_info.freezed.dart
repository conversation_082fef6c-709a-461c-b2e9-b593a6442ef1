// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'level_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LevelInfo {

 Level get level; String get image; LevelLocalizeContent get title; LevelLocalizeContent get description;
/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LevelInfoCopyWith<LevelInfo> get copyWith => _$LevelInfoCopyWithImpl<LevelInfo>(this as LevelInfo, _$identity);

  /// Serializes this LevelInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LevelInfo&&(identical(other.level, level) || other.level == level)&&(identical(other.image, image) || other.image == image)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,level,image,title,description);

@override
String toString() {
  return 'LevelInfo(level: $level, image: $image, title: $title, description: $description)';
}


}

/// @nodoc
abstract mixin class $LevelInfoCopyWith<$Res>  {
  factory $LevelInfoCopyWith(LevelInfo value, $Res Function(LevelInfo) _then) = _$LevelInfoCopyWithImpl;
@useResult
$Res call({
 Level level, String image, LevelLocalizeContent title, LevelLocalizeContent description
});


$LevelLocalizeContentCopyWith<$Res> get title;$LevelLocalizeContentCopyWith<$Res> get description;

}
/// @nodoc
class _$LevelInfoCopyWithImpl<$Res>
    implements $LevelInfoCopyWith<$Res> {
  _$LevelInfoCopyWithImpl(this._self, this._then);

  final LevelInfo _self;
  final $Res Function(LevelInfo) _then;

/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? level = null,Object? image = null,Object? title = null,Object? description = null,}) {
  return _then(_self.copyWith(
level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as Level,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as LevelLocalizeContent,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as LevelLocalizeContent,
  ));
}
/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelLocalizeContentCopyWith<$Res> get title {
  
  return $LevelLocalizeContentCopyWith<$Res>(_self.title, (value) {
    return _then(_self.copyWith(title: value));
  });
}/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelLocalizeContentCopyWith<$Res> get description {
  
  return $LevelLocalizeContentCopyWith<$Res>(_self.description, (value) {
    return _then(_self.copyWith(description: value));
  });
}
}


/// Adds pattern-matching-related methods to [LevelInfo].
extension LevelInfoPatterns on LevelInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LevelInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LevelInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LevelInfo value)  $default,){
final _that = this;
switch (_that) {
case _LevelInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LevelInfo value)?  $default,){
final _that = this;
switch (_that) {
case _LevelInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Level level,  String image,  LevelLocalizeContent title,  LevelLocalizeContent description)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LevelInfo() when $default != null:
return $default(_that.level,_that.image,_that.title,_that.description);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Level level,  String image,  LevelLocalizeContent title,  LevelLocalizeContent description)  $default,) {final _that = this;
switch (_that) {
case _LevelInfo():
return $default(_that.level,_that.image,_that.title,_that.description);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Level level,  String image,  LevelLocalizeContent title,  LevelLocalizeContent description)?  $default,) {final _that = this;
switch (_that) {
case _LevelInfo() when $default != null:
return $default(_that.level,_that.image,_that.title,_that.description);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LevelInfo implements LevelInfo {
   _LevelInfo({required this.level, required this.image, required this.title, required this.description});
  factory _LevelInfo.fromJson(Map<String, dynamic> json) => _$LevelInfoFromJson(json);

@override final  Level level;
@override final  String image;
@override final  LevelLocalizeContent title;
@override final  LevelLocalizeContent description;

/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LevelInfoCopyWith<_LevelInfo> get copyWith => __$LevelInfoCopyWithImpl<_LevelInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LevelInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LevelInfo&&(identical(other.level, level) || other.level == level)&&(identical(other.image, image) || other.image == image)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,level,image,title,description);

@override
String toString() {
  return 'LevelInfo(level: $level, image: $image, title: $title, description: $description)';
}


}

/// @nodoc
abstract mixin class _$LevelInfoCopyWith<$Res> implements $LevelInfoCopyWith<$Res> {
  factory _$LevelInfoCopyWith(_LevelInfo value, $Res Function(_LevelInfo) _then) = __$LevelInfoCopyWithImpl;
@override @useResult
$Res call({
 Level level, String image, LevelLocalizeContent title, LevelLocalizeContent description
});


@override $LevelLocalizeContentCopyWith<$Res> get title;@override $LevelLocalizeContentCopyWith<$Res> get description;

}
/// @nodoc
class __$LevelInfoCopyWithImpl<$Res>
    implements _$LevelInfoCopyWith<$Res> {
  __$LevelInfoCopyWithImpl(this._self, this._then);

  final _LevelInfo _self;
  final $Res Function(_LevelInfo) _then;

/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? level = null,Object? image = null,Object? title = null,Object? description = null,}) {
  return _then(_LevelInfo(
level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as Level,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as LevelLocalizeContent,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as LevelLocalizeContent,
  ));
}

/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelLocalizeContentCopyWith<$Res> get title {
  
  return $LevelLocalizeContentCopyWith<$Res>(_self.title, (value) {
    return _then(_self.copyWith(title: value));
  });
}/// Create a copy of LevelInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelLocalizeContentCopyWith<$Res> get description {
  
  return $LevelLocalizeContentCopyWith<$Res>(_self.description, (value) {
    return _then(_self.copyWith(description: value));
  });
}
}


/// @nodoc
mixin _$LevelLocalizeContent {

 String get en; String get id;
/// Create a copy of LevelLocalizeContent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LevelLocalizeContentCopyWith<LevelLocalizeContent> get copyWith => _$LevelLocalizeContentCopyWithImpl<LevelLocalizeContent>(this as LevelLocalizeContent, _$identity);

  /// Serializes this LevelLocalizeContent to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LevelLocalizeContent&&(identical(other.en, en) || other.en == en)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,en,id);

@override
String toString() {
  return 'LevelLocalizeContent(en: $en, id: $id)';
}


}

/// @nodoc
abstract mixin class $LevelLocalizeContentCopyWith<$Res>  {
  factory $LevelLocalizeContentCopyWith(LevelLocalizeContent value, $Res Function(LevelLocalizeContent) _then) = _$LevelLocalizeContentCopyWithImpl;
@useResult
$Res call({
 String en, String id
});




}
/// @nodoc
class _$LevelLocalizeContentCopyWithImpl<$Res>
    implements $LevelLocalizeContentCopyWith<$Res> {
  _$LevelLocalizeContentCopyWithImpl(this._self, this._then);

  final LevelLocalizeContent _self;
  final $Res Function(LevelLocalizeContent) _then;

/// Create a copy of LevelLocalizeContent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? en = null,Object? id = null,}) {
  return _then(_self.copyWith(
en: null == en ? _self.en : en // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [LevelLocalizeContent].
extension LevelLocalizeContentPatterns on LevelLocalizeContent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LevelLocalizeContent value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LevelLocalizeContent() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LevelLocalizeContent value)  $default,){
final _that = this;
switch (_that) {
case _LevelLocalizeContent():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LevelLocalizeContent value)?  $default,){
final _that = this;
switch (_that) {
case _LevelLocalizeContent() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String en,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LevelLocalizeContent() when $default != null:
return $default(_that.en,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String en,  String id)  $default,) {final _that = this;
switch (_that) {
case _LevelLocalizeContent():
return $default(_that.en,_that.id);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String en,  String id)?  $default,) {final _that = this;
switch (_that) {
case _LevelLocalizeContent() when $default != null:
return $default(_that.en,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LevelLocalizeContent extends LevelLocalizeContent {
   _LevelLocalizeContent({required this.en, required this.id}): super._();
  factory _LevelLocalizeContent.fromJson(Map<String, dynamic> json) => _$LevelLocalizeContentFromJson(json);

@override final  String en;
@override final  String id;

/// Create a copy of LevelLocalizeContent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LevelLocalizeContentCopyWith<_LevelLocalizeContent> get copyWith => __$LevelLocalizeContentCopyWithImpl<_LevelLocalizeContent>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LevelLocalizeContentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LevelLocalizeContent&&(identical(other.en, en) || other.en == en)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,en,id);

@override
String toString() {
  return 'LevelLocalizeContent(en: $en, id: $id)';
}


}

/// @nodoc
abstract mixin class _$LevelLocalizeContentCopyWith<$Res> implements $LevelLocalizeContentCopyWith<$Res> {
  factory _$LevelLocalizeContentCopyWith(_LevelLocalizeContent value, $Res Function(_LevelLocalizeContent) _then) = __$LevelLocalizeContentCopyWithImpl;
@override @useResult
$Res call({
 String en, String id
});




}
/// @nodoc
class __$LevelLocalizeContentCopyWithImpl<$Res>
    implements _$LevelLocalizeContentCopyWith<$Res> {
  __$LevelLocalizeContentCopyWithImpl(this._self, this._then);

  final _LevelLocalizeContent _self;
  final $Res Function(_LevelLocalizeContent) _then;

/// Create a copy of LevelLocalizeContent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? en = null,Object? id = null,}) {
  return _then(_LevelLocalizeContent(
en: null == en ? _self.en : en // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
