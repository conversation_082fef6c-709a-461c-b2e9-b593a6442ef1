// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'diagnostic_test_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$diagnosticTestControllerHash() =>
    r'6e1ad43f8c086050d91a17e65e9273b8186fe7b4';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [DiagnosticTestController].
@ProviderFor(DiagnosticTestController)
final diagnosticTestControllerProvider = AutoDisposeAsyncNotifierProvider<
  DiagnosticTestController,
  DiagnosticTestState
>.internal(
  DiagnosticTestController.new,
  name: r'diagnosticTestControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$diagnosticTestControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DiagnosticTestController =
    AutoDisposeAsyncNotifier<DiagnosticTestState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
