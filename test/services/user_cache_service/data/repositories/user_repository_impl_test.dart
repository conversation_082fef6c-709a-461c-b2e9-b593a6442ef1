import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/user_cache_service/data/datasource/user_local_datasource.dart';
import 'package:selfeng/services/user_cache_service/data/repositories/user_repository_impl.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

// Mock classes
class MockUserDataSource extends Mock implements UserDataSource {}

void main() {
  group('UserRepositoryImpl', () {
    late UserRepositoryImpl repository;
    late MockUserDataSource mockDataSource;

    setUp(() {
      mockDataSource = MockUserDataSource();
      repository = UserRepositoryImpl(mockDataSource);
    });

    group('fetchUser', () {
      test('should fetch user from data source', () async {
        // Arrange
        final user = User(
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        );
        when(
          () => mockDataSource.fetchUser(),
        ).thenAnswer((_) async => Right(user));

        // Act
        final result = await repository.fetchUser();

        // Assert
        expect(result, isA<Right<AppException, User>>());
        expect((result as Right<AppException, User>).value, equals(user));
        verify(() => mockDataSource.fetchUser()).called(1);
      });

      test(
        'should return Left with AppException when fetching user fails',
        () async {
          // Arrange
          final exception = AppException(
            identifier: 'UserLocalDatasource',
            statusCode: 404,
            message: 'User not found',
          );
          when(
            () => mockDataSource.fetchUser(),
          ).thenAnswer((_) async => Left(exception));

          // Act
          final result = await repository.fetchUser();

          // Assert
          expect(result, isA<Left<AppException, User>>());
          expect((result as Left<AppException, User>).value, equals(exception));
          verify(() => mockDataSource.fetchUser()).called(1);
        },
      );
    });

    group('saveUser', () {
      test('should save user to data source', () async {
        // Arrange
        final user = User(
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        );
        when(
          () => mockDataSource.saveUser(user: user),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.saveUser(user: user);

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.saveUser(user: user)).called(1);
      });

      test('should return false when saving user fails', () async {
        // Arrange
        final user = User(
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        );
        when(
          () => mockDataSource.saveUser(user: user),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.saveUser(user: user);

        // Assert
        expect(result, isFalse);
        verify(() => mockDataSource.saveUser(user: user)).called(1);
      });
    });

    group('deleteUser', () {
      test('should delete user from data source', () async {
        // Arrange
        when(() => mockDataSource.deleteUser()).thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteUser();

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.deleteUser()).called(1);
      });

      test('should return false when deleting user fails', () async {
        // Arrange
        when(() => mockDataSource.deleteUser()).thenAnswer((_) async => false);

        // Act
        final result = await repository.deleteUser();

        // Assert
        expect(result, isFalse);
        verify(() => mockDataSource.deleteUser()).called(1);
      });
    });

    group('hasUser', () {
      test('should check if user exists in data source', () async {
        // Arrange
        when(() => mockDataSource.hasUser()).thenAnswer((_) async => true);

        // Act
        final result = await repository.hasUser();

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.hasUser()).called(1);
      });

      test(
        'should return false when user does not exist in data source',
        () async {
          // Arrange
          when(() => mockDataSource.hasUser()).thenAnswer((_) async => false);

          // Act
          final result = await repository.hasUser();

          // Assert
          expect(result, isFalse);
          verify(() => mockDataSource.hasUser()).called(1);
        },
      );
    });
  });
}
