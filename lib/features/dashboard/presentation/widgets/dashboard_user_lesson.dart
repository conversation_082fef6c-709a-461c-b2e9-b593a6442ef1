import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/dashboard_controller.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/assessment_card.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/continue_lesson_section.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/get_started_section.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class DashboardUserLesson extends ConsumerStatefulWidget {
  const DashboardUserLesson({super.key});

  @override
  ConsumerState<DashboardUserLesson> createState() =>
      _DashboardUserLessonState();
}

class _DashboardUserLessonState extends ConsumerState<DashboardUserLesson> {
  late final dashboardController = ref.read(
    dashboardControllerProvider.notifier,
  );

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardControllerProvider);

    return dashboardState.when(
      data: (state) {
        final bool showAssessment = !state.afterTest;
        final bool hasCourse = state.lastCourse.isNotEmpty;

        return Column(
          children: [
            if (showAssessment) const AssessmentCard(),
            if (hasCourse)
              ContinueLessonsSection(
                viewState: AsyncValue.data(state),
                viewModel: dashboardController,
              )
            else
              const GetStartedSection(),
          ],
        );
      },
      loading: () => const LoadingCircle(),
      error:
          (error, stackTrace) => Center(
            child: Text(
              error.toString(),
              style: const TextStyle(color: Colors.red),
            ),
          ),
    );
  }
}
