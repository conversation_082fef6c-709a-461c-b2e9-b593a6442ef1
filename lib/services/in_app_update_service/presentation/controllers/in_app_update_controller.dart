import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:selfeng/services/in_app_update_service/domain/providers/in_app_update_service_provider.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart';

/// State for in-app update
class InAppUpdateState {
  final bool isLoading;
  final bool isUpdateAvailable;
  final bool isUpdateDownloaded;
  final bool isHighPriority;
  final AppUpdateInfo? updateInfo;
  final String? error;

  const InAppUpdateState({
    this.isLoading = false,
    this.isUpdateAvailable = false,
    this.isUpdateDownloaded = false,
    this.isHighPriority = false,
    this.updateInfo,
    this.error,
  });

  InAppUpdateState copyWith({
    bool? isLoading,
    bool? isUpdateAvailable,
    bool? isUpdateDownloaded,
    bool? isHighPriority,
    AppUpdateInfo? updateInfo,
    String? error,
  }) {
    return InAppUpdateState(
      isLoading: isLoading ?? this.isLoading,
      isUpdateAvailable: isUpdateAvailable ?? this.isUpdateAvailable,
      isUpdateDownloaded: isUpdateDownloaded ?? this.isUpdateDownloaded,
      isHighPriority: isHighPriority ?? this.isHighPriority,
      updateInfo: updateInfo ?? this.updateInfo,
      error: error ?? this.error,
    );
  }
}

/// Controller for managing in-app updates
class InAppUpdateController extends StateNotifier<InAppUpdateState> {
  final InAppUpdateServiceRepository _updateService;

  InAppUpdateController(this._updateService) : super(const InAppUpdateState());

  /// Initialize the update service and check for updates
  Future<void> initialize() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Initialize the service
      final initResult = await _updateService.initialize();
      if (initResult.isLeft()) {
        final error = initResult.fold(
          (error) => error,
          (_) => throw StateError('Should not reach here'),
        );
        state = state.copyWith(isLoading: false, error: error.message);
        return;
      }

      // Check for updates
      await checkForUpdates();
    } catch (e) {
      debugPrint('❌ Failed to initialize update controller: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize update service: $e',
      );
    }
  }

  /// Check for available updates
  Future<void> checkForUpdates() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.checkForUpdate();

      if (result.isLeft()) {
        final error = result.fold(
          (error) => error,
          (_) => throw StateError('Should not reach here'),
        );
        state = state.copyWith(isLoading: false, error: error.message);
        return;
      }

      final updateInfo = result.fold(
        (_) => throw StateError('Should not reach here'),
        (info) => info,
      );
      final isUpdateAvailable =
          updateInfo?.updateAvailability == UpdateAvailability.updateAvailable;

      // Check if flexible update is downloaded
      bool isDownloaded = false;
      if (isUpdateAvailable) {
        final downloadResult =
            await _updateService.isFlexibleUpdateDownloaded();
        if (downloadResult.isRight()) {
          isDownloaded = downloadResult.fold(
            (_) => false,
            (downloaded) => downloaded,
          );
        }
      }

      state = state.copyWith(
        isLoading: false,
        isUpdateAvailable: isUpdateAvailable,
        isUpdateDownloaded: isDownloaded,
        isHighPriority: _updateService.isHighPriorityUpdate,
        updateInfo: updateInfo,
      );
    } catch (e) {
      debugPrint('❌ Failed to check for updates: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to check for updates: $e',
      );
    }
  }

  /// Start an immediate update (for critical updates)
  Future<void> startImmediateUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.startImmediateUpdate();

      if (result.isLeft()) {
        final error = result.fold(
          (error) => error,
          (_) => throw StateError('Should not reach here'),
        );
        state = state.copyWith(isLoading: false, error: error.message);
        return;
      }

      // Immediate update started successfully
      // The app will be blocked until update completes
      debugPrint('✅ Immediate update started');
    } catch (e) {
      debugPrint('❌ Failed to start immediate update: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start immediate update: $e',
      );
    }
  }

  /// Start a flexible update (downloads in background)
  Future<void> startFlexibleUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.startFlexibleUpdate();

      if (result.isLeft()) {
        final error = result.fold(
          (error) => error,
          (_) => throw StateError('Should not reach here'),
        );
        state = state.copyWith(isLoading: false, error: error.message);
        return;
      }

      // Flexible update started successfully
      debugPrint('✅ Flexible update started');
      state = state.copyWith(isLoading: false);

      // Start monitoring the download progress
      _monitorFlexibleUpdate();
    } catch (e) {
      debugPrint('❌ Failed to start flexible update: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start flexible update: $e',
      );
    }
  }

  /// Complete a flexible update (install the downloaded update)
  Future<void> completeFlexibleUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.completeFlexibleUpdate();

      if (result.isLeft()) {
        final error = result.fold(
          (error) => error,
          (_) => throw StateError('Should not reach here'),
        );
        state = state.copyWith(isLoading: false, error: error.message);
        return;
      }

      // Update completed successfully
      debugPrint('✅ Flexible update completed');
    } catch (e) {
      debugPrint('❌ Failed to complete flexible update: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to complete flexible update: $e',
      );
    }
  }

  /// Monitor flexible update progress
  void _monitorFlexibleUpdate() {
    // Check every 5 seconds if the flexible update is downloaded
    Future.delayed(const Duration(seconds: 5), () async {
      if (!mounted) return;

      final result = await _updateService.isFlexibleUpdateDownloaded();
      if (result.isRight()) {
        final isDownloaded = result.fold(
          (_) => false,
          (downloaded) => downloaded,
        );
        if (isDownloaded) {
          state = state.copyWith(isUpdateDownloaded: true);
          debugPrint('📦 Flexible update downloaded and ready to install');
        } else if (state.isUpdateAvailable && !state.isUpdateDownloaded) {
          // Continue monitoring if update is still in progress
          _monitorFlexibleUpdate();
        }
      } else if (state.isUpdateAvailable && !state.isUpdateDownloaded) {
        // Continue monitoring if update is still in progress
        _monitorFlexibleUpdate();
      }
    });
  }

  /// Clear any error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get update type as a human-readable string
  String get updatePriorityText {
    return _updateService.isCriticalUpdate ? 'Critical' : 'Normal';
  }

  /// Check if the current update is critical
  bool get isCriticalUpdate => _updateService.isCriticalUpdate;

  /// Check if the current update is normal
  bool get isNormalUpdate => _updateService.isNormalUpdate;

  /// Check if immediate update should be forced (for critical updates)
  bool get shouldForceImmediateUpdate {
    return state.isUpdateAvailable &&
        isCriticalUpdate &&
        _updateService.isImmediateUpdateAllowed;
  }
}

/// Provider for InAppUpdateController
final inAppUpdateControllerProvider =
    StateNotifierProvider<InAppUpdateController, InAppUpdateState>((ref) {
      final updateService = ref.watch(inAppUpdateServiceProvider);
      return InAppUpdateController(updateService);
    });
