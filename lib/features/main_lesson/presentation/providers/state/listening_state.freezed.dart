// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listening_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ListeningState {

 List<ListeningPart> get listenings; int get currentPage; bool get expandedResult; AudioPath? get audioPath; Map<String, dynamic>? get response; int? get expandedQuestionIndex;// Changed from selectedIndex, default to null
 bool get nextSection; bool get isNewPart; bool get isIntro;
/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListeningStateCopyWith<ListeningState> get copyWith => _$ListeningStateCopyWithImpl<ListeningState>(this as ListeningState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListeningState&&const DeepCollectionEquality().equals(other.listenings, listenings)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&const DeepCollectionEquality().equals(other.response, response)&&(identical(other.expandedQuestionIndex, expandedQuestionIndex) || other.expandedQuestionIndex == expandedQuestionIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isNewPart, isNewPart) || other.isNewPart == isNewPart)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(listenings),currentPage,expandedResult,audioPath,const DeepCollectionEquality().hash(response),expandedQuestionIndex,nextSection,isNewPart,isIntro);

@override
String toString() {
  return 'ListeningState(listenings: $listenings, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, expandedQuestionIndex: $expandedQuestionIndex, nextSection: $nextSection, isNewPart: $isNewPart, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class $ListeningStateCopyWith<$Res>  {
  factory $ListeningStateCopyWith(ListeningState value, $Res Function(ListeningState) _then) = _$ListeningStateCopyWithImpl;
@useResult
$Res call({
 List<ListeningPart> listenings, int currentPage, bool expandedResult, AudioPath? audioPath, Map<String, dynamic>? response, int? expandedQuestionIndex, bool nextSection, bool isNewPart, bool isIntro
});


$AudioPathCopyWith<$Res>? get audioPath;

}
/// @nodoc
class _$ListeningStateCopyWithImpl<$Res>
    implements $ListeningStateCopyWith<$Res> {
  _$ListeningStateCopyWithImpl(this._self, this._then);

  final ListeningState _self;
  final $Res Function(ListeningState) _then;

/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? listenings = null,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? expandedQuestionIndex = freezed,Object? nextSection = null,Object? isNewPart = null,Object? isIntro = null,}) {
  return _then(_self.copyWith(
listenings: null == listenings ? _self.listenings : listenings // ignore: cast_nullable_to_non_nullable
as List<ListeningPart>,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,expandedQuestionIndex: freezed == expandedQuestionIndex ? _self.expandedQuestionIndex : expandedQuestionIndex // ignore: cast_nullable_to_non_nullable
as int?,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isNewPart: null == isNewPart ? _self.isNewPart : isNewPart // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}
}


/// Adds pattern-matching-related methods to [ListeningState].
extension ListeningStatePatterns on ListeningState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListeningState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListeningState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListeningState value)  $default,){
final _that = this;
switch (_that) {
case _ListeningState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListeningState value)?  $default,){
final _that = this;
switch (_that) {
case _ListeningState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ListeningPart> listenings,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  int? expandedQuestionIndex,  bool nextSection,  bool isNewPart,  bool isIntro)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListeningState() when $default != null:
return $default(_that.listenings,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.expandedQuestionIndex,_that.nextSection,_that.isNewPart,_that.isIntro);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ListeningPart> listenings,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  int? expandedQuestionIndex,  bool nextSection,  bool isNewPart,  bool isIntro)  $default,) {final _that = this;
switch (_that) {
case _ListeningState():
return $default(_that.listenings,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.expandedQuestionIndex,_that.nextSection,_that.isNewPart,_that.isIntro);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ListeningPart> listenings,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  int? expandedQuestionIndex,  bool nextSection,  bool isNewPart,  bool isIntro)?  $default,) {final _that = this;
switch (_that) {
case _ListeningState() when $default != null:
return $default(_that.listenings,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.expandedQuestionIndex,_that.nextSection,_that.isNewPart,_that.isIntro);case _:
  return null;

}
}

}

/// @nodoc


class _ListeningState extends ListeningState {
   _ListeningState({final  List<ListeningPart> listenings = const [], this.currentPage = 0, this.expandedResult = false, this.audioPath, final  Map<String, dynamic>? response, this.expandedQuestionIndex = null, this.nextSection = false, this.isNewPart = true, this.isIntro = false}): _listenings = listenings,_response = response,super._();
  

 final  List<ListeningPart> _listenings;
@override@JsonKey() List<ListeningPart> get listenings {
  if (_listenings is EqualUnmodifiableListView) return _listenings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_listenings);
}

@override@JsonKey() final  int currentPage;
@override@JsonKey() final  bool expandedResult;
@override final  AudioPath? audioPath;
 final  Map<String, dynamic>? _response;
@override Map<String, dynamic>? get response {
  final value = _response;
  if (value == null) return null;
  if (_response is EqualUnmodifiableMapView) return _response;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey() final  int? expandedQuestionIndex;
// Changed from selectedIndex, default to null
@override@JsonKey() final  bool nextSection;
@override@JsonKey() final  bool isNewPart;
@override@JsonKey() final  bool isIntro;

/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListeningStateCopyWith<_ListeningState> get copyWith => __$ListeningStateCopyWithImpl<_ListeningState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListeningState&&const DeepCollectionEquality().equals(other._listenings, _listenings)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&const DeepCollectionEquality().equals(other._response, _response)&&(identical(other.expandedQuestionIndex, expandedQuestionIndex) || other.expandedQuestionIndex == expandedQuestionIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isNewPart, isNewPart) || other.isNewPart == isNewPart)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_listenings),currentPage,expandedResult,audioPath,const DeepCollectionEquality().hash(_response),expandedQuestionIndex,nextSection,isNewPart,isIntro);

@override
String toString() {
  return 'ListeningState(listenings: $listenings, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, expandedQuestionIndex: $expandedQuestionIndex, nextSection: $nextSection, isNewPart: $isNewPart, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class _$ListeningStateCopyWith<$Res> implements $ListeningStateCopyWith<$Res> {
  factory _$ListeningStateCopyWith(_ListeningState value, $Res Function(_ListeningState) _then) = __$ListeningStateCopyWithImpl;
@override @useResult
$Res call({
 List<ListeningPart> listenings, int currentPage, bool expandedResult, AudioPath? audioPath, Map<String, dynamic>? response, int? expandedQuestionIndex, bool nextSection, bool isNewPart, bool isIntro
});


@override $AudioPathCopyWith<$Res>? get audioPath;

}
/// @nodoc
class __$ListeningStateCopyWithImpl<$Res>
    implements _$ListeningStateCopyWith<$Res> {
  __$ListeningStateCopyWithImpl(this._self, this._then);

  final _ListeningState _self;
  final $Res Function(_ListeningState) _then;

/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? listenings = null,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? expandedQuestionIndex = freezed,Object? nextSection = null,Object? isNewPart = null,Object? isIntro = null,}) {
  return _then(_ListeningState(
listenings: null == listenings ? _self._listenings : listenings // ignore: cast_nullable_to_non_nullable
as List<ListeningPart>,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self._response : response // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,expandedQuestionIndex: freezed == expandedQuestionIndex ? _self.expandedQuestionIndex : expandedQuestionIndex // ignore: cast_nullable_to_non_nullable
as int?,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isNewPart: null == isNewPart ? _self.isNewPart : isNewPart // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ListeningState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}
}

// dart format on
