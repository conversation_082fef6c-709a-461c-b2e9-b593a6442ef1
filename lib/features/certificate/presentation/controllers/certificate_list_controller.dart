import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import '../../domain/entities/certificate.dart';
import '../../domain/entities/certificate_level.dart';
import '../../domain/repositories/certificate_repository.dart';

class CertificateListController with ChangeNotifier {
  final CertificateRepository _certificateRepository;

  CertificateListController(this._certificateRepository) {
    fetchCertificates();
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String? _error;
  String? get error => _error;

  Map<CertificateLevel, List<Certificate>> _groupedCertificates = {};
  Map<CertificateLevel, List<Certificate>> get groupedCertificates =>
      _groupedCertificates;

  Future<void> fetchCertificates() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _certificateRepository.getCertificates();
      result.fold(
        (error) {
          _error = error.message;
        },
        (certificates) {
          _groupAndSortCertificates(certificates);
        },
      );
    } catch (e) {
      _error = "Failed to load certificates. Please try again.";
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _groupAndSortCertificates(List<Certificate> certificates) {
    // Group certificates by their level's ID to handle potential object inequality.
    final groupedByLevelId = groupBy(certificates, (cert) => cert.level.id);

    final Map<CertificateLevel, List<Certificate>> tempGroupedCerts = {};

    // Process each group
    groupedByLevelId.forEach((levelId, certs) {
      if (certs.isNotEmpty) {
        // All certificates in this group have the same level, so we can take the level from the first certificate.
        final level = certs.first.level;

        // Sort the certificates in this group by date, newest first.
        certs.sort((a, b) => b.dateIssued.compareTo(a.dateIssued));

        tempGroupedCerts[level] = certs;
      }
    });

    _groupedCertificates = tempGroupedCerts;
  }
}
