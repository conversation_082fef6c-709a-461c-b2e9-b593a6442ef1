import 'package:flutter/material.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/theme/text_styles.dart';

class AuthLoadingDialog extends StatelessWidget {
  const AuthLoadingDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent dismissing the dialog by back button
      child: Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                color: Color(0xffEB1A19),
                strokeWidth: 3,
              ),
              const SizedBox(height: 20),
              Text(
                context.loc.signingIn,
                style: AppTextStyles.addOnsBodySemiBold.copyWith(
                  color: const Color(0xff333333),
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                context.loc.pleaseWait,
                style: AppTextStyles.addOnsBody.copyWith(
                  color: const Color(0xff666666),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show the loading dialog
  static Future<void> show(BuildContext context) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AuthLoadingDialog(),
    );
  }

  /// Hide the loading dialog
  static void hide(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }
}
