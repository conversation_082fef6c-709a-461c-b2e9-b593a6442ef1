// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

class SelectAnswer extends StatelessWidget {
  const SelectAnswer({
    super.key,
    required this.title,
    required this.number,
    required this.value,
    required this.groupValue,
    required this.onTap,
  });
  final String title;
  final String number;
  final value;
  final groupValue;
  final Function onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(value),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 9, horizontal: 24),
        decoration: BoxDecoration(
          color: groupValue == value ? const Color(0xffFFB3AC) : null,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: const Color(0xffE82329), width: 0.4),
        ),
        child: Row(
          children: [
            Text('$number.', style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(width: 8),
            SizedBox(
              width: MediaQuery.of(context).size.width - 160,
              child: Text(title, style: Theme.of(context).textTheme.bodyLarge),
            ),
          ],
        ),
      ),
    );
  }
}
