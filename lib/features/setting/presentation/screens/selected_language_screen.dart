import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/features/setting/presentation/providers/state/setting_state.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class SelectedLanguageScreen extends ConsumerStatefulWidget {
  const SelectedLanguageScreen({super.key});

  @override
  ConsumerState<SelectedLanguageScreen> createState() =>
      _SelectedLanguageScreenState();
}

class _SelectedLanguageScreenState extends ConsumerState<SelectedLanguageScreen>
    with TickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);

    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      ref.read(settingControllerProvider.notifier).nextPage(context);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final viewState = ref.watch(settingControllerProvider);

    return Scaffold(
      body: FadeTransition(
        opacity: _animation,
        child: Stack(
          children: [
            // _buildBackground(),
            _buildContent(context, viewState),
            _buildBottomImage(viewState),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    AsyncValue<SettingState> viewState,
  ) {
    if (!viewState.hasValue) return const SizedBox.shrink();

    final selectedLanguage =
        viewState.value!.languageList[viewState.value!.selectedIndex];

    return Column(
      children: [
        const SizedBox(height: 186),
        Center(
          child: Image.asset(
            selectedLanguage.icon,
            fit: BoxFit.fitWidth,
            width: 130,
            height: 130,
          ),
        ),
        const SizedBox(height: 18),
        Center(
          child: Text(
            '${context.loc.selectedLanguageDesc1}\n${selectedLanguage.title}.',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomImage(AsyncValue<SettingState> viewState) {
    if (!viewState.hasValue) return const SizedBox.shrink();

    return Align(
      alignment: FractionalOffset.bottomCenter,
      child: Image.asset(
        viewState
            .value!
            .languageList[viewState.value!.selectedIndex]
            .imageBackground,
        fit: BoxFit.fitWidth,
        width: double.infinity,
      ),
    );
  }
}
