// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

class SelectLanguage extends StatelessWidget {
  const SelectLanguage({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    required this.groupValue,
    required this.onTap,
  });
  final String title;
  final String icon;
  final value;
  final groupValue;
  final Function onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(value),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 9, horizontal: 12),
        decoration: BoxDecoration(
          color: groupValue != value ? null : const Color(0xffFEB5A3),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: const Color(0xffE82329), width: 0.4),
        ),
        child: Row(
          children: [
            Image.asset(icon, fit: BoxFit.fitWidth, width: 36, height: 36),
            const SizedBox(width: 8),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}
