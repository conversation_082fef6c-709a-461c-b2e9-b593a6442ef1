import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class DiagnosticTestRepository {
  Future<Either<AppException, List<QuestionModel>>> getListDiagnosticTest();

  Future<Either<AppException, dynamic>> saveAnswer(
    List<Choice> answer,
    int resultCount,
  );
}

class DiagnosticTestRepositoryImpl extends DiagnosticTestRepository {
  // final FirebaseFirestore dataSource;
  final FirestoreServiceRepository dataSource;

  DiagnosticTestRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppException, List<QuestionModel>>>
  getListDiagnosticTest() async {
    try {
      final QuerySnapshot querySnapshot =
          await dataSource.fireStore
              .collection('new-diagnostic-test')
              .where('is_active', isEqualTo: true)
              .orderBy('order')
              .get();

      final List<Map<String, dynamic>> mapTemp =
          querySnapshot.docs.map((doc) {
            final data = doc.data()! as Map<String, dynamic>;
            data['questionId'] = doc.reference.path;
            return data;
          }).toList();

      return Right(
        mapTemp.map((json) => QuestionModel.fromJson(json)).toList(),
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch lead statistic',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, dynamic>> saveAnswer(
    List<Choice> answer,
    int resultCount,
  ) async {
    late Map<String, dynamic> mapTemp;
    int score = resultCount;
    try {
      final result =
          await dataSource.fireStore
              .collection('diagnostic-test-result-category')
              .where('min_point', isLessThanOrEqualTo: score)
              .where('max_point', isGreaterThanOrEqualTo: score)
              .get();

      /**
       * Change user status after test
       */
      await dataSource.dataUser().update({'is_after_test': true});

      await dataSource
          .dataUser()
          .collection('testResult')
          .doc('diagnosticTest')
          .set({
            'count_correct_answer': resultCount,
            'score': score,
            'result_coteggory_id': result.docs.first.reference.path,
          });

      for (var item in answer) {
        await dataSource
            .dataUser()
            .collection('testResult')
            .doc('diagnosticTest')
            .collection('answers')
            .add(item.toJson());
      }

      mapTemp = result.docs.first.data();
      return Right(QuestionResultModel.fromJson(mapTemp));
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch lead statistic',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
