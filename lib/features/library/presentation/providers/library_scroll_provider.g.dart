// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_scroll_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$libraryScrollPositionHash() =>
    r'b36aec1a572d8be3303be17003586c913d871e0c';

/// Provider that manages the scroll position for the Library screen.
///
/// This provider persists the scroll position across navigation,
/// allowing users to return to the same position in the library list
/// after navigating to other screens.
///
/// Copied from [LibraryScrollPosition].
@ProviderFor(LibraryScrollPosition)
final libraryScrollPositionProvider =
    NotifierProvider<LibraryScrollPosition, double>.internal(
      LibraryScrollPosition.new,
      name: r'libraryScrollPositionProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$libraryScrollPositionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LibraryScrollPosition = Notifier<double>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
