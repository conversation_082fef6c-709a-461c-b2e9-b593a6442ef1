import 'package:flutter/material.dart';

class CertificateCard extends StatelessWidget {
  final String title;
  final String issueDate;

  const CertificateCard({
    super.key,
    required this.title,
    required this.issueDate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withAlpha(26),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(
          color: Theme.of(context).primaryColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left Section (Thumbnail)
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor.withAlpha(26),
                    Theme.of(context).primaryColor.withAlpha(77),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Icon(
                Icons.article_outlined,
                color: Theme.of(context).primaryColor,
                size: 40,
              ),
            ),
            const SizedBox(width: 16.0),
            // Middle Section (Details)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.0,
                      color: Theme.of(context).primaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    'Issued: $issueDate',
                    style: const TextStyle(fontSize: 14.0, color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16.0),
            // Right Section (Action Icon)
            Icon(
              Icons.download_outlined,
              color: Theme.of(context).primaryColor,
              size: 28,
            ),
          ],
        ),
      ),
    );
  }
}
