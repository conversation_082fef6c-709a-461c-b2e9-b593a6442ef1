import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class GetStartedBanner extends StatelessWidget {
  const GetStartedBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const textShadow = Shadow(
      blurRadius: 3.0,
      color: Colors.black45,
      offset: Offset(1.0, 1.0),
    );

    const whiteTextStyle = TextStyle(
      color: Colors.white,
      fontWeight: FontWeight.bold,
      shadows: [textShadow],
    );

    return SizedBox(
      height: 200,
      width: size.width,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset('$assetImageDashboard/journey.jpg', fit: BoxFit.cover),
          Container(color: Colors.black.withValues(alpha: .1)),
          Center(
            child: SizedBox(
              width: size.width * 0.8,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 16,
                children: [
                  Text(
                    context.loc.unlock_opportunities,
                    style: whiteTextStyle.copyWith(fontSize: 16),
                    softWrap: true,
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    context.loc.start_journey,
                    style: whiteTextStyle.copyWith(fontSize: 22),
                    softWrap: true,
                    textAlign: TextAlign.center,
                  ),
                  ElevatedButton(
                    onPressed:
                        () => customNav(
                          context,
                          RouterName.pronunciationChallengeOnboarding,
                          params: {'level': Level.a1.name, 'chapter': '1'},
                        ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      context.loc.get_started,
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
