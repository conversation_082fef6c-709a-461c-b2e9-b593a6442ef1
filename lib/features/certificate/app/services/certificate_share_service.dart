import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../utils/image_converter.dart';

class CertificateShareService {
  final Dio _dio = Dio();

  /// Share a certificate image from URL
  /// Returns a map with success status and message for user feedback
  Future<Map<String, dynamic>> shareCertificate({
    required String url,
    required String fileName,
    String? text,
  }) async {
    try {
      // Download the image to a temporary location
      final tempResult = await _downloadToTemp(url, fileName);
      if (!tempResult['success']) {
        return tempResult;
      }

      final filePath = tempResult['path'] as String;
      final file = File(filePath);

      // Share the file using SharePlus.instance.share
      final result = await SharePlus.instance.share(
        ShareParams(
          files: [XFile(filePath)],
          text: text ?? 'Check out my certificate!',
          subject: 'My Certificate',
        ),
      );

      // Clean up temporary file after sharing
      _cleanupTempFile(file);

      if (result.status == ShareResultStatus.success) {
        return {'success': true, 'message': 'Certificate shared successfully'};
      } else {
        return {'success': false, 'message': 'Sharing was cancelled or failed'};
      }
    } catch (e) {
      return {'success': false, 'message': 'Share failed: ${e.toString()}'};
    }
  }

  /// Share multiple certificate pages at once
  Future<Map<String, dynamic>> shareMultipleCertificates({
    required List<String> urls,
    required List<String> fileNames,
    String? text,
  }) async {
    try {
      if (urls.length != fileNames.length) {
        return {
          'success': false,
          'message': 'URLs and file names count mismatch',
        };
      }

      final List<XFile> files = [];
      final List<File> tempFiles = [];

      // Download all images to temporary locations
      for (int i = 0; i < urls.length; i++) {
        final tempResult = await _downloadToTemp(urls[i], fileNames[i]);
        if (!tempResult['success']) {
          // Clean up any files downloaded so far
          for (final tempFile in tempFiles) {
            _cleanupTempFile(tempFile);
          }
          return tempResult;
        }

        final filePath = tempResult['path'] as String;
        final file = File(filePath);
        files.add(XFile(filePath));
        tempFiles.add(file);
      }

      // Share all files using SharePlus.instance.share
      final result = await SharePlus.instance.share(
        ShareParams(
          files: files,
          text: text ?? 'Check out my certificates!',
          subject: 'My Certificates',
        ),
      );

      // Clean up all temporary files
      for (final tempFile in tempFiles) {
        _cleanupTempFile(tempFile);
      }

      if (result.status == ShareResultStatus.success) {
        return {'success': true, 'message': 'Certificates shared successfully'};
      } else {
        return {'success': false, 'message': 'Sharing was cancelled or failed'};
      }
    } catch (e) {
      return {'success': false, 'message': 'Share failed: ${e.toString()}'};
    }
  }

  /// Download SVG and convert to PNG in temporary directory
  Future<Map<String, dynamic>> _downloadToTemp(
    String url,
    String fileName,
  ) async {
    try {
      final tempDir = await getTemporaryDirectory();

      // Ensure filename has .png extension
      final pngFileName = fileName.replaceAll(
        RegExp(r'\.(jpg|jpeg|svg)$', caseSensitive: false),
        '.png',
      );
      final tempPath = '${tempDir.path}/$pngFileName';

      // Download SVG and convert to PNG
      final pngBytes = await _downloadAndConvertSvgToPng(url);

      if (pngBytes == null) {
        return {
          'success': false,
          'message': 'Failed to convert certificate to PNG',
        };
      }

      // Save PNG file to temp directory
      final file = File(tempPath);
      await file.writeAsBytes(pngBytes);

      return {'success': true, 'path': tempPath};
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to download and convert image: ${e.toString()}',
      };
    }
  }

  /// Downloads SVG from URL and converts it to PNG bytes
  /// Returns high-quality PNG bytes suitable for certificate sharing
  Future<Uint8List?> _downloadAndConvertSvgToPng(String svgUrl) async {
    try {
      // Download SVG content
      final response = await _dio.get(
        svgUrl,
        options: Options(responseType: ResponseType.plain),
      );

      final svgString = response.data as String;

      // Convert SVG to PNG with high quality
      return await ImageConverter.svgToPng(svgString);
    } catch (e) {
      debugPrint('Failed to download or convert SVG: $e');
      return null;
    }
  }

  /// Clean up temporary file (fire and forget)
  void _cleanupTempFile(File file) {
    file.delete().catchError((error) {
      // Silently handle cleanup errors - return the file to satisfy the handler
      return file;
    });
  }

  /// Share certificate with custom text and subject
  Future<Map<String, dynamic>> shareCertificateWithCustomMessage({
    required String url,
    required String fileName,
    required String customText,
    required String subject,
  }) async {
    return shareCertificate(url: url, fileName: fileName, text: customText);
  }
}
