// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:selfeng/main/app_env.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static FirebaseOptions web = FirebaseOptions(
    apiKey: EnvInfo.firebaseApiKeyWeb,
    appId: EnvInfo.firebaseAppIdWeb,
    messagingSenderId: EnvInfo.firebaseMessagingSenderIdWeb,
    projectId: EnvInfo.firebaseProjectId,
    authDomain: EnvInfo.firebaseAuthDomain,
    storageBucket: EnvInfo.firebaseStorageBucket,
    measurementId: EnvInfo.firebaseMeasurementIdWeb,
  );

  static FirebaseOptions android = FirebaseOptions(
    apiKey: EnvInfo.firebaseApiKeyAndroid,
    appId: EnvInfo.firebaseAppIdAndroid,
    messagingSenderId: EnvInfo.firebaseMessagingSenderIdAndroid,
    projectId: EnvInfo.firebaseProjectId,
    storageBucket: EnvInfo.firebaseStorageBucket,
  );

  static FirebaseOptions ios = FirebaseOptions(
    apiKey: EnvInfo.firebaseApiKeyIos,
    appId: EnvInfo.firebaseAppIdIos,
    messagingSenderId: EnvInfo.firebaseMessagingSenderIdIos,
    projectId: EnvInfo.firebaseProjectId,
    storageBucket: EnvInfo.firebaseStorageBucket,
    androidClientId: EnvInfo.firebaseAndroidClientId,
    iosClientId: EnvInfo.firebaseIosClientId,
    iosBundleId: EnvInfo.firebaseIosBundleId,
  );
}
