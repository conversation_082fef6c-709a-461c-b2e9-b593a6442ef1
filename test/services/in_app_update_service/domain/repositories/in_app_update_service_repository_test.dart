import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

// Mock classes
class MockAppUpdateInfo extends Mock implements AppUpdateInfo {}

// Create a wrapper for Platform to allow mocking
class PlatformWrapper {
  bool get isAndroid => Platform.isAndroid;
  bool get isIOS => Platform.isIOS;
}

void main() {
  group('InAppUpdateServiceRepository', () {
    late InAppUpdateServiceRepository repository;

    setUp(() {
      repository = InAppUpdateServiceRepository();
    });

    group('initialize', () {
      test('should initialize successfully on supported platforms', () async {
        // Act
        final result = await repository.initialize();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        expect(repository.isInitialized, isTrue);
      });
    });

    group('checkForUpdate', () {
      test(
        'should check for updates successfully when update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(2);
          when(() => mockUpdateInfo.updatePriority).thenReturn(1);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);
          when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

          // Mock the static method
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Right<AppException, AppUpdateInfo?>>());
          expect(
            (result as Right<AppException, AppUpdateInfo?>).value,
            equals(mockUpdateInfo),
          );
          expect(repository.isUpdateAvailable, isTrue);
        },
      );

      test(
        'should check for updates successfully when no update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          // Mock the static method
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Right<AppException, AppUpdateInfo?>>());
          expect(
            (result as Right<AppException, AppUpdateInfo?>).value,
            equals(mockUpdateInfo),
          );
          expect(repository.isUpdateAvailable, isFalse);
        },
      );

      test(
        'should return Left with AppException when checking for updates fails',
        () async {
          // Arrange
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenThrow(Exception('Failed to check for updates'));

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Left<AppException, AppUpdateInfo?>>());
          expect(
            (result as Left<AppException, AppUpdateInfo?>).value.identifier,
            equals('in_app_update_check_error'),
          );
        },
      );
    });

    group('startImmediateUpdate', () {
      test('should start immediate update successfully', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);

        // Mock the static method
        when(
          () => InAppUpdate.checkForUpdate(),
        ).thenAnswer((_) async => mockUpdateInfo);
        when(
          () => InAppUpdate.performImmediateUpdate(),
        ).thenAnswer((_) async => Future.value());

        // First check for update to populate _updateInfo
        await repository.checkForUpdate();

        // Act
        final result = await repository.startImmediateUpdate();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => InAppUpdate.performImmediateUpdate()).called(1);
      });

      test(
        'should return Left with AppException when no update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          // Mock the static method
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // First check for update to populate _updateInfo
          await repository.checkForUpdate();

          // Act
          final result = await repository.startImmediateUpdate();

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('no_update_available'),
          );
        },
      );

      test(
        'should return Left with AppException when immediate update is not allowed',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(false);

          // Mock the static method
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // First check for update to populate _updateInfo
          await repository.checkForUpdate();

          // Act
          final result = await repository.startImmediateUpdate();

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('immediate_update_not_allowed'),
          );
        },
      );

      test(
        'should return Left with AppException when starting immediate update fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);

          // Mock the static method
          when(
            () => InAppUpdate.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);
          when(
            () => InAppUpdate.performImmediateUpdate(),
          ).thenThrow(Exception('Failed to start immediate update'));

          // First check for update to populate _updateInfo
          await repository.checkForUpdate();

          // Act
          final result = await repository.startImmediateUpdate();

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('immediate_update_failed'),
          );
        },
      );
    });

    // Additional tests for other methods can be added here...
  });
}
