import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_state.freezed.dart';

@freezed
sealed class AuthState with _$AuthState {
  const factory AuthState.initial() = Initial;
  const factory AuthState.loading() = Loading;
  const factory AuthState.failure({required AppException exception}) = Failure;
  const factory AuthState.success() = Success;
  const factory AuthState.signedIn({required User user}) = SignedIn;
  const factory AuthState.signedOut() = SignedOut;

  const AuthState._();
  bool get isAuth => switch (this) {
    SignedIn() => true,
    _ => false,
  };
}
