// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'questionnaire_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$QuestionnaireState {

 List<QuestionnaireModel> get questions; dynamic get selectedAnswerQ1; dynamic get selectedAnswerQ2; dynamic get selectedAnswerQ3; dynamic get selectedAnswerQ4; dynamic get selectedAnswerQ5; dynamic get selectedAnswerQ6; dynamic get selectedAnswerQ7; dynamic get selectedAnswerQ8; int get currentQuestion; ScrollController? get controllerScroll;
/// Create a copy of QuestionnaireState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QuestionnaireStateCopyWith<QuestionnaireState> get copyWith => _$QuestionnaireStateCopyWithImpl<QuestionnaireState>(this as QuestionnaireState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is QuestionnaireState&&const DeepCollectionEquality().equals(other.questions, questions)&&const DeepCollectionEquality().equals(other.selectedAnswerQ1, selectedAnswerQ1)&&const DeepCollectionEquality().equals(other.selectedAnswerQ2, selectedAnswerQ2)&&const DeepCollectionEquality().equals(other.selectedAnswerQ3, selectedAnswerQ3)&&const DeepCollectionEquality().equals(other.selectedAnswerQ4, selectedAnswerQ4)&&const DeepCollectionEquality().equals(other.selectedAnswerQ5, selectedAnswerQ5)&&const DeepCollectionEquality().equals(other.selectedAnswerQ6, selectedAnswerQ6)&&const DeepCollectionEquality().equals(other.selectedAnswerQ7, selectedAnswerQ7)&&const DeepCollectionEquality().equals(other.selectedAnswerQ8, selectedAnswerQ8)&&(identical(other.currentQuestion, currentQuestion) || other.currentQuestion == currentQuestion)&&(identical(other.controllerScroll, controllerScroll) || other.controllerScroll == controllerScroll));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(questions),const DeepCollectionEquality().hash(selectedAnswerQ1),const DeepCollectionEquality().hash(selectedAnswerQ2),const DeepCollectionEquality().hash(selectedAnswerQ3),const DeepCollectionEquality().hash(selectedAnswerQ4),const DeepCollectionEquality().hash(selectedAnswerQ5),const DeepCollectionEquality().hash(selectedAnswerQ6),const DeepCollectionEquality().hash(selectedAnswerQ7),const DeepCollectionEquality().hash(selectedAnswerQ8),currentQuestion,controllerScroll);

@override
String toString() {
  return 'QuestionnaireState(questions: $questions, selectedAnswerQ1: $selectedAnswerQ1, selectedAnswerQ2: $selectedAnswerQ2, selectedAnswerQ3: $selectedAnswerQ3, selectedAnswerQ4: $selectedAnswerQ4, selectedAnswerQ5: $selectedAnswerQ5, selectedAnswerQ6: $selectedAnswerQ6, selectedAnswerQ7: $selectedAnswerQ7, selectedAnswerQ8: $selectedAnswerQ8, currentQuestion: $currentQuestion, controllerScroll: $controllerScroll)';
}


}

/// @nodoc
abstract mixin class $QuestionnaireStateCopyWith<$Res>  {
  factory $QuestionnaireStateCopyWith(QuestionnaireState value, $Res Function(QuestionnaireState) _then) = _$QuestionnaireStateCopyWithImpl;
@useResult
$Res call({
 List<QuestionnaireModel> questions, dynamic selectedAnswerQ1, dynamic selectedAnswerQ2, dynamic selectedAnswerQ3, dynamic selectedAnswerQ4, dynamic selectedAnswerQ5, dynamic selectedAnswerQ6, dynamic selectedAnswerQ7, dynamic selectedAnswerQ8, int currentQuestion, ScrollController? controllerScroll
});




}
/// @nodoc
class _$QuestionnaireStateCopyWithImpl<$Res>
    implements $QuestionnaireStateCopyWith<$Res> {
  _$QuestionnaireStateCopyWithImpl(this._self, this._then);

  final QuestionnaireState _self;
  final $Res Function(QuestionnaireState) _then;

/// Create a copy of QuestionnaireState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? questions = null,Object? selectedAnswerQ1 = freezed,Object? selectedAnswerQ2 = freezed,Object? selectedAnswerQ3 = freezed,Object? selectedAnswerQ4 = freezed,Object? selectedAnswerQ5 = freezed,Object? selectedAnswerQ6 = freezed,Object? selectedAnswerQ7 = freezed,Object? selectedAnswerQ8 = freezed,Object? currentQuestion = null,Object? controllerScroll = freezed,}) {
  return _then(_self.copyWith(
questions: null == questions ? _self.questions : questions // ignore: cast_nullable_to_non_nullable
as List<QuestionnaireModel>,selectedAnswerQ1: freezed == selectedAnswerQ1 ? _self.selectedAnswerQ1 : selectedAnswerQ1 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ2: freezed == selectedAnswerQ2 ? _self.selectedAnswerQ2 : selectedAnswerQ2 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ3: freezed == selectedAnswerQ3 ? _self.selectedAnswerQ3 : selectedAnswerQ3 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ4: freezed == selectedAnswerQ4 ? _self.selectedAnswerQ4 : selectedAnswerQ4 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ5: freezed == selectedAnswerQ5 ? _self.selectedAnswerQ5 : selectedAnswerQ5 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ6: freezed == selectedAnswerQ6 ? _self.selectedAnswerQ6 : selectedAnswerQ6 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ7: freezed == selectedAnswerQ7 ? _self.selectedAnswerQ7 : selectedAnswerQ7 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ8: freezed == selectedAnswerQ8 ? _self.selectedAnswerQ8 : selectedAnswerQ8 // ignore: cast_nullable_to_non_nullable
as dynamic,currentQuestion: null == currentQuestion ? _self.currentQuestion : currentQuestion // ignore: cast_nullable_to_non_nullable
as int,controllerScroll: freezed == controllerScroll ? _self.controllerScroll : controllerScroll // ignore: cast_nullable_to_non_nullable
as ScrollController?,
  ));
}

}


/// Adds pattern-matching-related methods to [QuestionnaireState].
extension QuestionnaireStatePatterns on QuestionnaireState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _QuestionnaireState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _QuestionnaireState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _QuestionnaireState value)  $default,){
final _that = this;
switch (_that) {
case _QuestionnaireState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _QuestionnaireState value)?  $default,){
final _that = this;
switch (_that) {
case _QuestionnaireState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<QuestionnaireModel> questions,  dynamic selectedAnswerQ1,  dynamic selectedAnswerQ2,  dynamic selectedAnswerQ3,  dynamic selectedAnswerQ4,  dynamic selectedAnswerQ5,  dynamic selectedAnswerQ6,  dynamic selectedAnswerQ7,  dynamic selectedAnswerQ8,  int currentQuestion,  ScrollController? controllerScroll)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _QuestionnaireState() when $default != null:
return $default(_that.questions,_that.selectedAnswerQ1,_that.selectedAnswerQ2,_that.selectedAnswerQ3,_that.selectedAnswerQ4,_that.selectedAnswerQ5,_that.selectedAnswerQ6,_that.selectedAnswerQ7,_that.selectedAnswerQ8,_that.currentQuestion,_that.controllerScroll);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<QuestionnaireModel> questions,  dynamic selectedAnswerQ1,  dynamic selectedAnswerQ2,  dynamic selectedAnswerQ3,  dynamic selectedAnswerQ4,  dynamic selectedAnswerQ5,  dynamic selectedAnswerQ6,  dynamic selectedAnswerQ7,  dynamic selectedAnswerQ8,  int currentQuestion,  ScrollController? controllerScroll)  $default,) {final _that = this;
switch (_that) {
case _QuestionnaireState():
return $default(_that.questions,_that.selectedAnswerQ1,_that.selectedAnswerQ2,_that.selectedAnswerQ3,_that.selectedAnswerQ4,_that.selectedAnswerQ5,_that.selectedAnswerQ6,_that.selectedAnswerQ7,_that.selectedAnswerQ8,_that.currentQuestion,_that.controllerScroll);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<QuestionnaireModel> questions,  dynamic selectedAnswerQ1,  dynamic selectedAnswerQ2,  dynamic selectedAnswerQ3,  dynamic selectedAnswerQ4,  dynamic selectedAnswerQ5,  dynamic selectedAnswerQ6,  dynamic selectedAnswerQ7,  dynamic selectedAnswerQ8,  int currentQuestion,  ScrollController? controllerScroll)?  $default,) {final _that = this;
switch (_that) {
case _QuestionnaireState() when $default != null:
return $default(_that.questions,_that.selectedAnswerQ1,_that.selectedAnswerQ2,_that.selectedAnswerQ3,_that.selectedAnswerQ4,_that.selectedAnswerQ5,_that.selectedAnswerQ6,_that.selectedAnswerQ7,_that.selectedAnswerQ8,_that.currentQuestion,_that.controllerScroll);case _:
  return null;

}
}

}

/// @nodoc


class _QuestionnaireState extends QuestionnaireState {
   _QuestionnaireState({final  List<QuestionnaireModel> questions = const [], this.selectedAnswerQ1, this.selectedAnswerQ2, this.selectedAnswerQ3, this.selectedAnswerQ4, this.selectedAnswerQ5, this.selectedAnswerQ6, this.selectedAnswerQ7, this.selectedAnswerQ8, this.currentQuestion = 0, this.controllerScroll}): _questions = questions,super._();
  

 final  List<QuestionnaireModel> _questions;
@override@JsonKey() List<QuestionnaireModel> get questions {
  if (_questions is EqualUnmodifiableListView) return _questions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_questions);
}

@override final  dynamic selectedAnswerQ1;
@override final  dynamic selectedAnswerQ2;
@override final  dynamic selectedAnswerQ3;
@override final  dynamic selectedAnswerQ4;
@override final  dynamic selectedAnswerQ5;
@override final  dynamic selectedAnswerQ6;
@override final  dynamic selectedAnswerQ7;
@override final  dynamic selectedAnswerQ8;
@override@JsonKey() final  int currentQuestion;
@override final  ScrollController? controllerScroll;

/// Create a copy of QuestionnaireState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QuestionnaireStateCopyWith<_QuestionnaireState> get copyWith => __$QuestionnaireStateCopyWithImpl<_QuestionnaireState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _QuestionnaireState&&const DeepCollectionEquality().equals(other._questions, _questions)&&const DeepCollectionEquality().equals(other.selectedAnswerQ1, selectedAnswerQ1)&&const DeepCollectionEquality().equals(other.selectedAnswerQ2, selectedAnswerQ2)&&const DeepCollectionEquality().equals(other.selectedAnswerQ3, selectedAnswerQ3)&&const DeepCollectionEquality().equals(other.selectedAnswerQ4, selectedAnswerQ4)&&const DeepCollectionEquality().equals(other.selectedAnswerQ5, selectedAnswerQ5)&&const DeepCollectionEquality().equals(other.selectedAnswerQ6, selectedAnswerQ6)&&const DeepCollectionEquality().equals(other.selectedAnswerQ7, selectedAnswerQ7)&&const DeepCollectionEquality().equals(other.selectedAnswerQ8, selectedAnswerQ8)&&(identical(other.currentQuestion, currentQuestion) || other.currentQuestion == currentQuestion)&&(identical(other.controllerScroll, controllerScroll) || other.controllerScroll == controllerScroll));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_questions),const DeepCollectionEquality().hash(selectedAnswerQ1),const DeepCollectionEquality().hash(selectedAnswerQ2),const DeepCollectionEquality().hash(selectedAnswerQ3),const DeepCollectionEquality().hash(selectedAnswerQ4),const DeepCollectionEquality().hash(selectedAnswerQ5),const DeepCollectionEquality().hash(selectedAnswerQ6),const DeepCollectionEquality().hash(selectedAnswerQ7),const DeepCollectionEquality().hash(selectedAnswerQ8),currentQuestion,controllerScroll);

@override
String toString() {
  return 'QuestionnaireState(questions: $questions, selectedAnswerQ1: $selectedAnswerQ1, selectedAnswerQ2: $selectedAnswerQ2, selectedAnswerQ3: $selectedAnswerQ3, selectedAnswerQ4: $selectedAnswerQ4, selectedAnswerQ5: $selectedAnswerQ5, selectedAnswerQ6: $selectedAnswerQ6, selectedAnswerQ7: $selectedAnswerQ7, selectedAnswerQ8: $selectedAnswerQ8, currentQuestion: $currentQuestion, controllerScroll: $controllerScroll)';
}


}

/// @nodoc
abstract mixin class _$QuestionnaireStateCopyWith<$Res> implements $QuestionnaireStateCopyWith<$Res> {
  factory _$QuestionnaireStateCopyWith(_QuestionnaireState value, $Res Function(_QuestionnaireState) _then) = __$QuestionnaireStateCopyWithImpl;
@override @useResult
$Res call({
 List<QuestionnaireModel> questions, dynamic selectedAnswerQ1, dynamic selectedAnswerQ2, dynamic selectedAnswerQ3, dynamic selectedAnswerQ4, dynamic selectedAnswerQ5, dynamic selectedAnswerQ6, dynamic selectedAnswerQ7, dynamic selectedAnswerQ8, int currentQuestion, ScrollController? controllerScroll
});




}
/// @nodoc
class __$QuestionnaireStateCopyWithImpl<$Res>
    implements _$QuestionnaireStateCopyWith<$Res> {
  __$QuestionnaireStateCopyWithImpl(this._self, this._then);

  final _QuestionnaireState _self;
  final $Res Function(_QuestionnaireState) _then;

/// Create a copy of QuestionnaireState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? questions = null,Object? selectedAnswerQ1 = freezed,Object? selectedAnswerQ2 = freezed,Object? selectedAnswerQ3 = freezed,Object? selectedAnswerQ4 = freezed,Object? selectedAnswerQ5 = freezed,Object? selectedAnswerQ6 = freezed,Object? selectedAnswerQ7 = freezed,Object? selectedAnswerQ8 = freezed,Object? currentQuestion = null,Object? controllerScroll = freezed,}) {
  return _then(_QuestionnaireState(
questions: null == questions ? _self._questions : questions // ignore: cast_nullable_to_non_nullable
as List<QuestionnaireModel>,selectedAnswerQ1: freezed == selectedAnswerQ1 ? _self.selectedAnswerQ1 : selectedAnswerQ1 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ2: freezed == selectedAnswerQ2 ? _self.selectedAnswerQ2 : selectedAnswerQ2 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ3: freezed == selectedAnswerQ3 ? _self.selectedAnswerQ3 : selectedAnswerQ3 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ4: freezed == selectedAnswerQ4 ? _self.selectedAnswerQ4 : selectedAnswerQ4 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ5: freezed == selectedAnswerQ5 ? _self.selectedAnswerQ5 : selectedAnswerQ5 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ6: freezed == selectedAnswerQ6 ? _self.selectedAnswerQ6 : selectedAnswerQ6 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ7: freezed == selectedAnswerQ7 ? _self.selectedAnswerQ7 : selectedAnswerQ7 // ignore: cast_nullable_to_non_nullable
as dynamic,selectedAnswerQ8: freezed == selectedAnswerQ8 ? _self.selectedAnswerQ8 : selectedAnswerQ8 // ignore: cast_nullable_to_non_nullable
as dynamic,currentQuestion: null == currentQuestion ? _self.currentQuestion : currentQuestion // ignore: cast_nullable_to_non_nullable
as int,controllerScroll: freezed == controllerScroll ? _self.controllerScroll : controllerScroll // ignore: cast_nullable_to_non_nullable
as ScrollController?,
  ));
}


}

// dart format on
