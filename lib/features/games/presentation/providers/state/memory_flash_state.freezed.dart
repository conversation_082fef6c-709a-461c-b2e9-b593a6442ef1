// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'memory_flash_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MemoryFlashState {

 GameStage get gameStage; List<WordObject> get activeWords; List<MemoryFlash> get memoryFlash; List<String> get words; List<String> get wordRandom; List<String> get wordChoices; int get score; int get selectedTopic; double get gameSpeed; bool get isPlaying; MemoryFlashTopic? get topic; int get currentTopicIndex; int get currentLevelIndex;
/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemoryFlashStateCopyWith<MemoryFlashState> get copyWith => _$MemoryFlashStateCopyWithImpl<MemoryFlashState>(this as MemoryFlashState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemoryFlashState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other.activeWords, activeWords)&&const DeepCollectionEquality().equals(other.memoryFlash, memoryFlash)&&const DeepCollectionEquality().equals(other.words, words)&&const DeepCollectionEquality().equals(other.wordRandom, wordRandom)&&const DeepCollectionEquality().equals(other.wordChoices, wordChoices)&&(identical(other.score, score) || other.score == score)&&(identical(other.selectedTopic, selectedTopic) || other.selectedTopic == selectedTopic)&&(identical(other.gameSpeed, gameSpeed) || other.gameSpeed == gameSpeed)&&(identical(other.isPlaying, isPlaying) || other.isPlaying == isPlaying)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex)&&(identical(other.currentLevelIndex, currentLevelIndex) || other.currentLevelIndex == currentLevelIndex));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(activeWords),const DeepCollectionEquality().hash(memoryFlash),const DeepCollectionEquality().hash(words),const DeepCollectionEquality().hash(wordRandom),const DeepCollectionEquality().hash(wordChoices),score,selectedTopic,gameSpeed,isPlaying,topic,currentTopicIndex,currentLevelIndex);

@override
String toString() {
  return 'MemoryFlashState(gameStage: $gameStage, activeWords: $activeWords, memoryFlash: $memoryFlash, words: $words, wordRandom: $wordRandom, wordChoices: $wordChoices, score: $score, selectedTopic: $selectedTopic, gameSpeed: $gameSpeed, isPlaying: $isPlaying, topic: $topic, currentTopicIndex: $currentTopicIndex, currentLevelIndex: $currentLevelIndex)';
}


}

/// @nodoc
abstract mixin class $MemoryFlashStateCopyWith<$Res>  {
  factory $MemoryFlashStateCopyWith(MemoryFlashState value, $Res Function(MemoryFlashState) _then) = _$MemoryFlashStateCopyWithImpl;
@useResult
$Res call({
 GameStage gameStage, List<WordObject> activeWords, List<MemoryFlash> memoryFlash, List<String> words, List<String> wordRandom, List<String> wordChoices, int score, int selectedTopic, double gameSpeed, bool isPlaying, MemoryFlashTopic? topic, int currentTopicIndex, int currentLevelIndex
});


$MemoryFlashTopicCopyWith<$Res>? get topic;

}
/// @nodoc
class _$MemoryFlashStateCopyWithImpl<$Res>
    implements $MemoryFlashStateCopyWith<$Res> {
  _$MemoryFlashStateCopyWithImpl(this._self, this._then);

  final MemoryFlashState _self;
  final $Res Function(MemoryFlashState) _then;

/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? gameStage = null,Object? activeWords = null,Object? memoryFlash = null,Object? words = null,Object? wordRandom = null,Object? wordChoices = null,Object? score = null,Object? selectedTopic = null,Object? gameSpeed = null,Object? isPlaying = null,Object? topic = freezed,Object? currentTopicIndex = null,Object? currentLevelIndex = null,}) {
  return _then(_self.copyWith(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,activeWords: null == activeWords ? _self.activeWords : activeWords // ignore: cast_nullable_to_non_nullable
as List<WordObject>,memoryFlash: null == memoryFlash ? _self.memoryFlash : memoryFlash // ignore: cast_nullable_to_non_nullable
as List<MemoryFlash>,words: null == words ? _self.words : words // ignore: cast_nullable_to_non_nullable
as List<String>,wordRandom: null == wordRandom ? _self.wordRandom : wordRandom // ignore: cast_nullable_to_non_nullable
as List<String>,wordChoices: null == wordChoices ? _self.wordChoices : wordChoices // ignore: cast_nullable_to_non_nullable
as List<String>,score: null == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as int,selectedTopic: null == selectedTopic ? _self.selectedTopic : selectedTopic // ignore: cast_nullable_to_non_nullable
as int,gameSpeed: null == gameSpeed ? _self.gameSpeed : gameSpeed // ignore: cast_nullable_to_non_nullable
as double,isPlaying: null == isPlaying ? _self.isPlaying : isPlaying // ignore: cast_nullable_to_non_nullable
as bool,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as MemoryFlashTopic?,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,currentLevelIndex: null == currentLevelIndex ? _self.currentLevelIndex : currentLevelIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MemoryFlashTopicCopyWith<$Res>? get topic {
    if (_self.topic == null) {
    return null;
  }

  return $MemoryFlashTopicCopyWith<$Res>(_self.topic!, (value) {
    return _then(_self.copyWith(topic: value));
  });
}
}


/// Adds pattern-matching-related methods to [MemoryFlashState].
extension MemoryFlashStatePatterns on MemoryFlashState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MemoryFlashState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MemoryFlashState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MemoryFlashState value)  $default,){
final _that = this;
switch (_that) {
case _MemoryFlashState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MemoryFlashState value)?  $default,){
final _that = this;
switch (_that) {
case _MemoryFlashState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( GameStage gameStage,  List<WordObject> activeWords,  List<MemoryFlash> memoryFlash,  List<String> words,  List<String> wordRandom,  List<String> wordChoices,  int score,  int selectedTopic,  double gameSpeed,  bool isPlaying,  MemoryFlashTopic? topic,  int currentTopicIndex,  int currentLevelIndex)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MemoryFlashState() when $default != null:
return $default(_that.gameStage,_that.activeWords,_that.memoryFlash,_that.words,_that.wordRandom,_that.wordChoices,_that.score,_that.selectedTopic,_that.gameSpeed,_that.isPlaying,_that.topic,_that.currentTopicIndex,_that.currentLevelIndex);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( GameStage gameStage,  List<WordObject> activeWords,  List<MemoryFlash> memoryFlash,  List<String> words,  List<String> wordRandom,  List<String> wordChoices,  int score,  int selectedTopic,  double gameSpeed,  bool isPlaying,  MemoryFlashTopic? topic,  int currentTopicIndex,  int currentLevelIndex)  $default,) {final _that = this;
switch (_that) {
case _MemoryFlashState():
return $default(_that.gameStage,_that.activeWords,_that.memoryFlash,_that.words,_that.wordRandom,_that.wordChoices,_that.score,_that.selectedTopic,_that.gameSpeed,_that.isPlaying,_that.topic,_that.currentTopicIndex,_that.currentLevelIndex);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( GameStage gameStage,  List<WordObject> activeWords,  List<MemoryFlash> memoryFlash,  List<String> words,  List<String> wordRandom,  List<String> wordChoices,  int score,  int selectedTopic,  double gameSpeed,  bool isPlaying,  MemoryFlashTopic? topic,  int currentTopicIndex,  int currentLevelIndex)?  $default,) {final _that = this;
switch (_that) {
case _MemoryFlashState() when $default != null:
return $default(_that.gameStage,_that.activeWords,_that.memoryFlash,_that.words,_that.wordRandom,_that.wordChoices,_that.score,_that.selectedTopic,_that.gameSpeed,_that.isPlaying,_that.topic,_that.currentTopicIndex,_that.currentLevelIndex);case _:
  return null;

}
}

}

/// @nodoc


class _MemoryFlashState implements MemoryFlashState {
   _MemoryFlashState({this.gameStage = GameStage.loading, final  List<WordObject> activeWords = const [], final  List<MemoryFlash> memoryFlash = const [], final  List<String> words = const [], final  List<String> wordRandom = const [], final  List<String> wordChoices = const [], this.score = 0, this.selectedTopic = 0, this.gameSpeed = 0.3, this.isPlaying = false, this.topic, this.currentTopicIndex = 0, this.currentLevelIndex = 0}): _activeWords = activeWords,_memoryFlash = memoryFlash,_words = words,_wordRandom = wordRandom,_wordChoices = wordChoices;
  

@override@JsonKey() final  GameStage gameStage;
 final  List<WordObject> _activeWords;
@override@JsonKey() List<WordObject> get activeWords {
  if (_activeWords is EqualUnmodifiableListView) return _activeWords;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_activeWords);
}

 final  List<MemoryFlash> _memoryFlash;
@override@JsonKey() List<MemoryFlash> get memoryFlash {
  if (_memoryFlash is EqualUnmodifiableListView) return _memoryFlash;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_memoryFlash);
}

 final  List<String> _words;
@override@JsonKey() List<String> get words {
  if (_words is EqualUnmodifiableListView) return _words;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_words);
}

 final  List<String> _wordRandom;
@override@JsonKey() List<String> get wordRandom {
  if (_wordRandom is EqualUnmodifiableListView) return _wordRandom;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wordRandom);
}

 final  List<String> _wordChoices;
@override@JsonKey() List<String> get wordChoices {
  if (_wordChoices is EqualUnmodifiableListView) return _wordChoices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wordChoices);
}

@override@JsonKey() final  int score;
@override@JsonKey() final  int selectedTopic;
@override@JsonKey() final  double gameSpeed;
@override@JsonKey() final  bool isPlaying;
@override final  MemoryFlashTopic? topic;
@override@JsonKey() final  int currentTopicIndex;
@override@JsonKey() final  int currentLevelIndex;

/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemoryFlashStateCopyWith<_MemoryFlashState> get copyWith => __$MemoryFlashStateCopyWithImpl<_MemoryFlashState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemoryFlashState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other._activeWords, _activeWords)&&const DeepCollectionEquality().equals(other._memoryFlash, _memoryFlash)&&const DeepCollectionEquality().equals(other._words, _words)&&const DeepCollectionEquality().equals(other._wordRandom, _wordRandom)&&const DeepCollectionEquality().equals(other._wordChoices, _wordChoices)&&(identical(other.score, score) || other.score == score)&&(identical(other.selectedTopic, selectedTopic) || other.selectedTopic == selectedTopic)&&(identical(other.gameSpeed, gameSpeed) || other.gameSpeed == gameSpeed)&&(identical(other.isPlaying, isPlaying) || other.isPlaying == isPlaying)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex)&&(identical(other.currentLevelIndex, currentLevelIndex) || other.currentLevelIndex == currentLevelIndex));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(_activeWords),const DeepCollectionEquality().hash(_memoryFlash),const DeepCollectionEquality().hash(_words),const DeepCollectionEquality().hash(_wordRandom),const DeepCollectionEquality().hash(_wordChoices),score,selectedTopic,gameSpeed,isPlaying,topic,currentTopicIndex,currentLevelIndex);

@override
String toString() {
  return 'MemoryFlashState(gameStage: $gameStage, activeWords: $activeWords, memoryFlash: $memoryFlash, words: $words, wordRandom: $wordRandom, wordChoices: $wordChoices, score: $score, selectedTopic: $selectedTopic, gameSpeed: $gameSpeed, isPlaying: $isPlaying, topic: $topic, currentTopicIndex: $currentTopicIndex, currentLevelIndex: $currentLevelIndex)';
}


}

/// @nodoc
abstract mixin class _$MemoryFlashStateCopyWith<$Res> implements $MemoryFlashStateCopyWith<$Res> {
  factory _$MemoryFlashStateCopyWith(_MemoryFlashState value, $Res Function(_MemoryFlashState) _then) = __$MemoryFlashStateCopyWithImpl;
@override @useResult
$Res call({
 GameStage gameStage, List<WordObject> activeWords, List<MemoryFlash> memoryFlash, List<String> words, List<String> wordRandom, List<String> wordChoices, int score, int selectedTopic, double gameSpeed, bool isPlaying, MemoryFlashTopic? topic, int currentTopicIndex, int currentLevelIndex
});


@override $MemoryFlashTopicCopyWith<$Res>? get topic;

}
/// @nodoc
class __$MemoryFlashStateCopyWithImpl<$Res>
    implements _$MemoryFlashStateCopyWith<$Res> {
  __$MemoryFlashStateCopyWithImpl(this._self, this._then);

  final _MemoryFlashState _self;
  final $Res Function(_MemoryFlashState) _then;

/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? gameStage = null,Object? activeWords = null,Object? memoryFlash = null,Object? words = null,Object? wordRandom = null,Object? wordChoices = null,Object? score = null,Object? selectedTopic = null,Object? gameSpeed = null,Object? isPlaying = null,Object? topic = freezed,Object? currentTopicIndex = null,Object? currentLevelIndex = null,}) {
  return _then(_MemoryFlashState(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,activeWords: null == activeWords ? _self._activeWords : activeWords // ignore: cast_nullable_to_non_nullable
as List<WordObject>,memoryFlash: null == memoryFlash ? _self._memoryFlash : memoryFlash // ignore: cast_nullable_to_non_nullable
as List<MemoryFlash>,words: null == words ? _self._words : words // ignore: cast_nullable_to_non_nullable
as List<String>,wordRandom: null == wordRandom ? _self._wordRandom : wordRandom // ignore: cast_nullable_to_non_nullable
as List<String>,wordChoices: null == wordChoices ? _self._wordChoices : wordChoices // ignore: cast_nullable_to_non_nullable
as List<String>,score: null == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as int,selectedTopic: null == selectedTopic ? _self.selectedTopic : selectedTopic // ignore: cast_nullable_to_non_nullable
as int,gameSpeed: null == gameSpeed ? _self.gameSpeed : gameSpeed // ignore: cast_nullable_to_non_nullable
as double,isPlaying: null == isPlaying ? _self.isPlaying : isPlaying // ignore: cast_nullable_to_non_nullable
as bool,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as MemoryFlashTopic?,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,currentLevelIndex: null == currentLevelIndex ? _self.currentLevelIndex : currentLevelIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of MemoryFlashState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MemoryFlashTopicCopyWith<$Res>? get topic {
    if (_self.topic == null) {
    return null;
  }

  return $MemoryFlashTopicCopyWith<$Res>(_self.topic!, (value) {
    return _then(_self.copyWith(topic: value));
  });
}
}

// dart format on
