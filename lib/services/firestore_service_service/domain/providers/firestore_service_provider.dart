import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final firestoreServiceRepositoryProvider = Provider<FirestoreServiceRepository>(
  (ref) {
    FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
    FirebaseAuth firebaseAuth = FirebaseAuth.instance;
    FirebaseStorage firebaseStorage = FirebaseStorage.instance;
    FirebaseFunctions firebaseFunctions = FirebaseFunctions.instanceFor(
      region: 'asia-southeast1',
    );
    return FirestoreServiceRepositoryImpl(
      firebaseFirestore,
      firebaseAuth,
      firebaseStorage,
      firebaseFunctions,
    );
  },
);
