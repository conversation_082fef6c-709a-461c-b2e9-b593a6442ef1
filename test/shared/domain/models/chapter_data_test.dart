import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';

void main() {
  group('ChapterData Model Tests', () {
    group('Constructor and Properties', () {
      test('should create ChapterData with all required properties', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        expect(chapterData.chapter, equals(1));
        expect(chapterData.label, equals('Test Chapter'));
        expect(chapterData.description, equals(description));
        expect(chapterData.image, equals('test_image.png'));
        expect(
          chapterData.imageUrl,
          equals('https://example.com/test_image.png'),
        );
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        final json = chapterData.toJson();

        expect(json['chapter'], equals(1));
        expect(json['label'], equals('Test Chapter'));
        expect(json['description'], isNotNull);
        expect(json['image'], equals('test_image.png'));
        expect(json['image_url'], equals('https://example.com/test_image.png'));
      });

      test('should deserialize from JSON correctly', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final json = {
          'chapter': 2,
          'label': 'Chapter 2',
          'description': {
            'en': 'English description',
            'id': 'Indonesian description',
          },
          'image': 'chapter2.png',
          'image_url': 'https://example.com/chapter2.png',
        };

        final chapterData = ChapterData.fromJson(json);

        expect(chapterData.chapter, equals(2));
        expect(chapterData.label, equals('Chapter 2'));
        expect(chapterData.description, equals(description));
        expect(chapterData.image, equals('chapter2.png'));
        expect(
          chapterData.imageUrl,
          equals('https://example.com/chapter2.png'),
        );
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'chapter': 3,
          'label': 'Chapter 3',
          'description': {
            'en': 'English description',
            'id': 'Indonesian description',
          },
          'image': 'chapter3.png',
          'image_url': 'https://example.com/chapter3.png',
        };

        final chapterData = ChapterData.fromJson(json);

        expect(chapterData.chapter, equals(3));
        expect(chapterData.label, equals('Chapter 3'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData1 = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        final chapterData2 = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        expect(chapterData1, equals(chapterData2));
        expect(chapterData1.hashCode, equals(chapterData2.hashCode));
      });

      test('should not be equal when chapter is different', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData1 = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        final chapterData2 = ChapterData(
          chapter: 2,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        expect(chapterData1, isNot(equals(chapterData2)));
        expect(chapterData1.hashCode, isNot(equals(chapterData2.hashCode)));
      });

      test('should not be equal when label is different', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData1 = ChapterData(
          chapter: 1,
          label: 'Chapter 1',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        final chapterData2 = ChapterData(
          chapter: 1,
          label: 'Chapter 2',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        expect(chapterData1, isNot(equals(chapterData2)));
        expect(chapterData1.hashCode, isNot(equals(chapterData2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final chapterData = ChapterData(
          chapter: 1,
          label: 'Test Chapter',
          description: description,
          image: 'test_image.png',
          imageUrl: 'https://example.com/test_image.png',
        );

        final stringRepresentation = chapterData.toString();

        expect(stringRepresentation, contains('ChapterData'));
        expect(stringRepresentation, contains('chapter: 1'));
        expect(stringRepresentation, contains('label: Test Chapter'));
      });
    });
  });

  group('ChapterDescription Tests', () {
    group('Constructor and Properties', () {
      test('should create ChapterDescription with required properties', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        expect(description.en, equals('English description'));
        expect(description.id, equals('Indonesian description'));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final json = description.toJson();

        expect(json['en'], equals('English description'));
        expect(json['id'], equals('Indonesian description'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'en': 'English description',
          'id': 'Indonesian description',
        };

        final description = ChapterDescription.fromJson(json);

        expect(description.en, equals('English description'));
        expect(description.id, equals('Indonesian description'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        const description1 = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );
        const description2 = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        expect(description1, equals(description2));
        expect(description1.hashCode, equals(description2.hashCode));
      });

      test('should not be equal when English description is different', () {
        const description1 = ChapterDescription(
          en: 'English description 1',
          id: 'Indonesian description',
        );
        const description2 = ChapterDescription(
          en: 'English description 2',
          id: 'Indonesian description',
        );

        expect(description1, isNot(equals(description2)));
        expect(description1.hashCode, isNot(equals(description2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        const description = ChapterDescription(
          en: 'English description',
          id: 'Indonesian description',
        );

        final stringRepresentation = description.toString();

        expect(stringRepresentation, contains('ChapterDescription'));
        expect(stringRepresentation, contains('en: English description'));
        expect(stringRepresentation, contains('id: Indonesian description'));
      });
    });
  });
}
