import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class MainLessonRepository {
  Future<Either<AppException, List<ChapterIndexData>>> getChapterIndex({
    required String level,
  });
  Future<Either<AppException, List<ContentIndexData>>> getPathIndex({
    required String level,
    required String chapter,
    required SectionType section,
  });
  Future<Either<AppException, PronunciationSubPart>> getPronunciation({
    required String path,
  });

  Future<Either<AppException, ConversationPart>> getConversation({
    required String path,
  });

  Future<Either<AppException, ListeningPart>> getListening({
    required String path,
  });

  Future<Either<AppException, SpeakingPart>> getSpeaking({
    required String path,
  });

  Future<Either<AppException, List<ConversationPart>>> getConversationList(
    List<String> paths,
  );

  Future<Either<AppException, List<ListeningPart>>> getListeningList(
    List<String> paths,
  );

  Future<Either<AppException, List<SpeakingPart>>> getSpeakingList(
    List<String> paths,
  );

  Future<Either<AppException, List<ChapterData>>> getChapters(Level level);
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  );

  Future<Either<AppException, dynamic>> saveAnswer(answer);
  Future<Either<AppException, dynamic>> uploadAudio({required String path});
  Future<Either<AppException, PronunciationResultFormatted>>
  checkPronunciation({
    required AudioPath audio,
    required String text,
    bool autoDelete = true,
  });

  Future<Either<AppException, ContentIndexData>> getFirstLesson();

  Future<Either<AppException, bool>> saveIntro({required String lessonName});
  Future<Either<AppException, bool>> isIntro({required String lessonName});
}
