import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/features/library/domain/providers/level_info_provider.dart';
import 'package:selfeng/features/library/domain/providers/library_provider.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_chapter_state.dart';
import 'package:selfeng/shared/domain/models/level.dart';

part 'library_chapter_controller.g.dart';

@riverpod
class LibraryChapterController extends _$LibraryChapterController {
  // Cache states by level to avoid rebuilding during transitions
  final Map<String, LibraryChapterState> _stateCache = {};

  @override
  Future<LibraryChapterState> build(String level) async {
    try {
      // Return cached state if available
      if (_stateCache.containsKey(level)) {
        return _stateCache[level]!;
      }

      // Get repositories
      final libraryRepository = ref.watch(libraryRepositoryProvider);
      final levelInfo = ref.watch(levelInfoRepositoryProvider);

      // Get level info and chapters in parallel
      final levelEnum = Level.values.byName(level.toLowerCase());
      final levelInfoFuture = levelInfo.getLevelInfo(levelEnum);
      final chaptersFuture = libraryRepository.getChapters(level);

      // Wait for both to complete
      final results = await Future.wait([levelInfoFuture, chaptersFuture]);
      final levelInfoData = results[0] as LevelInfo;
      final chaptersResult = results[1];

      // Create state with both level info and chapters
      final state = (chaptersResult as dynamic).fold(
        (failure) => throw failure.message,
        (chapters) =>
            LibraryChapterState(levelInfo: levelInfoData, chapters: chapters),
      );

      // Cache the state
      _stateCache[level] = state;
      return state;
    } catch (e) {
      // Clear cache if there's an error
      _stateCache.remove(level);
      rethrow;
    }
  }
}
