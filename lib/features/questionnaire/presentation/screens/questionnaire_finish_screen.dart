import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/questionnaire/presentation/providers/questionnaire_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class QuestionnaireFinishScreen extends ConsumerStatefulWidget {
  const QuestionnaireFinishScreen({super.key});

  @override
  ConsumerState<QuestionnaireFinishScreen> createState() =>
      _QuestionnaireFinishScreenState();
}

class _QuestionnaireFinishScreenState
    extends ConsumerState<QuestionnaireFinishScreen> {
  late AsyncValue viewState;
  late QuestionnaireController viewModel;

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(questionnaireControllerProvider);
    viewModel = ref.watch(questionnaireControllerProvider.notifier);

    ref.listen(questionnaireControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      //show Snackbar on failure
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  '$assetImageQuestionnaire/BG-Done.png',
                  // fit: BoxFit.fitWidth,
                  width: 254,
                  height: 252,
                ),
                const SizedBox(height: 20),
                Text(
                  textAlign: TextAlign.center,
                  context.loc.questionnaireFinish,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 20),
                Text(
                  textAlign: TextAlign.center,
                  context.loc.questionnaireFinishDesc,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(0xff998E8D),
                  ),
                ),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: VButtonGradient(
                title: context.loc.questionnaireIWilling,
                onTap: () {
                  viewModel.saveAnswer();
                  customNav(
                    context,
                    RouterName.diagnosticTestOnboardScreen,
                    isReplace: true,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
