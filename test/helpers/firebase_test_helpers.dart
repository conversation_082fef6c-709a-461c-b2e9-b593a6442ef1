import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Firebase testing helpers that work around sealed class limitations
///
/// Since Firebase SDK uses sealed classes that cannot be mocked directly,
/// these helpers provide alternative approaches for testing Firebase functionality.
class FirebaseTestHelpers {
  FirebaseTestHelpers._();

  /// Creates a fake User object for testing
  ///
  /// Since User is a sealed class, we create a test implementation
  static TestUser createTestUser({
    String uid = 'test_user_id',
    String email = '<EMAIL>',
    String displayName = 'Test User',
    bool emailVerified = true,
  }) {
    return TestUser(
      uid: uid,
      email: email,
      displayName: displayName,
      emailVerified: emailVerified,
    );
  }

  /// Creates test data for Firestore operations
  static Map<String, dynamic> createTestUserData({
    String email = '<EMAIL>',
    bool afterTest = false,
    Map<String, dynamic>? lastPronunciation,
    Map<String, dynamic>? lastConversation,
    Map<String, dynamic>? lastListening,
    Map<String, dynamic>? lastSpeaking,
  }) {
    return {
      'email': email,
      'is_after_test': afterTest,
      'last_pronunciation': lastPronunciation,
      'last_conversation': lastConversation,
      'last_listening': lastListening,
      'last_speaking': lastSpeaking,
    };
  }

  /// Creates test data for LastCourse
  static Map<String, dynamic> createTestLastCourse({
    DateTime? accessTime,
    String level = 'A1',
    int chapter = 1,
    String section = 'pronunciation',
    String path = 'test_path',
    String speakingStage = 'stage1',
    int? partOrder,
    int? subpartOrder,
  }) {
    return {
      'accessTime': (accessTime ?? DateTime.now()).toIso8601String(),
      'level': level,
      'chapter': chapter,
      'section': section,
      'path': path,
      'speakingStage': speakingStage,
      'partOrder': partOrder,
      'subpartOrder': subpartOrder,
    };
  }

  /// Simulates Firebase Auth state changes
  static Stream<User?> createAuthStateStream({
    User? initialUser,
    List<User?> stateChanges = const [],
  }) {
    return Stream.fromIterable([initialUser, ...stateChanges]);
  }

  /// Creates a test implementation of Firebase exceptions
  static FirebaseAuthException createAuthException({
    String code = 'test-error',
    String message = 'Test error message',
  }) {
    return FirebaseAuthException(code: code, message: message);
  }

  /// Creates a test implementation of Firestore exceptions
  static FirebaseException createFirestoreException({
    String code = 'unavailable',
    String message = 'Test firestore error',
    String plugin = 'cloud_firestore',
  }) {
    return FirebaseException(code: code, message: message, plugin: plugin);
  }

  /// Verifies Firebase Auth method calls without mocking sealed classes
  static void verifyAuthMethodCalls(
    MockFirebaseAuth mockAuth, {
    bool shouldCallSignIn = false,
    bool shouldCallSignOut = false,
    bool shouldCallCreateUser = false,
  }) {
    if (shouldCallSignIn) {
      verify(
        () => mockAuth.signInWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
        ),
      ).called(1);
    }

    if (shouldCallSignOut) {
      verify(() => mockAuth.signOut()).called(1);
    }

    if (shouldCallCreateUser) {
      verify(
        () => mockAuth.createUserWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
        ),
      ).called(1);
    }
  }
}

/// Test implementation of User since the real User class is sealed
class TestUser implements User {
  TestUser({
    required this.uid,
    this.email,
    this.displayName,
    this.emailVerified = false,
    this.isAnonymous = false,
    this.phoneNumber,
    this.photoURL,
    this.refreshToken,
    this.tenantId,
    this.providerData = const [],
  });

  @override
  final String uid;

  @override
  final String? email;

  @override
  final String? displayName;

  @override
  final bool emailVerified;

  @override
  final bool isAnonymous;

  @override
  final String? phoneNumber;

  @override
  final String? photoURL;

  @override
  final String? refreshToken;

  @override
  final String? tenantId;

  @override
  final List<UserInfo> providerData;

  @override
  UserMetadata get metadata =>
      throw UnimplementedError('Not needed for basic testing');

  // Implement required methods with basic functionality for testing
  @override
  Future<void> delete() async {}

  @override
  Future<String> getIdToken([bool forceRefresh = false]) async => 'test_token';

  @override
  Future<IdTokenResult> getIdTokenResult([bool forceRefresh = false]) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<UserCredential> linkWithCredential(AuthCredential credential) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<ConfirmationResult> linkWithPhoneNumber(
    String phoneNumber, [
    RecaptchaVerifier? verifier,
  ]) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<UserCredential> linkWithPopup(AuthProvider provider) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<void> linkWithRedirect(AuthProvider provider) async {}

  @override
  Future<UserCredential> linkWithProvider(AuthProvider provider) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<UserCredential> reauthenticateWithCredential(
    AuthCredential credential,
  ) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<UserCredential> reauthenticateWithProvider(
    AuthProvider provider,
  ) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<UserCredential> reauthenticateWithPopup(AuthProvider provider) async {
    throw UnimplementedError('Not needed for basic testing');
  }

  @override
  Future<void> reauthenticateWithRedirect(AuthProvider provider) async {}

  @override
  Future<void> reload() async {}

  @override
  Future<void> sendEmailVerification([
    ActionCodeSettings? actionCodeSettings,
  ]) async {}

  @override
  Future<User> unlink(String providerId) async => this;

  @override
  Future<void> updateDisplayName(String? displayName) async {}

  Future<void> updateEmail(String newEmail) async {}

  @override
  Future<void> updatePassword(String newPassword) async {}

  @override
  Future<void> updatePhoneNumber(PhoneAuthCredential phoneCredential) async {}

  @override
  Future<void> updatePhotoURL(String? photoURL) async {}

  @override
  Future<void> updateProfile({String? displayName, String? photoURL}) async {}

  @override
  Future<void> verifyBeforeUpdateEmail(
    String newEmail, [
    ActionCodeSettings? actionCodeSettings,
  ]) async {}

  @override
  MultiFactor get multiFactor =>
      throw UnimplementedError('Not needed for basic testing');
}

/// Mock Firebase Auth for testing
class MockFirebaseAuth extends Mock implements FirebaseAuth {}
