import 'dart:ui';

import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class SettingCacheRepository {
  Future<Either<AppException, Locale>> fetchLocale();
  Future<bool> saveLocale({required Locale locale});
  Future<bool> deleteLocale();
  Future<bool> hasLocale();

  // Add abstract methods for audio state
  Future<Either<AppException, bool>> getAudioEnabled();
  Future<bool> saveAudioEnabled({required bool enabled});
}
