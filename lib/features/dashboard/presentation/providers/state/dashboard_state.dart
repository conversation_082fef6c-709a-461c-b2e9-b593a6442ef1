import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

part 'dashboard_state.freezed.dart';

@freezed
abstract class DashboardState with _$DashboardState {
  factory DashboardState({
    LastCourse? lastPronunciation,
    LastCourse? lastConversation,
    LastCourse? lastListening,
    LastCourse? lastSpeaking,
    @Default(true) bool afterTest,
    @Default([]) List<LastCourseInfo> lastCourse,
    ContentIndexData? firstLesson,
  }) = _DashboardState;
}
