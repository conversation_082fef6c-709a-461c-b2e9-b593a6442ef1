import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/features/authentication/data/factories/auth_repository_factory.dart';
import 'package:selfeng/features/authentication/data/repositories/auth_repository_impl.dart';
import 'package:selfeng/features/authentication/data/repositories/google_sign_in_repository_impl.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';
import '../../../../mocks/firebase_mocks.dart';
import '../../../../helpers/mock_factories.dart' as mock_factories;

void main() {
  group('AuthRepositoryFactory', () {
    late MockFirebaseAuth mockFirebaseAuth;
    late mock_factories.MockGoogleSignIn mockGoogleSignIn;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockGoogleSignIn = mock_factories.MockGoogleSignIn();
    });

    group('createAuthRepository', () {
      test('should create AuthRepositoryImpl instance', () {
        final repository = AuthRepositoryFactory.createAuthRepository(
          mockFirebaseAuth,
        );

        expect(repository, isA<AuthRepositoryImpl>());
        expect(repository, isA<AuthRepository>());
      });

      test('should use provided FirebaseAuth instance', () {
        final repository =
            AuthRepositoryFactory.createAuthRepository(mockFirebaseAuth)
                as AuthRepositoryImpl;

        // This test verifies that the repository was created with the provided instance
        // We can't directly access the private field, but we can verify the behavior
        // by mocking the FirebaseAuth and checking the interactions
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        final currentUser = repository.currentUser;
        expect(currentUser, isNull);
        verify(() => mockFirebaseAuth.currentUser).called(1);
      });
    });

    group('createGoogleSignInRepository', () {
      test('should create GoogleSignInRepositoryImpl instance', () {
        final repository = AuthRepositoryFactory.createGoogleSignInRepository(
          mockGoogleSignIn,
        );

        expect(repository, isA<GoogleSignInRepositoryImpl>());
        expect(repository, isA<GoogleSignInRepository>());
      });

      test('should use provided GoogleSignIn instance', () {
        final repository =
            AuthRepositoryFactory.createGoogleSignInRepository(mockGoogleSignIn)
                as GoogleSignInRepositoryImpl;

        // This test verifies that the repository was created with the provided instance
        // We can verify this by checking that calling methods on the repository
        // will interact with our mock
        when(() => mockGoogleSignIn.supportsAuthenticate()).thenReturn(true);

        final supportsAuthenticate = repository.supportsAuthenticate();
        expect(supportsAuthenticate, isTrue);
        verify(() => mockGoogleSignIn.supportsAuthenticate()).called(1);
      });
    });
  });
}
