import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
// Removed unused imports

/// Comprehensive Firebase service mocks for testing
/// Provides realistic mock behaviors for all Firebase services used in the app

// Firebase Core Mocks
class MockFirebaseApp extends Mock implements FirebaseApp {}

// Firebase Auth Mocks
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUser extends Mock implements User {}

class MockUserCredential extends Mock implements UserCredential {}

class MockAuthCredential extends Mock implements AuthCredential {}

class MockOAuthCredential extends Mock implements OAuthCredential {}

class MockUserInfo extends Mock implements UserInfo {}

// Firestore Mocks
class MockFirebaseFirestore extends Mock implements FirebaseFirestore {}

// Note: CollectionReference, DocumentReference, etc. are sealed classes in newer Firebase versions
// These mocks are commented out due to sealed class restrictions in some cases
// Uncomment if needed for specific Firebase versions
// The following Firestore classes are sealed in cloud_firestore v6+ and must not be implemented.
// Keep these commented. For tests, mock behaviors on FirebaseFirestore directly or use fakes.
/*
class MockCollectionReference<T> extends Mock implements CollectionReference<T> {}
class MockDocumentReference<T> extends Mock implements DocumentReference<T> {}
class MockDocumentSnapshot<T> extends Mock implements DocumentSnapshot<T> {}
class MockQuerySnapshot<T> extends Mock implements QuerySnapshot<T> {}
class MockQueryDocumentSnapshot<T> extends Mock implements QueryDocumentSnapshot<T> {}
class MockQuery<T> extends Mock implements Query<T> {}
class MockWriteBatch extends Mock implements WriteBatch {}
class MockTransaction extends Mock implements Transaction {}
*/

// Firebase Storage Mocks
class MockFirebaseStorage extends Mock implements FirebaseStorage {}

class MockReference extends Mock implements Reference {}

class MockUploadTask extends Mock implements UploadTask {}

class MockTaskSnapshot extends Mock implements TaskSnapshot {}

class MockFullMetadata extends Mock implements FullMetadata {}

// Firebase Messaging Mocks
class MockFirebaseMessaging extends Mock implements FirebaseMessaging {}

class MockRemoteMessage extends Mock implements RemoteMessage {}

class MockRemoteNotification extends Mock implements RemoteNotification {}

class MockNotificationSettings extends Mock implements NotificationSettings {}

// Firebase Analytics Mocks
class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

/// Firebase Auth Mock Setup
class FirebaseAuthMockSetup {
  static void setupAuthenticatedUser(
    MockFirebaseAuth mockAuth,
    MockUser mockUser, {
    String uid = 'test_user_id',
    String email = '<EMAIL>',
    String displayName = 'Test User',
    bool emailVerified = true,
  }) {
    when(() => mockUser.uid).thenReturn(uid);
    when(() => mockUser.email).thenReturn(email);
    when(() => mockUser.displayName).thenReturn(displayName);
    when(() => mockUser.emailVerified).thenReturn(emailVerified);
    when(() => mockUser.isAnonymous).thenReturn(false);
    when(() => mockUser.phoneNumber).thenReturn(null);
    when(() => mockUser.photoURL).thenReturn(null);
    when(() => mockUser.refreshToken).thenReturn('refresh_token');
    when(() => mockUser.tenantId).thenReturn(null);

    when(() => mockAuth.currentUser).thenReturn(mockUser);
    when(
      () => mockAuth.authStateChanges(),
    ).thenAnswer((_) => Stream.value(mockUser));
    when(
      () => mockAuth.userChanges(),
    ).thenAnswer((_) => Stream.value(mockUser));
    when(
      () => mockAuth.idTokenChanges(),
    ).thenAnswer((_) => Stream.value(mockUser));
  }

  static void setupUnauthenticatedUser(MockFirebaseAuth mockAuth) {
    when(() => mockAuth.currentUser).thenReturn(null);
    when(
      () => mockAuth.authStateChanges(),
    ).thenAnswer((_) => Stream.value(null));
    when(() => mockAuth.userChanges()).thenAnswer((_) => Stream.value(null));
    when(() => mockAuth.idTokenChanges()).thenAnswer((_) => Stream.value(null));
  }

  static void setupSignInSuccess(
    MockFirebaseAuth mockAuth,
    MockUserCredential mockCredential,
    MockUser mockUser,
  ) {
    when(() => mockCredential.user).thenReturn(mockUser);
    when(() => mockCredential.additionalUserInfo).thenReturn(null);
    when(() => mockCredential.credential).thenReturn(null);

    when(
      () => mockAuth.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenAnswer((_) async => mockCredential);

    when(
      () => mockAuth.signInWithCredential(any()),
    ).thenAnswer((_) async => mockCredential);
  }

  static void setupSignInFailure(
    MockFirebaseAuth mockAuth,
    String errorCode,
    String errorMessage,
  ) {
    when(
      () => mockAuth.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenThrow(FirebaseAuthException(code: errorCode, message: errorMessage));
  }

  static void setupSignOut(MockFirebaseAuth mockAuth) {
    when(() => mockAuth.signOut()).thenAnswer((_) async {});
  }
}

/// Firestore Mock Setup - commented out due to sealed class restrictions
/// Use direct mocking in individual tests instead
class FirestoreMockSetup {
  // Note: These methods are commented out because Firebase sealed classes cannot be mocked
  // Implement direct mocking in individual tests as needed
  // All Firestore setup methods commented out due to sealed class restrictions
  // Implement direct mocking in individual tests as needed

  /*
  static void setupCollection(...) { ... }
  static void setupDocument(...) { ... }
  static void setupDocumentGet(...) { ... }
  static void setupDocumentSet(...) { ... }
  static void setupDocumentUpdate(...) { ... }
  static void setupDocumentDelete(...) { ... }
  */

  /*
  static void setupCollectionGet(...) { ... }
  static void setupCollectionAdd(...) { ... }
  static void setupQuery(...) { ... }
  */
}

/// Firebase Storage Mock Setup
class FirebaseStorageMockSetup {
  static void setupReference(
    MockFirebaseStorage mockStorage,
    MockReference mockRef,
    String path,
  ) {
    when(() => mockStorage.ref(path)).thenReturn(mockRef);
    when(() => mockRef.fullPath).thenReturn(path);
    when(() => mockRef.name).thenReturn(path.split('/').last);
  }

  static void setupUpload(
    MockReference mockRef,
    MockUploadTask mockTask,
    MockTaskSnapshot mockSnapshot,
  ) {
    when(() => mockRef.putFile(any())).thenReturn(mockTask);
    when(() => mockRef.putData(any())).thenReturn(mockTask);
    when(() => mockTask.snapshot).thenReturn(mockSnapshot);
    when(() => mockTask.then(any())).thenAnswer((_) async => mockSnapshot);
    when(() => mockSnapshot.state).thenReturn(TaskState.success);
  }

  static void setupDownloadURL(MockReference mockRef, String downloadUrl) {
    when(() => mockRef.getDownloadURL()).thenAnswer((_) async => downloadUrl);
  }

  static void setupDelete(MockReference mockRef) {
    when(() => mockRef.delete()).thenAnswer((_) async {});
  }
}

/// Firebase Messaging Mock Setup
class FirebaseMessagingMockSetup {
  static void setupToken(MockFirebaseMessaging mockMessaging, String token) {
    when(() => mockMessaging.getToken()).thenAnswer((_) async => token);
    when(
      () => mockMessaging.onTokenRefresh,
    ).thenAnswer((_) => Stream.value(token));
  }

  static void setupPermissions(
    MockFirebaseMessaging mockMessaging,
    AuthorizationStatus status,
  ) {
    final settings = NotificationSettings(
      authorizationStatus: status,
      alert: AppleNotificationSetting.enabled,
      announcement: AppleNotificationSetting.enabled,
      badge: AppleNotificationSetting.enabled,
      carPlay: AppleNotificationSetting.enabled,
      lockScreen: AppleNotificationSetting.enabled,
      notificationCenter: AppleNotificationSetting.enabled,
      showPreviews: AppleShowPreviewSetting.always,
      timeSensitive: AppleNotificationSetting.enabled,
      criticalAlert: AppleNotificationSetting.enabled,
      sound: AppleNotificationSetting.enabled,
      providesAppNotificationSettings: AppleNotificationSetting.enabled,
    );

    when(
      () => mockMessaging.requestPermission(),
    ).thenAnswer((_) async => settings);
    when(
      () => mockMessaging.getNotificationSettings(),
    ).thenAnswer((_) async => settings);
  }

  static void setupMessageHandling(
    MockFirebaseMessaging mockMessaging,
    MockRemoteMessage mockMessage,
  ) {
    when(
      () => mockMessaging.getInitialMessage(),
    ).thenAnswer((_) async => mockMessage);
    // Note: onMessage and onMessageOpenedApp are not available in mock
    // when(() => mockMessaging.onMessage).thenAnswer((_) => Stream.value(mockMessage));
    // when(() => mockMessaging.onMessageOpenedApp).thenAnswer((_) => Stream.value(mockMessage));
  }
}

/// Firebase Analytics Mock Setup
class FirebaseAnalyticsMockSetup {
  static void setupAnalytics(MockFirebaseAnalytics mockAnalytics) {
    when(
      () => mockAnalytics.logEvent(
        name: any(named: 'name'),
        parameters: any(named: 'parameters'),
      ),
    ).thenAnswer((_) async {});

    when(
      () => mockAnalytics.setUserId(id: any(named: 'id')),
    ).thenAnswer((_) async {});

    when(
      () => mockAnalytics.setUserProperty(
        name: any(named: 'name'),
        value: any(named: 'value'),
      ),
    ).thenAnswer((_) async {});

    // Note: setCurrentScreen is not available in newer Firebase Analytics versions
    // when(() => mockAnalytics.setCurrentScreen(...)).thenAnswer((_) async {});
  }
}

/// Utility class for creating complete Firebase mock scenarios
class FirebaseMockScenarios {
  /// Sets up a complete authenticated user scenario with Firestore data
  static void setupAuthenticatedUserWithData({
    required MockFirebaseAuth mockAuth,
    required MockUser mockUser,
    required MockFirebaseFirestore mockFirestore,
    required Map<String, dynamic> userData,
    String userId = 'test_user_id',
    String email = '<EMAIL>',
  }) {
    // Setup authentication
    FirebaseAuthMockSetup.setupAuthenticatedUser(
      mockAuth,
      mockUser,
      uid: userId,
      email: email,
    );

    // Note: Firestore setup commented out due to sealed class restrictions
    // Implement direct mocking in individual tests as needed
    /*
    final mockCollection = MockCollectionReference<Map<String, dynamic>>();
    final mockDocument = MockDocumentReference<Map<String, dynamic>>();
    final mockSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();

    FirestoreMockSetup.setupCollection(mockFirestore, mockCollection, 'users');
    FirestoreMockSetup.setupDocument(mockCollection, mockDocument, userId);
    FirestoreMockSetup.setupDocumentGet(mockDocument, mockSnapshot, userData);
    FirestoreMockSetup.setupDocumentSet(mockDocument);
    FirestoreMockSetup.setupDocumentUpdate(mockDocument);
    */
  }

  /// Sets up error scenarios for testing error handling
  /// Note: Commented out due to sealed class restrictions
  /*
  static void setupFirestoreError(
    MockDocumentReference<Map<String, dynamic>> mockDocument,
    String errorMessage,
  ) {
    when(() => mockDocument.get()).thenThrow(
      FirebaseException(
        plugin: 'cloud_firestore',
        code: 'unavailable',
        message: errorMessage,
      ),
    );
  }
  */

  /// Sets up network error scenarios
  static void setupNetworkError(MockFirebaseAuth mockAuth) {
    when(
      () => mockAuth.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenThrow(
      FirebaseAuthException(
        code: 'network-request-failed',
        message: 'Network error occurred',
      ),
    );
  }
}
