import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/certificate/data/models/certificate_model.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';

void main() {
  group('CertificateModel', () {
    group('fromJson', () {
      test('should create CertificateModel from complete JSON', () {
        // Arrange
        final json = {
          'id': 'cert-123',
          'title': 'Level A1 Certificate',
          'description': 'Test certificate description',
          'dateIssued': '2023-12-01T10:00:00Z',
          'certificateUrl': 'https://example.com/cert.pdf',
          'certificateUrlPage2': 'https://example.com/cert-page2.pdf',
          'level': {'id': 'a1', 'name': 'A1', 'description': 'Beginner level'},
          'scores': {
            'pronunciation': 85.5,
            'listening': 90.0,
            'speaking': 88.2,
          },
          'predicates': {
            'pronunciation': 'Good',
            'listening': 'Excellent',
            'speaking': 'Very Good',
          },
        };

        // Act
        final certificate = CertificateModel.fromJson(json);

        // Assert
        expect(certificate.id, 'cert-123');
        expect(certificate.title, 'Level A1 Certificate');
        expect(certificate.description, 'Test certificate description');
        expect(certificate.dateIssued, DateTime.parse('2023-12-01T10:00:00Z'));
        expect(certificate.certificateUrl, 'https://example.com/cert.pdf');
        expect(
          certificate.certificateUrlPage2,
          'https://example.com/cert-page2.pdf',
        );
        expect(certificate.level.id, 'a1');
        expect(certificate.level.name, 'A1');
        expect(certificate.scores?.pronunciation, 85.5);
        expect(certificate.scores?.listening, 90.0);
        expect(certificate.scores?.speaking, 88.2);
        expect(certificate.predicates?.pronunciation, 'Good');
        expect(certificate.predicates?.listening, 'Excellent');
        expect(certificate.predicates?.speaking, 'Very Good');
      });

      test(
        'should create CertificateModel from JSON without optional fields',
        () {
          // Arrange
          final json = {
            'id': 'cert-456',
            'title': 'Level B1 Certificate',
            'description': 'Test certificate description',
            'dateIssued': '2023-12-01T10:00:00Z',
            'certificateUrl': 'https://example.com/cert.pdf',
            'level': {
              'id': 'b1',
              'name': 'B1',
              'description': 'Intermediate level',
            },
            // Missing scores, predicates, and certificateUrlPage2
          };

          // Act
          final certificate = CertificateModel.fromJson(json);

          // Assert
          expect(certificate.id, 'cert-456');
          expect(certificate.title, 'Level B1 Certificate');
          expect(certificate.certificateUrlPage2, isNull);
          expect(certificate.scores, isNull);
          expect(certificate.predicates, isNull);
        },
      );
    });
  });

  group('CertificateScoresModel', () {
    group('fromJson', () {
      test('should create CertificateScoresModel from complete JSON', () {
        // Arrange
        final json = {
          'pronunciation': 85.5,
          'listening': 90.0,
          'speaking': 88.2,
        };

        // Act
        final scores = CertificateScoresModel.fromJson(json);

        // Assert
        expect(scores.pronunciation, 85.5);
        expect(scores.listening, 90.0);
        expect(scores.speaking, 88.2);
      });

      test('should handle integer values by converting to double', () {
        // Arrange
        final json = {'pronunciation': 85, 'listening': 90, 'speaking': 88};

        // Act
        final scores = CertificateScoresModel.fromJson(json);

        // Assert
        expect(scores.pronunciation, 85.0);
        expect(scores.listening, 90.0);
        expect(scores.speaking, 88.0);
      });

      test('should handle null values gracefully', () {
        // Arrange
        final json = {
          'pronunciation': null,
          'listening': 90.0,
          'speaking': null,
        };

        // Act
        final scores = CertificateScoresModel.fromJson(json);

        // Assert
        expect(scores.pronunciation, isNull);
        expect(scores.listening, 90.0);
        expect(scores.speaking, isNull);
      });
    });
  });

  group('CertificatePredicatesModel', () {
    group('fromJson', () {
      test('should create CertificatePredicatesModel from complete JSON', () {
        // Arrange
        final json = {
          'pronunciation': 'Good',
          'listening': 'Excellent',
          'speaking': 'Very Good',
        };

        // Act
        final predicates = CertificatePredicatesModel.fromJson(json);

        // Assert
        expect(predicates.pronunciation, 'Good');
        expect(predicates.listening, 'Excellent');
        expect(predicates.speaking, 'Very Good');
      });

      test('should handle null values gracefully', () {
        // Arrange
        final json = {
          'pronunciation': null,
          'listening': 'Excellent',
          'speaking': null,
        };

        // Act
        final predicates = CertificatePredicatesModel.fromJson(json);

        // Assert
        expect(predicates.pronunciation, isNull);
        expect(predicates.listening, 'Excellent');
        expect(predicates.speaking, isNull);
      });
    });
  });

  group('Certificate entity inheritance', () {
    test('CertificateModel should extend Certificate', () {
      // Arrange
      final json = {
        'id': 'cert-123',
        'title': 'Test Certificate',
        'description': 'Test description',
        'dateIssued': '2023-12-01T10:00:00Z',
        'certificateUrl': 'https://example.com/cert.pdf',
        'level': {'id': 'a1', 'name': 'A1', 'description': 'Beginner level'},
      };

      // Act
      final certificate = CertificateModel.fromJson(json);

      // Assert
      expect(certificate, isA<Certificate>());
    });

    test('CertificateScoresModel should extend CertificateScores', () {
      // Arrange
      final json = {'pronunciation': 85.5, 'listening': 90.0, 'speaking': 88.2};

      // Act
      final scores = CertificateScoresModel.fromJson(json);

      // Assert
      expect(scores, isA<CertificateScores>());
    });

    test('CertificatePredicatesModel should extend CertificatePredicates', () {
      // Arrange
      final json = {
        'pronunciation': 'Good',
        'listening': 'Excellent',
        'speaking': 'Very Good',
      };

      // Act
      final predicates = CertificatePredicatesModel.fromJson(json);

      // Assert
      expect(predicates, isA<CertificatePredicates>());
    });
  });
}
