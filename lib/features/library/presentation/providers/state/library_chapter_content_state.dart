import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';

part 'library_chapter_content_state.freezed.dart';

@freezed
abstract class LibraryChapterContentState with _$LibraryChapterContentState {
  factory LibraryChapterContentState({
    ChapterData? chapter,
    // List<ContentIndexData>? pronunciationContent,
    // List<ContentIndexData>? conversationContent,
    // List<ContentIndexData>? listeningContent,
    // List<ContentIndexData>? speakingContent,
  }) = _LibraryChapterContentState;

  // Allow custom getters / setters
  const LibraryChapterContentState._();
}
