// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class GlassEffectAppBar extends StatelessWidget {
  const GlassEffectAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.isBookmarked = false,
    this.onBookmark,
    this.onHelp,
  });
  final String title;
  final String? subtitle;
  final bool isBookmarked;
  final VoidCallback? onBookmark;
  final VoidCallback? onHelp;

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 2, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: const Color(0xffEBEBEB).withValues(alpha: 0.2),
              width: 1,
            ),
            // color: Color(0xffEBEBEB).withValues(alpha: 0.2),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Color(0xffB4A9A7),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                        color: const Color(0xffEDE0DE),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (subtitle != null)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 22,
                          vertical: 10,
                        ),
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Color(0xffFA3236),
                              Color(0xffD9353C),
                              Color(0xff8C1412),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        ),
                        child: Text(
                          subtitle!,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.white),
                          maxLines: 2,
                          softWrap: true,
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.help_outline,
                      color: Color(0xffB4A9A7),
                    ),
                    onPressed: onHelp ?? () {},
                  ),
                  IconButton(
                    icon: Icon(
                      isBookmarked
                          ? Icons.bookmark_outlined
                          : Icons.bookmark_border_outlined,
                      color: Color(0xffB4A9A7),
                    ),
                    onPressed: onBookmark ?? () {},
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize {
    final subtitleHeight = subtitle != null ? 100.0 : 0.0;
    return Size.fromHeight(kToolbarHeight + subtitleHeight);
  }
}
