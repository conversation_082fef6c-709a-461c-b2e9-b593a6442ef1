import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:riverpod/riverpod.dart';
import 'package:selfeng/features/authentication/presentation/providers/state/auth_state.dart';
import 'package:selfeng/features/authentication/presentation/providers/auth_controller.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart' as app_user;
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/data/local/shared_prefs_storage_service.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';

// Mock classes
class MockUserRepository extends Mock implements UserRepository {}

class MockAuthRepository extends Mock implements AuthRepository {}

class MockGoogleSignInRepository extends Mock
    implements GoogleSignInRepository {}

class MockSharedPrefsService extends Mock implements SharedPrefsService {}

class MockFirebaseUserCredential extends Mock
    implements firebase_auth.UserCredential {}

class MockFirebaseUser extends Mock implements firebase_auth.User {}

class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}

class MockGoogleSignInAuthentication extends Mock
    implements GoogleSignInAuthentication {}

void main() {
  group('AuthController Comprehensive Tests', () {
    late MockUserRepository mockUserRepository;
    late MockAuthRepository mockAuthRepository;
    late MockGoogleSignInRepository mockGoogleSignInRepository;
    late MockSharedPrefsService mockSharedPrefsService;
    ProviderContainer? container;

    final testUser = app_user.User(
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      image: 'https://example.com/image.jpg',
      token: 'test_token',
    );

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockAuthRepository = MockAuthRepository();
      mockGoogleSignInRepository = MockGoogleSignInRepository();
      mockSharedPrefsService = MockSharedPrefsService();

      // Register fallback values for mocktail
      registerFallbackValue(testUser);
      registerFallbackValue(const AuthConfig());

      // Setup default mock behaviors
      when(() => mockUserRepository.deleteUser()).thenAnswer((_) async => true);
      when(
        () => mockUserRepository.saveUser(user: any(named: 'user')),
      ).thenAnswer((_) async => true);
      when(() => mockUserRepository.hasUser()).thenAnswer((_) async => false);

      // Setup auth repository mocks
      when(() => mockAuthRepository.currentUser).thenReturn(null);
      when(() => mockAuthRepository.signOut()).thenAnswer((_) async {});
      when(
        () => mockAuthRepository.getIdToken(any()),
      ).thenAnswer((_) async => 'test_token');

      // Setup Google Sign In repository mocks
      when(
        () => mockGoogleSignInRepository.supportsAuthenticate(),
      ).thenReturn(true);
      when(
        () => mockGoogleSignInRepository.authenticate(),
      ).thenAnswer((_) async => MockGoogleSignInAccount());
      when(
        () => mockGoogleSignInRepository.disconnect(),
      ).thenAnswer((_) async {});
      when(
        () => mockGoogleSignInRepository.initialize(),
      ).thenAnswer((_) async {});
      when(() => mockGoogleSignInRepository.signOut()).thenAnswer((_) async {});
      when(
        () => mockGoogleSignInRepository.authenticationEvents,
      ).thenAnswer((_) => const Stream.empty());

      // Setup shared prefs service mocks
      when(
        () => mockSharedPrefsService.has(any()),
      ).thenAnswer((_) async => false);
      when(
        () => mockSharedPrefsService.set(any(), any()),
      ).thenAnswer((_) async => true);
    });

    tearDown(() {
      container?.dispose();
    });

    group('Configuration', () {
      test('should use custom configuration', () {
        final config = AuthConfig(
          googleSignInTimeout: const Duration(seconds: 10),
          googleSignInRetryDelay: const Duration(milliseconds: 100),
          initializationDelay: const Duration(seconds: 1),
          rethrowGoogleSignInErrors: true,
          rethrowInitializationErrors: true,
          rethrowNotificationErrors: true,
        );

        final controller = AuthController.test(
          userRepository: mockUserRepository,
          authRepository: mockAuthRepository,
          googleSignInRepository: mockGoogleSignInRepository,
          config: config,
        );

        // We can't directly access _config, but we can test behavior
        expect(controller, isA<AuthController>());
      });
    });

    group('AuthState Tests', () {
      test('should create initial state', () {
        const state = AuthState.initial();
        expect(state, isA<Initial>());
      });

      test('should create loading state', () {
        const state = AuthState.loading();
        expect(state, isA<Loading>());
      });

      test('should create signed in state', () {
        final state = AuthState.signedIn(user: testUser);
        expect(state, isA<SignedIn>());
        expect((state as SignedIn).user, equals(testUser));
      });

      test('should create signed out state', () {
        const state = AuthState.signedOut();
        expect(state, isA<SignedOut>());
      });

      test('should create failure state', () {
        final exception = AppException(
          message: 'Test error',
          statusCode: 400,
          identifier: 'TEST_ERROR',
        );
        final state = AuthState.failure(exception: exception);
        expect(state, isA<Failure>());
        expect((state as Failure).exception, equals(exception));
      });

      test('should create success state', () {
        const state = AuthState.success();
        expect(state, isA<Success>());
      });

      test('should correctly identify auth status', () {
        final signedInState = AuthState.signedIn(user: testUser);
        final signedOutState = AuthState.signedOut();

        expect(signedInState.isAuth, isTrue);
        expect(signedOutState.isAuth, isFalse);
      });
    });

    group('Repository Integration', () {
      test('should handle user save operation', () async {
        when(
          () => mockUserRepository.saveUser(user: any(named: 'user')),
        ).thenAnswer((_) async => true);

        final result = await mockUserRepository.saveUser(user: testUser);
        expect(result, isTrue);
        verify(() => mockUserRepository.saveUser(user: testUser)).called(1);
      });

      test('should handle user delete operation', () async {
        when(
          () => mockUserRepository.deleteUser(),
        ).thenAnswer((_) async => true);

        final result = await mockUserRepository.deleteUser();
        expect(result, isTrue);
        verify(() => mockUserRepository.deleteUser()).called(1);
      });
    });

    group('AuthController Functionality', () {
      test('should create AuthController with test constructor', () {
        final controller = AuthController.test(
          userRepository: mockUserRepository,
          authRepository: mockAuthRepository,
          googleSignInRepository: mockGoogleSignInRepository,
        );

        expect(controller, isA<AuthController>());
      });

      test('should verify repository methods are called for logout', () async {
        when(() => mockAuthRepository.signOut()).thenAnswer((_) async {});
        when(
          () => mockGoogleSignInRepository.disconnect(),
        ).thenAnswer((_) async {});
        when(
          () => mockUserRepository.deleteUser(),
        ).thenAnswer((_) async => true);

        // Test that the individual methods are called correctly
        // without testing the state management part

        // Test auth repository signOut
        await mockAuthRepository.signOut();
        verify(() => mockAuthRepository.signOut()).called(1);

        // Test google sign in disconnect
        await mockGoogleSignInRepository.disconnect();
        verify(() => mockGoogleSignInRepository.disconnect()).called(1);

        // Test user repository deleteUser
        await mockUserRepository.deleteUser();
        verify(() => mockUserRepository.deleteUser()).called(1);
      });

      test('should verify repository methods are called for login', () async {
        when(
          () => mockGoogleSignInRepository.supportsAuthenticate(),
        ).thenReturn(true);
        when(
          () => mockGoogleSignInRepository.authenticate(),
        ).thenAnswer((_) async => MockGoogleSignInAccount());

        // Test that the individual methods are called correctly

        // Test google sign in supportsAuthenticate
        final supportsAuth = mockGoogleSignInRepository.supportsAuthenticate();
        expect(supportsAuth, isTrue);
        verify(
          () => mockGoogleSignInRepository.supportsAuthenticate(),
        ).called(1);

        // Test google sign in authenticate
        await mockGoogleSignInRepository.authenticate();
        verify(() => mockGoogleSignInRepository.authenticate()).called(1);
      });

      test('should initialize with default config', () {
        final config = AuthConfig();
        expect(config.googleSignInTimeout, const Duration(seconds: 30));
        expect(
          config.googleSignInRetryDelay,
          const Duration(milliseconds: 500),
        );
        expect(config.initializationDelay, const Duration(seconds: 3));
        expect(config.rethrowGoogleSignInErrors, isFalse);
        expect(config.rethrowInitializationErrors, isFalse);
        expect(config.rethrowNotificationErrors, isFalse);
      });

      test('should allow custom config', () {
        final config = AuthConfig(
          googleSignInTimeout: const Duration(seconds: 10),
          googleSignInRetryDelay: const Duration(milliseconds: 10),
          initializationDelay: const Duration(seconds: 1),
          rethrowGoogleSignInErrors: true,
          rethrowInitializationErrors: true,
          rethrowNotificationErrors: true,
        );

        expect(config.googleSignInTimeout, const Duration(seconds: 10));
        expect(config.googleSignInRetryDelay, const Duration(milliseconds: 10));
        expect(config.initializationDelay, const Duration(seconds: 1));
        expect(config.rethrowGoogleSignInErrors, isTrue);
        expect(config.rethrowInitializationErrors, isTrue);
        expect(config.rethrowNotificationErrors, isTrue);
      });
    });
  });
}
