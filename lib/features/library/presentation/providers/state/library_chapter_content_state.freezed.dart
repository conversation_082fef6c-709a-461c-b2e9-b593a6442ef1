// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'library_chapter_content_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$LibraryChapterContentState {

 ChapterData? get chapter;
/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LibraryChapterContentStateCopyWith<LibraryChapterContentState> get copyWith => _$LibraryChapterContentStateCopyWithImpl<LibraryChapterContentState>(this as LibraryChapterContentState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LibraryChapterContentState&&(identical(other.chapter, chapter) || other.chapter == chapter));
}


@override
int get hashCode => Object.hash(runtimeType,chapter);

@override
String toString() {
  return 'LibraryChapterContentState(chapter: $chapter)';
}


}

/// @nodoc
abstract mixin class $LibraryChapterContentStateCopyWith<$Res>  {
  factory $LibraryChapterContentStateCopyWith(LibraryChapterContentState value, $Res Function(LibraryChapterContentState) _then) = _$LibraryChapterContentStateCopyWithImpl;
@useResult
$Res call({
 ChapterData? chapter
});


$ChapterDataCopyWith<$Res>? get chapter;

}
/// @nodoc
class _$LibraryChapterContentStateCopyWithImpl<$Res>
    implements $LibraryChapterContentStateCopyWith<$Res> {
  _$LibraryChapterContentStateCopyWithImpl(this._self, this._then);

  final LibraryChapterContentState _self;
  final $Res Function(LibraryChapterContentState) _then;

/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chapter = freezed,}) {
  return _then(_self.copyWith(
chapter: freezed == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as ChapterData?,
  ));
}
/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDataCopyWith<$Res>? get chapter {
    if (_self.chapter == null) {
    return null;
  }

  return $ChapterDataCopyWith<$Res>(_self.chapter!, (value) {
    return _then(_self.copyWith(chapter: value));
  });
}
}


/// Adds pattern-matching-related methods to [LibraryChapterContentState].
extension LibraryChapterContentStatePatterns on LibraryChapterContentState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LibraryChapterContentState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LibraryChapterContentState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LibraryChapterContentState value)  $default,){
final _that = this;
switch (_that) {
case _LibraryChapterContentState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LibraryChapterContentState value)?  $default,){
final _that = this;
switch (_that) {
case _LibraryChapterContentState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ChapterData? chapter)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LibraryChapterContentState() when $default != null:
return $default(_that.chapter);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ChapterData? chapter)  $default,) {final _that = this;
switch (_that) {
case _LibraryChapterContentState():
return $default(_that.chapter);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ChapterData? chapter)?  $default,) {final _that = this;
switch (_that) {
case _LibraryChapterContentState() when $default != null:
return $default(_that.chapter);case _:
  return null;

}
}

}

/// @nodoc


class _LibraryChapterContentState extends LibraryChapterContentState {
   _LibraryChapterContentState({this.chapter}): super._();
  

@override final  ChapterData? chapter;

/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LibraryChapterContentStateCopyWith<_LibraryChapterContentState> get copyWith => __$LibraryChapterContentStateCopyWithImpl<_LibraryChapterContentState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LibraryChapterContentState&&(identical(other.chapter, chapter) || other.chapter == chapter));
}


@override
int get hashCode => Object.hash(runtimeType,chapter);

@override
String toString() {
  return 'LibraryChapterContentState(chapter: $chapter)';
}


}

/// @nodoc
abstract mixin class _$LibraryChapterContentStateCopyWith<$Res> implements $LibraryChapterContentStateCopyWith<$Res> {
  factory _$LibraryChapterContentStateCopyWith(_LibraryChapterContentState value, $Res Function(_LibraryChapterContentState) _then) = __$LibraryChapterContentStateCopyWithImpl;
@override @useResult
$Res call({
 ChapterData? chapter
});


@override $ChapterDataCopyWith<$Res>? get chapter;

}
/// @nodoc
class __$LibraryChapterContentStateCopyWithImpl<$Res>
    implements _$LibraryChapterContentStateCopyWith<$Res> {
  __$LibraryChapterContentStateCopyWithImpl(this._self, this._then);

  final _LibraryChapterContentState _self;
  final $Res Function(_LibraryChapterContentState) _then;

/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chapter = freezed,}) {
  return _then(_LibraryChapterContentState(
chapter: freezed == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as ChapterData?,
  ));
}

/// Create a copy of LibraryChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDataCopyWith<$Res>? get chapter {
    if (_self.chapter == null) {
    return null;
  }

  return $ChapterDataCopyWith<$Res>(_self.chapter!, (value) {
    return _then(_self.copyWith(chapter: value));
  });
}
}

// dart format on
