import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:selfeng/features/authentication/data/repositories/auth_repository_impl.dart';
import '../../../../mocks/firebase_mocks.dart';

void main() {
  group('AuthRepositoryImpl', () {
    late AuthRepositoryImpl repository;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockAuthCredential mockCredential;
    late MockUserCredential mockUserCredential;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
      mockCredential = MockAuthCredential();
      mockUserCredential = MockUserCredential();

      repository = AuthRepositoryImpl(mockFirebaseAuth);
    });

    group('currentUser', () {
      test('should return current user from FirebaseAuth', () {
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);

        final currentUser = repository.currentUser;

        expect(currentUser, mockUser);
        verify(() => mockFirebaseAuth.currentUser).called(1);
      });

      test('should return null when no current user', () {
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        final currentUser = repository.currentUser;

        expect(currentUser, isNull);
        verify(() => mockFirebaseAuth.currentUser).called(1);
      });
    });

    group('signOut', () {
      test('should call signOut on FirebaseAuth', () async {
        when(() => mockFirebaseAuth.signOut()).thenAnswer((_) async {});

        await repository.signOut();
        verify(() => mockFirebaseAuth.signOut()).called(1);
      });

      test('should propagate FirebaseAuth exceptions', () async {
        final exception = firebase_auth.FirebaseAuthException(
          code: 'ERROR_USER_TOKEN_EXPIRED',
          message: 'The user\'s credential is no longer valid.',
        );

        when(() => mockFirebaseAuth.signOut()).thenThrow(exception);

        expect(
          () => repository.signOut(),
          throwsA(isA<firebase_auth.FirebaseAuthException>()),
        );
        verify(() => mockFirebaseAuth.signOut()).called(1);
      });
    });

    group('signInWithCredential', () {
      test('should call signInWithCredential on FirebaseAuth', () async {
        when(
          () => mockFirebaseAuth.signInWithCredential(mockCredential),
        ).thenAnswer((_) async => mockUserCredential);

        final result = await repository.signInWithCredential(mockCredential);

        expect(result, mockUserCredential);
        verify(
          () => mockFirebaseAuth.signInWithCredential(mockCredential),
        ).called(1);
      });

      test('should propagate FirebaseAuth exceptions', () {
        final exception = firebase_auth.FirebaseAuthException(
          code: 'ERROR_INVALID_CREDENTIAL',
          message: 'The supplied auth credential is malformed or has expired.',
        );

        when(
          () => mockFirebaseAuth.signInWithCredential(mockCredential),
        ).thenThrow(exception);

        expect(
          () => repository.signInWithCredential(mockCredential),
          throwsA(isA<firebase_auth.FirebaseAuthException>()),
        );
        verify(
          () => mockFirebaseAuth.signInWithCredential(mockCredential),
        ).called(1);
      });
    });

    group('getIdToken', () {
      test('should return ID token when user is authenticated', () async {
        const token = 'test_id_token';
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.getIdToken(any())).thenAnswer((_) async => token);

        final result = await repository.getIdToken(false);

        expect(result, token);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(() => mockUser.getIdToken(false)).called(1);
      });

      test('should return null when no user is authenticated', () async {
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        final result = await repository.getIdToken(false);

        expect(result, isNull);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verifyNever(() => mockUser.getIdToken(any()));
      });

      test('should propagate exceptions when getting ID token fails', () async {
        final exception = firebase_auth.FirebaseAuthException(
          code: 'ERROR_USER_TOKEN_EXPIRED',
          message: 'The user\'s credential is no longer valid.',
        );

        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.getIdToken(any())).thenThrow(exception);

        expect(
          () => repository.getIdToken(false),
          throwsA(isA<firebase_auth.FirebaseAuthException>()),
        );
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(() => mockUser.getIdToken(false)).called(1);
      });

      test('should pass forceRefresh parameter correctly', () async {
        const token = 'test_id_token';
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.getIdToken(true)).thenAnswer((_) async => token);

        final result = await repository.getIdToken(true);

        expect(result, token);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(() => mockUser.getIdToken(true)).called(1);
      });
    });
  });
}
