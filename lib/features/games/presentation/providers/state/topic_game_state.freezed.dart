// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'topic_game_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TopicGameState {

 GameStage get gameStage; List<MemoryFlashTopic> get topics; int get currentTopicIndex; bool get shouldAdvanceToNextTopic;
/// Create a copy of TopicGameState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TopicGameStateCopyWith<TopicGameState> get copyWith => _$TopicGameStateCopyWithImpl<TopicGameState>(this as TopicGameState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TopicGameState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other.topics, topics)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex)&&(identical(other.shouldAdvanceToNextTopic, shouldAdvanceToNextTopic) || other.shouldAdvanceToNextTopic == shouldAdvanceToNextTopic));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(topics),currentTopicIndex,shouldAdvanceToNextTopic);

@override
String toString() {
  return 'TopicGameState(gameStage: $gameStage, topics: $topics, currentTopicIndex: $currentTopicIndex, shouldAdvanceToNextTopic: $shouldAdvanceToNextTopic)';
}


}

/// @nodoc
abstract mixin class $TopicGameStateCopyWith<$Res>  {
  factory $TopicGameStateCopyWith(TopicGameState value, $Res Function(TopicGameState) _then) = _$TopicGameStateCopyWithImpl;
@useResult
$Res call({
 GameStage gameStage, List<MemoryFlashTopic> topics, int currentTopicIndex, bool shouldAdvanceToNextTopic
});




}
/// @nodoc
class _$TopicGameStateCopyWithImpl<$Res>
    implements $TopicGameStateCopyWith<$Res> {
  _$TopicGameStateCopyWithImpl(this._self, this._then);

  final TopicGameState _self;
  final $Res Function(TopicGameState) _then;

/// Create a copy of TopicGameState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? gameStage = null,Object? topics = null,Object? currentTopicIndex = null,Object? shouldAdvanceToNextTopic = null,}) {
  return _then(_self.copyWith(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,topics: null == topics ? _self.topics : topics // ignore: cast_nullable_to_non_nullable
as List<MemoryFlashTopic>,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,shouldAdvanceToNextTopic: null == shouldAdvanceToNextTopic ? _self.shouldAdvanceToNextTopic : shouldAdvanceToNextTopic // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [TopicGameState].
extension TopicGameStatePatterns on TopicGameState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TopicGameState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TopicGameState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TopicGameState value)  $default,){
final _that = this;
switch (_that) {
case _TopicGameState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TopicGameState value)?  $default,){
final _that = this;
switch (_that) {
case _TopicGameState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( GameStage gameStage,  List<MemoryFlashTopic> topics,  int currentTopicIndex,  bool shouldAdvanceToNextTopic)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TopicGameState() when $default != null:
return $default(_that.gameStage,_that.topics,_that.currentTopicIndex,_that.shouldAdvanceToNextTopic);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( GameStage gameStage,  List<MemoryFlashTopic> topics,  int currentTopicIndex,  bool shouldAdvanceToNextTopic)  $default,) {final _that = this;
switch (_that) {
case _TopicGameState():
return $default(_that.gameStage,_that.topics,_that.currentTopicIndex,_that.shouldAdvanceToNextTopic);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( GameStage gameStage,  List<MemoryFlashTopic> topics,  int currentTopicIndex,  bool shouldAdvanceToNextTopic)?  $default,) {final _that = this;
switch (_that) {
case _TopicGameState() when $default != null:
return $default(_that.gameStage,_that.topics,_that.currentTopicIndex,_that.shouldAdvanceToNextTopic);case _:
  return null;

}
}

}

/// @nodoc


class _TopicGameState implements TopicGameState {
   _TopicGameState({this.gameStage = GameStage.topic, final  List<MemoryFlashTopic> topics = const [], this.currentTopicIndex = 0, this.shouldAdvanceToNextTopic = false}): _topics = topics;
  

@override@JsonKey() final  GameStage gameStage;
 final  List<MemoryFlashTopic> _topics;
@override@JsonKey() List<MemoryFlashTopic> get topics {
  if (_topics is EqualUnmodifiableListView) return _topics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topics);
}

@override@JsonKey() final  int currentTopicIndex;
@override@JsonKey() final  bool shouldAdvanceToNextTopic;

/// Create a copy of TopicGameState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TopicGameStateCopyWith<_TopicGameState> get copyWith => __$TopicGameStateCopyWithImpl<_TopicGameState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TopicGameState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other._topics, _topics)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex)&&(identical(other.shouldAdvanceToNextTopic, shouldAdvanceToNextTopic) || other.shouldAdvanceToNextTopic == shouldAdvanceToNextTopic));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(_topics),currentTopicIndex,shouldAdvanceToNextTopic);

@override
String toString() {
  return 'TopicGameState(gameStage: $gameStage, topics: $topics, currentTopicIndex: $currentTopicIndex, shouldAdvanceToNextTopic: $shouldAdvanceToNextTopic)';
}


}

/// @nodoc
abstract mixin class _$TopicGameStateCopyWith<$Res> implements $TopicGameStateCopyWith<$Res> {
  factory _$TopicGameStateCopyWith(_TopicGameState value, $Res Function(_TopicGameState) _then) = __$TopicGameStateCopyWithImpl;
@override @useResult
$Res call({
 GameStage gameStage, List<MemoryFlashTopic> topics, int currentTopicIndex, bool shouldAdvanceToNextTopic
});




}
/// @nodoc
class __$TopicGameStateCopyWithImpl<$Res>
    implements _$TopicGameStateCopyWith<$Res> {
  __$TopicGameStateCopyWithImpl(this._self, this._then);

  final _TopicGameState _self;
  final $Res Function(_TopicGameState) _then;

/// Create a copy of TopicGameState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? gameStage = null,Object? topics = null,Object? currentTopicIndex = null,Object? shouldAdvanceToNextTopic = null,}) {
  return _then(_TopicGameState(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,topics: null == topics ? _self._topics : topics // ignore: cast_nullable_to_non_nullable
as List<MemoryFlashTopic>,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,shouldAdvanceToNextTopic: null == shouldAdvanceToNextTopic ? _self.shouldAdvanceToNextTopic : shouldAdvanceToNextTopic // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
