// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chapter_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChapterData {

@Json<PERSON>ey(name: 'chapter') int get chapter;@JsonKey(name: 'label') String get label;@JsonKey(name: 'description') ChapterDescription get description;@JsonKey(name: 'image') String get image;@JsonKey(name: 'image_url') String get imageUrl;
/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChapterDataCopyWith<ChapterData> get copyWith => _$ChapterDataCopyWithImpl<ChapterData>(this as ChapterData, _$identity);

  /// Serializes this ChapterData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChapterData&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.label, label) || other.label == label)&&(identical(other.description, description) || other.description == description)&&(identical(other.image, image) || other.image == image)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chapter,label,description,image,imageUrl);

@override
String toString() {
  return 'ChapterData(chapter: $chapter, label: $label, description: $description, image: $image, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class $ChapterDataCopyWith<$Res>  {
  factory $ChapterDataCopyWith(ChapterData value, $Res Function(ChapterData) _then) = _$ChapterDataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'chapter') int chapter,@JsonKey(name: 'label') String label,@JsonKey(name: 'description') ChapterDescription description,@JsonKey(name: 'image') String image,@JsonKey(name: 'image_url') String imageUrl
});


$ChapterDescriptionCopyWith<$Res> get description;

}
/// @nodoc
class _$ChapterDataCopyWithImpl<$Res>
    implements $ChapterDataCopyWith<$Res> {
  _$ChapterDataCopyWithImpl(this._self, this._then);

  final ChapterData _self;
  final $Res Function(ChapterData) _then;

/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chapter = null,Object? label = null,Object? description = null,Object? image = null,Object? imageUrl = null,}) {
  return _then(_self.copyWith(
chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as ChapterDescription,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}
/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDescriptionCopyWith<$Res> get description {
  
  return $ChapterDescriptionCopyWith<$Res>(_self.description, (value) {
    return _then(_self.copyWith(description: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChapterData].
extension ChapterDataPatterns on ChapterData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChapterData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChapterData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChapterData value)  $default,){
final _that = this;
switch (_that) {
case _ChapterData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChapterData value)?  $default,){
final _that = this;
switch (_that) {
case _ChapterData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'chapter')  int chapter, @JsonKey(name: 'label')  String label, @JsonKey(name: 'description')  ChapterDescription description, @JsonKey(name: 'image')  String image, @JsonKey(name: 'image_url')  String imageUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChapterData() when $default != null:
return $default(_that.chapter,_that.label,_that.description,_that.image,_that.imageUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'chapter')  int chapter, @JsonKey(name: 'label')  String label, @JsonKey(name: 'description')  ChapterDescription description, @JsonKey(name: 'image')  String image, @JsonKey(name: 'image_url')  String imageUrl)  $default,) {final _that = this;
switch (_that) {
case _ChapterData():
return $default(_that.chapter,_that.label,_that.description,_that.image,_that.imageUrl);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'chapter')  int chapter, @JsonKey(name: 'label')  String label, @JsonKey(name: 'description')  ChapterDescription description, @JsonKey(name: 'image')  String image, @JsonKey(name: 'image_url')  String imageUrl)?  $default,) {final _that = this;
switch (_that) {
case _ChapterData() when $default != null:
return $default(_that.chapter,_that.label,_that.description,_that.image,_that.imageUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChapterData implements ChapterData {
  const _ChapterData({@JsonKey(name: 'chapter') required this.chapter, @JsonKey(name: 'label') required this.label, @JsonKey(name: 'description') required this.description, @JsonKey(name: 'image') required this.image, @JsonKey(name: 'image_url') required this.imageUrl});
  factory _ChapterData.fromJson(Map<String, dynamic> json) => _$ChapterDataFromJson(json);

@override@JsonKey(name: 'chapter') final  int chapter;
@override@JsonKey(name: 'label') final  String label;
@override@JsonKey(name: 'description') final  ChapterDescription description;
@override@JsonKey(name: 'image') final  String image;
@override@JsonKey(name: 'image_url') final  String imageUrl;

/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChapterDataCopyWith<_ChapterData> get copyWith => __$ChapterDataCopyWithImpl<_ChapterData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChapterDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChapterData&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.label, label) || other.label == label)&&(identical(other.description, description) || other.description == description)&&(identical(other.image, image) || other.image == image)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chapter,label,description,image,imageUrl);

@override
String toString() {
  return 'ChapterData(chapter: $chapter, label: $label, description: $description, image: $image, imageUrl: $imageUrl)';
}


}

/// @nodoc
abstract mixin class _$ChapterDataCopyWith<$Res> implements $ChapterDataCopyWith<$Res> {
  factory _$ChapterDataCopyWith(_ChapterData value, $Res Function(_ChapterData) _then) = __$ChapterDataCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'chapter') int chapter,@JsonKey(name: 'label') String label,@JsonKey(name: 'description') ChapterDescription description,@JsonKey(name: 'image') String image,@JsonKey(name: 'image_url') String imageUrl
});


@override $ChapterDescriptionCopyWith<$Res> get description;

}
/// @nodoc
class __$ChapterDataCopyWithImpl<$Res>
    implements _$ChapterDataCopyWith<$Res> {
  __$ChapterDataCopyWithImpl(this._self, this._then);

  final _ChapterData _self;
  final $Res Function(_ChapterData) _then;

/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chapter = null,Object? label = null,Object? description = null,Object? image = null,Object? imageUrl = null,}) {
  return _then(_ChapterData(
chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as ChapterDescription,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of ChapterData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDescriptionCopyWith<$Res> get description {
  
  return $ChapterDescriptionCopyWith<$Res>(_self.description, (value) {
    return _then(_self.copyWith(description: value));
  });
}
}


/// @nodoc
mixin _$ChapterDescription {

@JsonKey(name: 'en') String get en;@JsonKey(name: 'id') String get id;
/// Create a copy of ChapterDescription
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChapterDescriptionCopyWith<ChapterDescription> get copyWith => _$ChapterDescriptionCopyWithImpl<ChapterDescription>(this as ChapterDescription, _$identity);

  /// Serializes this ChapterDescription to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChapterDescription&&(identical(other.en, en) || other.en == en)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,en,id);

@override
String toString() {
  return 'ChapterDescription(en: $en, id: $id)';
}


}

/// @nodoc
abstract mixin class $ChapterDescriptionCopyWith<$Res>  {
  factory $ChapterDescriptionCopyWith(ChapterDescription value, $Res Function(ChapterDescription) _then) = _$ChapterDescriptionCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'en') String en,@JsonKey(name: 'id') String id
});




}
/// @nodoc
class _$ChapterDescriptionCopyWithImpl<$Res>
    implements $ChapterDescriptionCopyWith<$Res> {
  _$ChapterDescriptionCopyWithImpl(this._self, this._then);

  final ChapterDescription _self;
  final $Res Function(ChapterDescription) _then;

/// Create a copy of ChapterDescription
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? en = null,Object? id = null,}) {
  return _then(_self.copyWith(
en: null == en ? _self.en : en // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ChapterDescription].
extension ChapterDescriptionPatterns on ChapterDescription {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChapterDescription value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChapterDescription() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChapterDescription value)  $default,){
final _that = this;
switch (_that) {
case _ChapterDescription():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChapterDescription value)?  $default,){
final _that = this;
switch (_that) {
case _ChapterDescription() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'en')  String en, @JsonKey(name: 'id')  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChapterDescription() when $default != null:
return $default(_that.en,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'en')  String en, @JsonKey(name: 'id')  String id)  $default,) {final _that = this;
switch (_that) {
case _ChapterDescription():
return $default(_that.en,_that.id);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'en')  String en, @JsonKey(name: 'id')  String id)?  $default,) {final _that = this;
switch (_that) {
case _ChapterDescription() when $default != null:
return $default(_that.en,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChapterDescription implements ChapterDescription {
  const _ChapterDescription({@JsonKey(name: 'en') required this.en, @JsonKey(name: 'id') required this.id});
  factory _ChapterDescription.fromJson(Map<String, dynamic> json) => _$ChapterDescriptionFromJson(json);

@override@JsonKey(name: 'en') final  String en;
@override@JsonKey(name: 'id') final  String id;

/// Create a copy of ChapterDescription
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChapterDescriptionCopyWith<_ChapterDescription> get copyWith => __$ChapterDescriptionCopyWithImpl<_ChapterDescription>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChapterDescriptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChapterDescription&&(identical(other.en, en) || other.en == en)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,en,id);

@override
String toString() {
  return 'ChapterDescription(en: $en, id: $id)';
}


}

/// @nodoc
abstract mixin class _$ChapterDescriptionCopyWith<$Res> implements $ChapterDescriptionCopyWith<$Res> {
  factory _$ChapterDescriptionCopyWith(_ChapterDescription value, $Res Function(_ChapterDescription) _then) = __$ChapterDescriptionCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'en') String en,@JsonKey(name: 'id') String id
});




}
/// @nodoc
class __$ChapterDescriptionCopyWithImpl<$Res>
    implements _$ChapterDescriptionCopyWith<$Res> {
  __$ChapterDescriptionCopyWithImpl(this._self, this._then);

  final _ChapterDescription _self;
  final $Res Function(_ChapterDescription) _then;

/// Create a copy of ChapterDescription
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? en = null,Object? id = null,}) {
  return _then(_ChapterDescription(
en: null == en ? _self.en : en // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
