import 'package:flutter/material.dart';
import 'dart:math' as math;

class CircularProgressIndicatorCustom extends StatelessWidget {
  final int currentValue;
  final int totalValue;
  final double size;
  final Color backgroundColor;
  final Color progressColor;

  const CircularProgressIndicatorCustom({
    super.key,
    required this.currentValue,
    required this.totalValue,
    this.size = 36.0,
    this.backgroundColor = Colors.grey,
    this.progressColor = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    final progress = currentValue / totalValue;
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Color(0xffD0C4C2),
        shape: BoxShape.circle,
      ),
      margin: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      child: CustomPaint(
        painter: _CircularProgressPainter(
          progress: progress,
          backgroundColor: backgroundColor,
          progressColor: progressColor,
        ),
        child: Center(
          child: Text(
            '$currentValue/$totalValue',
            style: Theme.of(
              context,
            ).textTheme.labelSmall!.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color backgroundColor;
  final Color progressColor;

  _CircularProgressPainter({
    required this.progress,
    required this.backgroundColor,
    required this.progressColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 3;

    // Draw background circle
    canvas.drawCircle(center, radius, Paint()..color = backgroundColor);

    // Draw progress arc
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      progress * 2 * math.pi,
      false,
      Paint()
        ..color = progressColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
