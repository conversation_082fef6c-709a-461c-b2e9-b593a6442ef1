import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';

part 'diagnostic_test_state.freezed.dart';

@freezed
abstract class DiagnosticTestState with _$DiagnosticTestState {
  factory DiagnosticTestState({
    @Default([]) List<QuestionModel> questions,
    QuestionResultModel? result,
    @Default(0) int currentPage,
    @Default(false) bool expandedResult,
    ScrollController? controllerScroll,
  }) = _DiagnosticTestState;

  // Allow custom getters / setters
  const DiagnosticTestState._();

  int get questionLength => questions.length - 1;
  // Custom getter to check if all questions are answered
  bool get allQuestionsAnswered => questions.every(
    (question) => question.state == QuestionConcreteState.answered,
  );

  int get correctCount =>
      questions.where((question) => question.isCorrect).length;
}
