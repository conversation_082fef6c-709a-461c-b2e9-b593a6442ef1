import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/level.dart';

part 'learning_material_state.freezed.dart';

@freezed
sealed class LearningMaterialState with _$LearningMaterialState {
  const factory LearningMaterialState({
    @Default(<ChapterData>[]) List<ChapterData> chapters,
    @Default(Level.a1) Level fetchedLevel,
  }) = _LearningMaterialState;
}
