import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/pronunciation_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/pronunciation_state.dart';
import 'package:selfeng/shared/widgets/repeat_next_button.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:lottie/lottie.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class PronunciationChallengeContentResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const PronunciationChallengeContentResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<PronunciationChallengeContentResultScreen> createState() =>
      _PronunciationChallengeContentResultScreenState();
}

class _PronunciationChallengeContentResultScreenState
    extends ConsumerState<PronunciationChallengeContentResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<PronunciationState> viewState;
  late PronunciationController viewModel;
  late final AudioPlayer _player = AudioPlayer();
  late final AudioPlayer _playerLocal = AudioPlayer();
  late final AudioPlayer _confettiPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _player.dispose();
    _playerLocal.dispose();
    _confettiPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _confettiPlayer.play(AssetSource('sounds/success3.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = pronunciationControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    return Scaffold(
      body: Stack(
        children: [
          Lottie.asset(
            'assets/animations/congrats.json',
            animate: true,
            repeat: false,
            fit: BoxFit.fitWidth,
          ),
          if (viewState.value!.response != null)
            Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const SizedBox(height: 120),
                        Container(
                          height: 252,
                          width: 252,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xffFE754C),
                                Color(0xffE21F29),
                                Color(0xffC3151F),
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.topRight,
                            ),
                            image: DecorationImage(
                              image: AssetImage(
                                '$assetImageMainLesson/pronunciation_challenge/${viewState.value!.response!.accuracyScore > 95
                                    ? 'Luar Biasa-Android'
                                    : viewState.value!.response!.accuracyScore > 85
                                    ? 'Kerja Bagus-Android'
                                    : viewState.value!.response!.accuracyScore > 75
                                    ? 'Kerja Bagus-Android'
                                    : viewState.value!.response!.accuracyScore > 60
                                    ? 'Cukup sesuai-Android'
                                    : 'Tingkatkan Kembali-Android'}.png',
                              ),
                              fit: BoxFit.scaleDown,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          viewState.value!.response!.accuracyScore >= 95
                              ? context.loc.excellent
                              : viewState.value!.response!.accuracyScore >= 85
                              ? context.loc.very_good
                              : viewState.value!.response!.accuracyScore >= 75
                              ? context.loc.be_better
                              : context.loc.fair,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 24),
                        Container(
                          width: MediaQuery.of(context).size.width,
                          color: Color.fromARGB(255, 255, 248, 247),
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () async {
                                  await _player.play(
                                    UrlSource(
                                      viewState.value!.data?.audio ?? '',
                                    ),
                                  );
                                },
                                child: Icon(
                                  Icons.volume_up,
                                  size: 36,
                                  color: Color.fromARGB(255, 133, 115, 113),
                                ),
                              ),
                              const SizedBox(width: 16),
                              SizedBox(
                                width: MediaQuery.of(context).size.width - 100,
                                child: Text(
                                  viewState.value!.data!.caption,
                                  style: TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.w400,
                                    color: Color.fromARGB(255, 133, 115, 113),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          height: 1,
                          color: const Color.fromARGB(255, 255, 179, 172),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width,
                          color: Color.fromARGB(255, 255, 248, 247),
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () async {
                                  await _playerLocal.play(
                                    DeviceFileSource(
                                      viewState.value!.audioPath!.path,
                                    ),
                                  );
                                },
                                child: Icon(
                                  Icons.volume_up,
                                  size: 36,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(width: 16),
                              InkWell(
                                onTap: () {
                                  _showLegendDialog(context);
                                },
                                child: SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width - 100,
                                  child: Wrap(
                                    spacing: 8,
                                    textDirection: TextDirection.ltr,
                                    children: generateResultText,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 80,
                        ), // Add padding at bottom for RepeatNextButton
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 32),
              ],
            ),
          RepeatNextButton(
            onTapRepeat: () {
              viewModel.clearResponse();
              customNav(
                context,
                RouterName.pronunciationChallenge,
                isReplace: true,
                params: {
                  'level': widget.level,
                  'chapter': widget.chapter,
                  'path': widget.path,
                },
              );
            },
            onTapNext: () async {
              await viewModel.deleteLocalAudio();
              if (viewModel.index == viewModel.activePaths.length - 1) {
                if (context.mounted) {
                  viewModel.showAgregateResult(context);
                }
              } else {
                viewModel.nextQuestion();
                if (context.mounted) {
                  customNav(
                    context,
                    RouterName.pronunciationChallenge,
                    isReplace: true,
                    params: {
                      'level': widget.level,
                      'chapter': widget.chapter,
                      'path': widget.path,
                    },
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }

  void _showLegendDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AnimatedOpacity(
          duration: Duration(milliseconds: 300),
          opacity: 1.0,
          child: AnimatedScale(
            duration: Duration(milliseconds: 300),
            scale: 1.0,
            child: Dialog(
              child: Container(
                width: MediaQuery.of(context).size.width * 1.25,
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // SizedBox(height: 16),
                    // Text('Info',
                    //     style: Theme.of(context).textTheme.titleLarge),
                    // const SizedBox(
                    //   height: 16,
                    // ),
                    RichText(
                      text: TextSpan(
                        text: 'Pronunciation Info',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.black87,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),
                    colorLegend(Colors.green.shade800, context.loc.excellent),
                    SizedBox(height: 8),
                    colorLegend(Colors.blue.shade800, context.loc.very_good),
                    SizedBox(height: 8),
                    colorLegend(Colors.orange.shade800, context.loc.be_better),
                    SizedBox(height: 8),
                    colorLegend(Colors.red.shade800, context.loc.fair),
                    SizedBox(height: 16),
                    RichText(
                      text: TextSpan(
                        text: 'Mistakes Indicator',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.black87,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                    textLegend(
                      TextStyle(
                        decoration: TextDecoration.underline,
                        decorationColor: Colors.grey[800],
                        fontSize: 18,
                      ),
                      'Mispronunciation',
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        SizedBox(
                          width: 50,
                          child: RichText(
                            text: TextSpan(
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: '[',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 213, 126, 69),
                                  ),
                                ),
                                TextSpan(
                                  text: 'text',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 213, 126, 69),
                                  ),
                                ),
                                TextSpan(
                                  text: ']',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 213, 126, 69),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(width: 24),
                        Text('Omitted word', style: TextStyle(fontSize: 18)),
                      ],
                    ),
                    SizedBox(height: 8),
                    textLegend(
                      TextStyle(
                        decoration: TextDecoration.lineThrough,
                        decorationColor: Colors.pink,
                        decorationThickness: 2,
                        fontSize: 18,
                      ),
                      'Inserted word',
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> get generateResultText {
    return [
      if (viewState.value!.response?.formattedResult != null)
        RichText(
          textAlign: TextAlign.start,
          text: TextSpan(
            style: TextStyle(fontWeight: FontWeight.w800),
            children: [
              ...viewState.value!.response!.formattedResult.map(
                (text) => TextSpan(
                  text:
                      text.errorType == 'Omission'
                          ? '[${text.text}]'
                          : text.text,
                  style: setTextStyle(text),
                ),
              ),
              WidgetSpan(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(5, 5, 0, 0),
                  child: Image.asset(
                    "assets/images/main_lesson/pronunciation_challenge/info.png",
                    width: 22,
                  ),
                ),
              ),
            ],
          ),
        ),
    ];
  }

  TextStyle setTextStyle(FormattedResult text) {
    return TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      decoration:
          text.errorType == "Insertion"
              ? TextDecoration.lineThrough
              : text.errorType == "Mispronunciation"
              ? TextDecoration.underline
              : TextDecoration.none,
      decorationColor: Colors.red.shade600,
      decorationThickness: text.errorType == 'Insertion' ? 2 : 1,
      color:
          text.errorType == 'Omission'
              ? Color.fromARGB(255, 213, 126, 69)
              : text.accuracyScore >= 85
              ? Colors.green.shade800
              : text.accuracyScore >= 70
              ? Colors.blue.shade800
              : text.accuracyScore >= 55
              ? Colors.orange.shade800
              : Colors.red.shade800,
    );
  }

  Row colorLegend(Color legendColor, String legendText) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(color: legendColor),
        ),
        SizedBox(width: 16),
        Text(legendText, style: TextStyle(fontSize: 18)),
      ],
    );
  }

  Row textLegend(TextStyle legentStyle, String legendText) {
    return Row(
      children: [
        SizedBox(width: 50, child: Text('text', style: legentStyle)),
        SizedBox(width: 24),
        Text(legendText, style: TextStyle(fontSize: 18)),
      ],
    );
  }
}
