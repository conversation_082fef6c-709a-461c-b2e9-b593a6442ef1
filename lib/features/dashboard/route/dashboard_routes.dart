part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<DashboardRoute>(
  path: '/',
  name: RouterName.dashboardScreen,
  // routes: [
  //   TypedGoRoute<AdminRoute>(path: 'admin'),
  //   TypedGoRoute<UserRoute>(path: 'user'),
  //   TypedGoRoute<GuestRoute>(path: 'guest'),
  // ],
)
class DashboardRoute extends GoRouteData with _$DashboardRoute {
  const DashboardRoute();

  /// Important note on this redirect function: this isn't reactive.
  /// No redirect will be triggered on a user role change.
  ///
  /// This is currently unsupported.
  // @override
  // FutureOr<String?> redirect(BuildContext context, GoRouterState state) async {
  //   final userRole = await ProviderScope.containerOf(context).read(
  //     permissionsProvider.future,
  //   );
  //
  //   return userRole.map(
  //     admin: (_) => const AdminRoute().location,
  //     user: (_) => const UserRoute().location,
  //     guest: (_) => const GuestRoute().location,
  //     none: (_) => null,
  //   );
  // }

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      dashboard.loadLibrary,
      () => dashboard.DashboardScreen(),
    );
  }
}

/// This route shows how to parametrize a simple page and how to pass a simple query parameter.
