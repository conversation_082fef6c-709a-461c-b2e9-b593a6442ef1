// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'learning_material_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$LearningMaterialState {

 List<ChapterData> get chapters; Level get fetchedLevel;
/// Create a copy of LearningMaterialState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LearningMaterialStateCopyWith<LearningMaterialState> get copyWith => _$LearningMaterialStateCopyWithImpl<LearningMaterialState>(this as LearningMaterialState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LearningMaterialState&&const DeepCollectionEquality().equals(other.chapters, chapters)&&(identical(other.fetchedLevel, fetchedLevel) || other.fetchedLevel == fetchedLevel));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(chapters),fetchedLevel);

@override
String toString() {
  return 'LearningMaterialState(chapters: $chapters, fetchedLevel: $fetchedLevel)';
}


}

/// @nodoc
abstract mixin class $LearningMaterialStateCopyWith<$Res>  {
  factory $LearningMaterialStateCopyWith(LearningMaterialState value, $Res Function(LearningMaterialState) _then) = _$LearningMaterialStateCopyWithImpl;
@useResult
$Res call({
 List<ChapterData> chapters, Level fetchedLevel
});




}
/// @nodoc
class _$LearningMaterialStateCopyWithImpl<$Res>
    implements $LearningMaterialStateCopyWith<$Res> {
  _$LearningMaterialStateCopyWithImpl(this._self, this._then);

  final LearningMaterialState _self;
  final $Res Function(LearningMaterialState) _then;

/// Create a copy of LearningMaterialState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chapters = null,Object? fetchedLevel = null,}) {
  return _then(_self.copyWith(
chapters: null == chapters ? _self.chapters : chapters // ignore: cast_nullable_to_non_nullable
as List<ChapterData>,fetchedLevel: null == fetchedLevel ? _self.fetchedLevel : fetchedLevel // ignore: cast_nullable_to_non_nullable
as Level,
  ));
}

}


/// Adds pattern-matching-related methods to [LearningMaterialState].
extension LearningMaterialStatePatterns on LearningMaterialState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LearningMaterialState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LearningMaterialState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LearningMaterialState value)  $default,){
final _that = this;
switch (_that) {
case _LearningMaterialState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LearningMaterialState value)?  $default,){
final _that = this;
switch (_that) {
case _LearningMaterialState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ChapterData> chapters,  Level fetchedLevel)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LearningMaterialState() when $default != null:
return $default(_that.chapters,_that.fetchedLevel);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ChapterData> chapters,  Level fetchedLevel)  $default,) {final _that = this;
switch (_that) {
case _LearningMaterialState():
return $default(_that.chapters,_that.fetchedLevel);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ChapterData> chapters,  Level fetchedLevel)?  $default,) {final _that = this;
switch (_that) {
case _LearningMaterialState() when $default != null:
return $default(_that.chapters,_that.fetchedLevel);case _:
  return null;

}
}

}

/// @nodoc


class _LearningMaterialState implements LearningMaterialState {
  const _LearningMaterialState({final  List<ChapterData> chapters = const <ChapterData>[], this.fetchedLevel = Level.a1}): _chapters = chapters;
  

 final  List<ChapterData> _chapters;
@override@JsonKey() List<ChapterData> get chapters {
  if (_chapters is EqualUnmodifiableListView) return _chapters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_chapters);
}

@override@JsonKey() final  Level fetchedLevel;

/// Create a copy of LearningMaterialState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LearningMaterialStateCopyWith<_LearningMaterialState> get copyWith => __$LearningMaterialStateCopyWithImpl<_LearningMaterialState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LearningMaterialState&&const DeepCollectionEquality().equals(other._chapters, _chapters)&&(identical(other.fetchedLevel, fetchedLevel) || other.fetchedLevel == fetchedLevel));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_chapters),fetchedLevel);

@override
String toString() {
  return 'LearningMaterialState(chapters: $chapters, fetchedLevel: $fetchedLevel)';
}


}

/// @nodoc
abstract mixin class _$LearningMaterialStateCopyWith<$Res> implements $LearningMaterialStateCopyWith<$Res> {
  factory _$LearningMaterialStateCopyWith(_LearningMaterialState value, $Res Function(_LearningMaterialState) _then) = __$LearningMaterialStateCopyWithImpl;
@override @useResult
$Res call({
 List<ChapterData> chapters, Level fetchedLevel
});




}
/// @nodoc
class __$LearningMaterialStateCopyWithImpl<$Res>
    implements _$LearningMaterialStateCopyWith<$Res> {
  __$LearningMaterialStateCopyWithImpl(this._self, this._then);

  final _LearningMaterialState _self;
  final $Res Function(_LearningMaterialState) _then;

/// Create a copy of LearningMaterialState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chapters = null,Object? fetchedLevel = null,}) {
  return _then(_LearningMaterialState(
chapters: null == chapters ? _self._chapters : chapters // ignore: cast_nullable_to_non_nullable
as List<ChapterData>,fetchedLevel: null == fetchedLevel ? _self.fetchedLevel : fetchedLevel // ignore: cast_nullable_to_non_nullable
as Level,
  ));
}


}

// dart format on
