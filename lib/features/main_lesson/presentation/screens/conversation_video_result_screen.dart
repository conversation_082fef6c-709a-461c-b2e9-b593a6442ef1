import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/next_button.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:lottie/lottie.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class ConversationVideoResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const ConversationVideoResultScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<ConversationVideoResultScreen> createState() =>
      _ConversationVideoResultScreenState();
}

class _ConversationVideoResultScreenState
    extends ConsumerState<ConversationVideoResultScreen>
    with TickerProviderStateMixin {
  late final AudioPlayer _confettiPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _confettiPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _confettiPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Lottie.asset(
            'assets/animations/congrats.json',
            animate: true,
            repeat: false,
            fit: BoxFit.fitWidth,
          ),
          ListView(
            children: [
              const SizedBox(height: 170),
              Container(
                height: 252,
                width: 252,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Color(0xffFE754C),
                      Color(0xffE21F29),
                      Color(0xffC3151F),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  ),
                  image: DecorationImage(
                    image: AssetImage(
                      '$assetImageMainLesson/conversation_video/BG13.png',
                    ),
                    fit: BoxFit.scaleDown,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: MediaQuery.of(context).size.width * .8,
                child: Center(
                  child: Text(
                    context.loc.cv_result,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge,
                    softWrap: true,
                    maxLines: 10,
                  ),
                ),
              ),
            ],
          ),
          NextButton(
            onTap:
                () => VDialogAlert(
                  title: context.loc.nextSection,
                  child: Column(
                    children: [
                      VButtonGradient(
                        title: context.loc.yes,
                        fontStyle: Theme.of(
                          context,
                        ).textTheme.bodyLarge?.copyWith(color: Colors.white),
                        onTap: () {
                          customNav(
                            context,
                            RouterName.listeningMasteryOnboarding,
                            isReplace: true,
                            params: {
                              'level': widget.level,
                              'chapter': widget.chapter,
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      VButtonGradient(
                        title: context.loc.no,
                        fontStyle: Theme.of(context).textTheme.bodyLarge,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          color: Colors.transparent,
                          border: Border.all(
                            color: const Color(
                              0xff802115,
                            ), //                   <--- border color
                            width: 0.6,
                          ),
                        ),
                        onTap: () {
                          context.pop();
                          customNav(
                            context,
                            RouterName.dashboardScreen,
                            isReplace: true,
                          );
                        },
                      ),
                    ],
                  ),
                ).showMyDialog(context),
          ),
        ],
      ),
    );
  }

  Container instructionItem({required DefaultModel item}) => Container(
    padding: const EdgeInsets.symmetric(vertical: 20),
    child: Row(
      children: [
        Text('${item.title}.', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(width: 12),
        Container(
          height: 90,
          width: 90,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            image: DecorationImage(
              image: AssetImage(item.image),
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
        const SizedBox(width: 12),
        item.child,
      ],
    ),
  );
}
