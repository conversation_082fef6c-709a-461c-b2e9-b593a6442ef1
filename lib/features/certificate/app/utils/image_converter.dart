import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ImageConverter {
  /// Converts an SVG string to a PNG image represented by a [Uint8List].
  ///
  /// The [svgString] is the SVG content to be converted.
  /// The conversion is done by drawing the SVG onto a [ui.Picture] and then
  /// rendering it to a [ui.Image] of the specified [width] and [height].
  /// The final image is then encoded as a PNG.
  ///
  /// Returns a [Future<Uint8List?>] containing the PNG data, or `null` if
  /// the conversion fails.
  static Future<Uint8List?> svgToPng(
    String svgString, {
    int targetWidth = 1200,
  }) async {
    try {
      final pictureInfo = await vg.loadPicture(
        SvgStringLoader(svgString),
        null,
      );

      final originalWidth = pictureInfo.size.width;
      final originalHeight = pictureInfo.size.height;
      final aspectRatio = originalWidth / originalHeight;

      final targetHeight = (targetWidth / aspectRatio).round();

      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      final scaleX = targetWidth / originalWidth;
      final scaleY = targetHeight / originalHeight;
      canvas.scale(scaleX, scaleY);

      canvas.drawPicture(pictureInfo.picture);

      final picture = recorder.endRecording();
      final image = await picture.toImage(targetWidth, targetHeight);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error converting SVG to PNG: $e');
      return null;
    }
  }
}
