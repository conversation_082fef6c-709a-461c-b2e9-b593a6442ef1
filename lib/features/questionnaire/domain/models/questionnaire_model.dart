// ignore_for_file: invalid_annotation_target

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'questionnaire_model.freezed.dart';
part 'questionnaire_model.g.dart';

@freezed
sealed class QuestionnaireModel with _$QuestionnaireModel {
  const factory QuestionnaireModel({
    String? questionId,
    @Default(false) @JsonKey(includeToJson: false) bool status,
    @JsonKey(includeToJson: false) List<Choices>? choices,
    @JsonKey(includeToJson: false) int? order,
    @JsonKey(includeToJson: false) Map<String, dynamic>? question,
    @JsonKey(includeToJson: false) int? type,
    @Default(false)
    @<PERSON><PERSON><PERSON><PERSON>(name: 'multiple_answer', includeToJson: false)
    bool multipleAnswer,
    dynamic answer,
    // @Json<PERSON>ey(name:'created_at', toJson: timestampToJson)
    // createdAt,
  }) = _QuestionnaireModel;

  factory QuestionnaireModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionnaireModelFromJson(json);
}

@freezed
sealed class Choices with _$Choices {
  const factory Choices({
    String? image,
    @JsonKey(name: 'image_url') String? imageUrl,
    String? value,
    Map<String, dynamic>? text,
  }) = _Choices;

  factory Choices.fromJson(Map<String, dynamic> json) =>
      _$ChoicesFromJson(json);
}

Timestamp timestampToJson(value) => Timestamp.now();
