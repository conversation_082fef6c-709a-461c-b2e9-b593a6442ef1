import 'package:flick_video_player/flick_video_player.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

part 'conversation_state.freezed.dart';

@freezed
abstract class ConversationState with _$ConversationState {
  factory ConversationState({
    @Default([]) List<ConversationPart> conversations,
    QuestionResultModel? result,
    @Default(0) int currentPage,
    @Default(false) bool expandedResult,
    AudioPath? audioPath,
    Map<String, dynamic>? response,
    @Default('stage 1') String stageTalking,
    @Default(0) int selectedIndex,
    @Default(false) bool nextSection,
    FlickManager? flickManager,
    @Default(false) bool isIntro,
  }) = _ConversationState;

  // Allow custom getters / setters
  const ConversationState._();
}
