import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';

void main() {
  group('PronunciationResultFormatted Tests', () {
    group('Constructor and Properties', () {
      test(
        'should create PronunciationResultFormatted with all required properties',
        () {
          final formattedResults = [
            FormattedResult(
              text: 'hello',
              accuracyScore: 95.0,
              errorType: 'Mispronunciation',
            ),
          ];

          final pronunciationResult = PronunciationResultFormatted(
            originalText: 'hello world',
            recordedInput: 'hello world',
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
            formattedResult: formattedResults,
          );

          expect(pronunciationResult.originalText, equals('hello world'));
          expect(pronunciationResult.recordedInput, equals('hello world'));
          expect(pronunciationResult.accuracyScore, equals(95.0));
          expect(pronunciationResult.fluencyScore, equals(90.0));
          expect(pronunciationResult.prosodyScore, equals(85.0));
          expect(pronunciationResult.completenessScore, equals(100.0));
          expect(pronunciationResult.pronScore, equals(92.5));
          expect(pronunciationResult.formattedResult, equals(formattedResults));
        },
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final json = pronunciationResult.toJson();

        expect(json['originalText'], equals('hello world'));
        expect(json['recordedInput'], equals('hello world'));
        expect(json['accuracyScore'], equals(95.0));
        expect(json['fluencyScore'], equals(90.0));
        expect(json['prosodyScore'], equals(85.0));
        expect(json['completenessScore'], equals(100.0));
        expect(json['pronScore'], equals(92.5));
        expect(json['formattedResult'], isA<List<dynamic>>());
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'originalText': 'hello world',
          'recordedInput': 'hello world',
          'accuracyScore': 95.0,
          'fluencyScore': 90.0,
          'prosodyScore': 85.0,
          'completenessScore': 100.0,
          'pronScore': 92.5,
          'formattedResult': [
            {
              'text': 'hello',
              'accuracyScore': 95.0,
              'errorType': 'Mispronunciation',
            },
          ],
        };

        final pronunciationResult = PronunciationResultFormatted.fromJson(json);

        expect(pronunciationResult.originalText, equals('hello world'));
        expect(pronunciationResult.recordedInput, equals('hello world'));
        expect(pronunciationResult.accuracyScore, equals(95.0));
        expect(pronunciationResult.fluencyScore, equals(90.0));
        expect(pronunciationResult.prosodyScore, equals(85.0));
        expect(pronunciationResult.completenessScore, equals(100.0));
        expect(pronunciationResult.pronScore, equals(92.5));
        expect(
          pronunciationResult.formattedResult,
          isA<List<FormattedResult>>(),
        );
        expect(pronunciationResult.formattedResult.length, equals(1));
        expect(pronunciationResult.formattedResult[0].text, equals('hello'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final result1 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final result2 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when originalText is different', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final result1 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final result2 = PronunciationResultFormatted(
          originalText: 'goodbye world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final stringRepresentation = pronunciationResult.toString();

        expect(stringRepresentation, contains('PronunciationResultFormatted'));
        expect(stringRepresentation, contains('originalText: hello world'));
        expect(stringRepresentation, contains('accuracyScore: 95.0'));
      });
    });
  });

  group('FormattedResult Tests', () {
    group('Constructor and Properties', () {
      test('should create FormattedResult with all required properties', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(formattedResult.text, equals('hello'));
        expect(formattedResult.accuracyScore, equals(95.0));
        expect(formattedResult.errorType, equals('Mispronunciation'));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final json = formattedResult.toJson();

        expect(json['text'], equals('hello'));
        expect(json['accuracyScore'], equals(95.0));
        expect(json['errorType'], equals('Mispronunciation'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'text': 'hello',
          'accuracyScore': 85.0,
          'errorType': 'Omission',
        };

        final formattedResult = FormattedResult.fromJson(json);

        expect(formattedResult.text, equals('hello'));
        expect(formattedResult.accuracyScore, equals(85.0));
        expect(formattedResult.errorType, equals('Omission'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final result1 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final result2 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when text is different', () {
        final result1 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final result2 = FormattedResult(
          text: 'world',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final stringRepresentation = formattedResult.toString();

        expect(stringRepresentation, contains('FormattedResult'));
        expect(stringRepresentation, contains('text: hello'));
        expect(stringRepresentation, contains('accuracyScore: 95.0'));
        expect(stringRepresentation, contains('errorType: Mispronunciation'));
      });
    });
  });
}
