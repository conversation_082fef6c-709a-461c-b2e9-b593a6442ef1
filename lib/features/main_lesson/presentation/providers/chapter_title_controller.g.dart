// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter_title_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chapterTitleControllerHash() =>
    r'b7f6b9a38407136cf70976c0aaf47f317c400456';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ChapterTitleController
    extends BuildlessAutoDisposeAsyncNotifier<ChapterTitleState> {
  late final String level;
  late final String chapter;

  FutureOr<ChapterTitleState> build(String level, String chapter);
}

/// See also [ChapterTitleController].
@ProviderFor(ChapterTitleController)
const chapterTitleControllerProvider = ChapterTitleControllerFamily();

/// See also [ChapterTitleController].
class ChapterTitleControllerFamily
    extends Family<AsyncValue<ChapterTitleState>> {
  /// See also [ChapterTitleController].
  const ChapterTitleControllerFamily();

  /// See also [ChapterTitleController].
  ChapterTitleControllerProvider call(String level, String chapter) {
    return ChapterTitleControllerProvider(level, chapter);
  }

  @override
  ChapterTitleControllerProvider getProviderOverride(
    covariant ChapterTitleControllerProvider provider,
  ) {
    return call(provider.level, provider.chapter);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'chapterTitleControllerProvider';
}

/// See also [ChapterTitleController].
class ChapterTitleControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          ChapterTitleController,
          ChapterTitleState
        > {
  /// See also [ChapterTitleController].
  ChapterTitleControllerProvider(String level, String chapter)
    : this._internal(
        () =>
            ChapterTitleController()
              ..level = level
              ..chapter = chapter,
        from: chapterTitleControllerProvider,
        name: r'chapterTitleControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$chapterTitleControllerHash,
        dependencies: ChapterTitleControllerFamily._dependencies,
        allTransitiveDependencies:
            ChapterTitleControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
      );

  ChapterTitleControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
  }) : super.internal();

  final String level;
  final String chapter;

  @override
  FutureOr<ChapterTitleState> runNotifierBuild(
    covariant ChapterTitleController notifier,
  ) {
    return notifier.build(level, chapter);
  }

  @override
  Override overrideWith(ChapterTitleController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ChapterTitleControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    ChapterTitleController,
    ChapterTitleState
  >
  createElement() {
    return _ChapterTitleControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ChapterTitleControllerProvider &&
        other.level == level &&
        other.chapter == chapter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ChapterTitleControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ChapterTitleState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;
}

class _ChapterTitleControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          ChapterTitleController,
          ChapterTitleState
        >
    with ChapterTitleControllerRef {
  _ChapterTitleControllerProviderElement(super.provider);

  @override
  String get level => (origin as ChapterTitleControllerProvider).level;
  @override
  String get chapter => (origin as ChapterTitleControllerProvider).chapter;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
