import 'package:selfeng/features/questionnaire/domain/repositories/questionnaire_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final questionnaireRepositoryProvider = Provider<QuestionnaireRepository>((
  ref,
) {
  // FirebaseFirestore firestore = FirebaseFirestore.instance;

  final FirestoreServiceRepository firestore = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return QuestionnaireRepositoryImpl(firestore);
});
