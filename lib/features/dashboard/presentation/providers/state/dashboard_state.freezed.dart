// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DashboardState {

 LastCourse? get lastPronunciation; LastCourse? get lastConversation; LastCourse? get lastListening; LastCourse? get lastSpeaking; bool get afterTest; List<LastCourseInfo> get lastCourse; ContentIndexData? get firstLesson;
/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardStateCopyWith<DashboardState> get copyWith => _$DashboardStateCopyWithImpl<DashboardState>(this as DashboardState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardState&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking)&&(identical(other.afterTest, afterTest) || other.afterTest == afterTest)&&const DeepCollectionEquality().equals(other.lastCourse, lastCourse)&&(identical(other.firstLesson, firstLesson) || other.firstLesson == firstLesson));
}


@override
int get hashCode => Object.hash(runtimeType,lastPronunciation,lastConversation,lastListening,lastSpeaking,afterTest,const DeepCollectionEquality().hash(lastCourse),firstLesson);

@override
String toString() {
  return 'DashboardState(lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking, afterTest: $afterTest, lastCourse: $lastCourse, firstLesson: $firstLesson)';
}


}

/// @nodoc
abstract mixin class $DashboardStateCopyWith<$Res>  {
  factory $DashboardStateCopyWith(DashboardState value, $Res Function(DashboardState) _then) = _$DashboardStateCopyWithImpl;
@useResult
$Res call({
 LastCourse? lastPronunciation, LastCourse? lastConversation, LastCourse? lastListening, LastCourse? lastSpeaking, bool afterTest, List<LastCourseInfo> lastCourse, ContentIndexData? firstLesson
});


$LastCourseCopyWith<$Res>? get lastPronunciation;$LastCourseCopyWith<$Res>? get lastConversation;$LastCourseCopyWith<$Res>? get lastListening;$LastCourseCopyWith<$Res>? get lastSpeaking;$ContentIndexDataCopyWith<$Res>? get firstLesson;

}
/// @nodoc
class _$DashboardStateCopyWithImpl<$Res>
    implements $DashboardStateCopyWith<$Res> {
  _$DashboardStateCopyWithImpl(this._self, this._then);

  final DashboardState _self;
  final $Res Function(DashboardState) _then;

/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,Object? afterTest = null,Object? lastCourse = null,Object? firstLesson = freezed,}) {
  return _then(_self.copyWith(
lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,afterTest: null == afterTest ? _self.afterTest : afterTest // ignore: cast_nullable_to_non_nullable
as bool,lastCourse: null == lastCourse ? _self.lastCourse : lastCourse // ignore: cast_nullable_to_non_nullable
as List<LastCourseInfo>,firstLesson: freezed == firstLesson ? _self.firstLesson : firstLesson // ignore: cast_nullable_to_non_nullable
as ContentIndexData?,
  ));
}
/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentIndexDataCopyWith<$Res>? get firstLesson {
    if (_self.firstLesson == null) {
    return null;
  }

  return $ContentIndexDataCopyWith<$Res>(_self.firstLesson!, (value) {
    return _then(_self.copyWith(firstLesson: value));
  });
}
}


/// Adds pattern-matching-related methods to [DashboardState].
extension DashboardStatePatterns on DashboardState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DashboardState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DashboardState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DashboardState value)  $default,){
final _that = this;
switch (_that) {
case _DashboardState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DashboardState value)?  $default,){
final _that = this;
switch (_that) {
case _DashboardState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool afterTest,  List<LastCourseInfo> lastCourse,  ContentIndexData? firstLesson)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DashboardState() when $default != null:
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.afterTest,_that.lastCourse,_that.firstLesson);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool afterTest,  List<LastCourseInfo> lastCourse,  ContentIndexData? firstLesson)  $default,) {final _that = this;
switch (_that) {
case _DashboardState():
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.afterTest,_that.lastCourse,_that.firstLesson);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool afterTest,  List<LastCourseInfo> lastCourse,  ContentIndexData? firstLesson)?  $default,) {final _that = this;
switch (_that) {
case _DashboardState() when $default != null:
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.afterTest,_that.lastCourse,_that.firstLesson);case _:
  return null;

}
}

}

/// @nodoc


class _DashboardState implements DashboardState {
   _DashboardState({this.lastPronunciation, this.lastConversation, this.lastListening, this.lastSpeaking, this.afterTest = true, final  List<LastCourseInfo> lastCourse = const [], this.firstLesson}): _lastCourse = lastCourse;
  

@override final  LastCourse? lastPronunciation;
@override final  LastCourse? lastConversation;
@override final  LastCourse? lastListening;
@override final  LastCourse? lastSpeaking;
@override@JsonKey() final  bool afterTest;
 final  List<LastCourseInfo> _lastCourse;
@override@JsonKey() List<LastCourseInfo> get lastCourse {
  if (_lastCourse is EqualUnmodifiableListView) return _lastCourse;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_lastCourse);
}

@override final  ContentIndexData? firstLesson;

/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardStateCopyWith<_DashboardState> get copyWith => __$DashboardStateCopyWithImpl<_DashboardState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardState&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking)&&(identical(other.afterTest, afterTest) || other.afterTest == afterTest)&&const DeepCollectionEquality().equals(other._lastCourse, _lastCourse)&&(identical(other.firstLesson, firstLesson) || other.firstLesson == firstLesson));
}


@override
int get hashCode => Object.hash(runtimeType,lastPronunciation,lastConversation,lastListening,lastSpeaking,afterTest,const DeepCollectionEquality().hash(_lastCourse),firstLesson);

@override
String toString() {
  return 'DashboardState(lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking, afterTest: $afterTest, lastCourse: $lastCourse, firstLesson: $firstLesson)';
}


}

/// @nodoc
abstract mixin class _$DashboardStateCopyWith<$Res> implements $DashboardStateCopyWith<$Res> {
  factory _$DashboardStateCopyWith(_DashboardState value, $Res Function(_DashboardState) _then) = __$DashboardStateCopyWithImpl;
@override @useResult
$Res call({
 LastCourse? lastPronunciation, LastCourse? lastConversation, LastCourse? lastListening, LastCourse? lastSpeaking, bool afterTest, List<LastCourseInfo> lastCourse, ContentIndexData? firstLesson
});


@override $LastCourseCopyWith<$Res>? get lastPronunciation;@override $LastCourseCopyWith<$Res>? get lastConversation;@override $LastCourseCopyWith<$Res>? get lastListening;@override $LastCourseCopyWith<$Res>? get lastSpeaking;@override $ContentIndexDataCopyWith<$Res>? get firstLesson;

}
/// @nodoc
class __$DashboardStateCopyWithImpl<$Res>
    implements _$DashboardStateCopyWith<$Res> {
  __$DashboardStateCopyWithImpl(this._self, this._then);

  final _DashboardState _self;
  final $Res Function(_DashboardState) _then;

/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,Object? afterTest = null,Object? lastCourse = null,Object? firstLesson = freezed,}) {
  return _then(_DashboardState(
lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,afterTest: null == afterTest ? _self.afterTest : afterTest // ignore: cast_nullable_to_non_nullable
as bool,lastCourse: null == lastCourse ? _self._lastCourse : lastCourse // ignore: cast_nullable_to_non_nullable
as List<LastCourseInfo>,firstLesson: freezed == firstLesson ? _self.firstLesson : firstLesson // ignore: cast_nullable_to_non_nullable
as ContentIndexData?,
  ));
}

/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}/// Create a copy of DashboardState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentIndexDataCopyWith<$Res>? get firstLesson {
    if (_self.firstLesson == null) {
    return null;
  }

  return $ContentIndexDataCopyWith<$Res>(_self.firstLesson!, (value) {
    return _then(_self.copyWith(firstLesson: value));
  });
}
}

// dart format on
