import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Custom navigation wrapper for <PERSON><PERSON><PERSON><PERSON> with consistent behavior
///
/// Returns a [Future<T?>] that completes when the pushed route is popped
/// with an optional result of type [T].
///
/// Parameters:
/// - [context]: The build context for navigation
/// - [route]: Named route to navigate to
/// - [params]: Path parameters for the route (optional)
/// - [isReplace]: If true, replaces current route instead of pushing (optional)
Future<T?> customNav<T extends Object?>(
  BuildContext context,
  String route, {
  Map<String, String> params = const <String, String>{},
  bool isReplace = false,
}) {
  final router = GoRouter.of(context);

  if (isReplace) {
    return router.pushReplacementNamed<T>(route, pathParameters: params);
  }

  return router.pushNamed<T>(route, pathParameters: params);
}

Future<bool> isCurrentPage(BuildContext context) async {
  await Future.delayed(const Duration(milliseconds: 100));

  if (context.mounted) {
    return ModalRoute.of(context)?.isCurrent ?? false;
  } else {
    return false;
  }
}

class DeferredRoute extends StatelessWidget {
  const DeferredRoute(this.future, this.buildChild, {super.key});
  final Future<dynamic> Function() future;
  final Widget Function() buildChild;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future(),
      builder: ((context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return buildChild();
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      }),
    );
  }
}
