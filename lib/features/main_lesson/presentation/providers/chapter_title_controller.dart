import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/chapter_title_state.dart';

part 'chapter_title_controller.g.dart';

@riverpod
class ChapterTitleController extends _$ChapterTitleController {
  late MainLessonRepository mainLessonRepository;

  @override
  FutureOr<ChapterTitleState> build(String level, String chapter) {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    init(level, chapter);
    return ChapterTitleState();
  }

  Future<void> init(String level, String chapter) async {
    final data = await mainLessonRepository.getChapterData(level, chapter);

    data.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        state = AsyncData(state.value!.copyWith(chapter: data));
      },
    );
    // final idx = await mainLessonRepository.getChapterIndex(level: level);

    // idx.fold((failure) {
    //   state = AsyncError(failure.message, StackTrace.current);
    // }, (data) async {
    //   state = AsyncData(state.value!.copyWith(
    //     paths: data,
    //   ));
    // });
  }
}
