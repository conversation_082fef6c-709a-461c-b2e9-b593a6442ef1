// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chapter_content_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChapterContentState {

 List<ContentIndexData>? get pronunciationContent; List<ContentIndexData>? get conversationContent; List<ContentIndexData>? get listeningContent; List<ContentIndexData>? get speakingContent;
/// Create a copy of ChapterContentState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChapterContentStateCopyWith<ChapterContentState> get copyWith => _$ChapterContentStateCopyWithImpl<ChapterContentState>(this as ChapterContentState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChapterContentState&&const DeepCollectionEquality().equals(other.pronunciationContent, pronunciationContent)&&const DeepCollectionEquality().equals(other.conversationContent, conversationContent)&&const DeepCollectionEquality().equals(other.listeningContent, listeningContent)&&const DeepCollectionEquality().equals(other.speakingContent, speakingContent));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(pronunciationContent),const DeepCollectionEquality().hash(conversationContent),const DeepCollectionEquality().hash(listeningContent),const DeepCollectionEquality().hash(speakingContent));

@override
String toString() {
  return 'ChapterContentState(pronunciationContent: $pronunciationContent, conversationContent: $conversationContent, listeningContent: $listeningContent, speakingContent: $speakingContent)';
}


}

/// @nodoc
abstract mixin class $ChapterContentStateCopyWith<$Res>  {
  factory $ChapterContentStateCopyWith(ChapterContentState value, $Res Function(ChapterContentState) _then) = _$ChapterContentStateCopyWithImpl;
@useResult
$Res call({
 List<ContentIndexData>? pronunciationContent, List<ContentIndexData>? conversationContent, List<ContentIndexData>? listeningContent, List<ContentIndexData>? speakingContent
});




}
/// @nodoc
class _$ChapterContentStateCopyWithImpl<$Res>
    implements $ChapterContentStateCopyWith<$Res> {
  _$ChapterContentStateCopyWithImpl(this._self, this._then);

  final ChapterContentState _self;
  final $Res Function(ChapterContentState) _then;

/// Create a copy of ChapterContentState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pronunciationContent = freezed,Object? conversationContent = freezed,Object? listeningContent = freezed,Object? speakingContent = freezed,}) {
  return _then(_self.copyWith(
pronunciationContent: freezed == pronunciationContent ? _self.pronunciationContent : pronunciationContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,conversationContent: freezed == conversationContent ? _self.conversationContent : conversationContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,listeningContent: freezed == listeningContent ? _self.listeningContent : listeningContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,speakingContent: freezed == speakingContent ? _self.speakingContent : speakingContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ChapterContentState].
extension ChapterContentStatePatterns on ChapterContentState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChapterContentState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChapterContentState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChapterContentState value)  $default,){
final _that = this;
switch (_that) {
case _ChapterContentState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChapterContentState value)?  $default,){
final _that = this;
switch (_that) {
case _ChapterContentState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ContentIndexData>? pronunciationContent,  List<ContentIndexData>? conversationContent,  List<ContentIndexData>? listeningContent,  List<ContentIndexData>? speakingContent)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChapterContentState() when $default != null:
return $default(_that.pronunciationContent,_that.conversationContent,_that.listeningContent,_that.speakingContent);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ContentIndexData>? pronunciationContent,  List<ContentIndexData>? conversationContent,  List<ContentIndexData>? listeningContent,  List<ContentIndexData>? speakingContent)  $default,) {final _that = this;
switch (_that) {
case _ChapterContentState():
return $default(_that.pronunciationContent,_that.conversationContent,_that.listeningContent,_that.speakingContent);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ContentIndexData>? pronunciationContent,  List<ContentIndexData>? conversationContent,  List<ContentIndexData>? listeningContent,  List<ContentIndexData>? speakingContent)?  $default,) {final _that = this;
switch (_that) {
case _ChapterContentState() when $default != null:
return $default(_that.pronunciationContent,_that.conversationContent,_that.listeningContent,_that.speakingContent);case _:
  return null;

}
}

}

/// @nodoc


class _ChapterContentState extends ChapterContentState {
   _ChapterContentState({final  List<ContentIndexData>? pronunciationContent, final  List<ContentIndexData>? conversationContent, final  List<ContentIndexData>? listeningContent, final  List<ContentIndexData>? speakingContent}): _pronunciationContent = pronunciationContent,_conversationContent = conversationContent,_listeningContent = listeningContent,_speakingContent = speakingContent,super._();
  

 final  List<ContentIndexData>? _pronunciationContent;
@override List<ContentIndexData>? get pronunciationContent {
  final value = _pronunciationContent;
  if (value == null) return null;
  if (_pronunciationContent is EqualUnmodifiableListView) return _pronunciationContent;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ContentIndexData>? _conversationContent;
@override List<ContentIndexData>? get conversationContent {
  final value = _conversationContent;
  if (value == null) return null;
  if (_conversationContent is EqualUnmodifiableListView) return _conversationContent;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ContentIndexData>? _listeningContent;
@override List<ContentIndexData>? get listeningContent {
  final value = _listeningContent;
  if (value == null) return null;
  if (_listeningContent is EqualUnmodifiableListView) return _listeningContent;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<ContentIndexData>? _speakingContent;
@override List<ContentIndexData>? get speakingContent {
  final value = _speakingContent;
  if (value == null) return null;
  if (_speakingContent is EqualUnmodifiableListView) return _speakingContent;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of ChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChapterContentStateCopyWith<_ChapterContentState> get copyWith => __$ChapterContentStateCopyWithImpl<_ChapterContentState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChapterContentState&&const DeepCollectionEquality().equals(other._pronunciationContent, _pronunciationContent)&&const DeepCollectionEquality().equals(other._conversationContent, _conversationContent)&&const DeepCollectionEquality().equals(other._listeningContent, _listeningContent)&&const DeepCollectionEquality().equals(other._speakingContent, _speakingContent));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_pronunciationContent),const DeepCollectionEquality().hash(_conversationContent),const DeepCollectionEquality().hash(_listeningContent),const DeepCollectionEquality().hash(_speakingContent));

@override
String toString() {
  return 'ChapterContentState(pronunciationContent: $pronunciationContent, conversationContent: $conversationContent, listeningContent: $listeningContent, speakingContent: $speakingContent)';
}


}

/// @nodoc
abstract mixin class _$ChapterContentStateCopyWith<$Res> implements $ChapterContentStateCopyWith<$Res> {
  factory _$ChapterContentStateCopyWith(_ChapterContentState value, $Res Function(_ChapterContentState) _then) = __$ChapterContentStateCopyWithImpl;
@override @useResult
$Res call({
 List<ContentIndexData>? pronunciationContent, List<ContentIndexData>? conversationContent, List<ContentIndexData>? listeningContent, List<ContentIndexData>? speakingContent
});




}
/// @nodoc
class __$ChapterContentStateCopyWithImpl<$Res>
    implements _$ChapterContentStateCopyWith<$Res> {
  __$ChapterContentStateCopyWithImpl(this._self, this._then);

  final _ChapterContentState _self;
  final $Res Function(_ChapterContentState) _then;

/// Create a copy of ChapterContentState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pronunciationContent = freezed,Object? conversationContent = freezed,Object? listeningContent = freezed,Object? speakingContent = freezed,}) {
  return _then(_ChapterContentState(
pronunciationContent: freezed == pronunciationContent ? _self._pronunciationContent : pronunciationContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,conversationContent: freezed == conversationContent ? _self._conversationContent : conversationContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,listeningContent: freezed == listeningContent ? _self._listeningContent : listeningContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,speakingContent: freezed == speakingContent ? _self._speakingContent : speakingContent // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>?,
  ));
}


}

// dart format on
