import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

class FadeOutAudioVolume extends StatefulWidget {
  final AudioPlayer player;
  final bool shouldFadeOut;
  final Duration duration;
  final Widget child;

  const FadeOutAudioVolume({
    super.key,
    required this.player,
    required this.shouldFadeOut,
    this.duration = const Duration(seconds: 2),
    required this.child,
  });

  @override
  State<FadeOutAudioVolume> createState() => _FadeOutAudioVolumeState();
}

class _FadeOutAudioVolumeState extends State<FadeOutAudioVolume> {
  Timer? _timer;
  double _currentVolume = 1.0;

  @override
  void didUpdateWidget(covariant FadeOutAudioVolume oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.shouldFadeOut && !oldWidget.shouldFadeOut) {
      _startFadeOut();
    }
  }

  void _startFadeOut() {
    _timer?.cancel();
    const int steps = 20;
    final stepDuration = widget.duration.inMilliseconds ~/ steps;
    int currentStep = 0;
    _currentVolume = 1.0;
    _timer = Timer.periodic(Duration(milliseconds: stepDuration), (timer) {
      setState(() {
        currentStep++;
        _currentVolume = 1.0 - (currentStep / steps);
        if (_currentVolume < 0) _currentVolume = 0;
        widget.player.setVolume(_currentVolume);
        if (currentStep >= steps) {
          widget.player.setVolume(0);
          timer.cancel();
        }
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
