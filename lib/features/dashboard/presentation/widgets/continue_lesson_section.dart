import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/dashboard_controller.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/dashboard_state.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/last_lesson_card_fix.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class ContinueLessonsSection extends StatelessWidget {
  final AsyncValue<DashboardState> viewState;
  final DashboardController viewModel;

  const ContinueLessonsSection({
    super.key,
    required this.viewState,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            context.loc.continueYourLessons,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          ),
        ),
        const SizedBox(height: 6),
        Container(
          width: double.infinity,
          height: 220,
          padding: const EdgeInsets.symmetric(vertical: 18.0),
          decoration: BoxDecoration(
            color: Color(0xffFFEDEB),
            border: Border.all(color: Color(0xffFFB3AC)),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _prepareLastLessonCards(
                ({
                  required lastCourse,
                  required totalCourses,
                  required onTap,
                }) => LastLessonCardFix(
                  lastCourse: lastCourse,
                  totalCourses: totalCourses,
                  onTap: onTap,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Generic function to prepare lesson cards using a builder
  List<Widget> _prepareLastLessonCards(
    Widget Function({
      required dynamic
      lastCourse, // Assuming common data structure or using dynamic
      required int totalCourses,
      required VoidCallback onTap,
    })
    cardBuilder,
  ) {
    final courses = viewState.value?.lastCourse;
    if (courses == null || courses.isEmpty) return [];

    final totalCourse = courses.length;
    return courses
        .map(
          (data) => cardBuilder(
            lastCourse: data,
            totalCourses: totalCourse,
            onTap: () => viewModel.changeisFromLastCourse(true),
          ),
        )
        .toList();
  }
}
