// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_chapter_content_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$libraryChapterContentControllerHash() =>
    r'25541f6383e5a0ffc48d7682bb76638fae6c3c3b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$LibraryChapterContentController
    extends BuildlessAutoDisposeAsyncNotifier<LibraryChapterContentState> {
  late final String level;
  late final String chapter;

  FutureOr<LibraryChapterContentState> build(String level, String chapter);
}

/// See also [LibraryChapterContentController].
@ProviderFor(LibraryChapterContentController)
const libraryChapterContentControllerProvider =
    LibraryChapterContentControllerFamily();

/// See also [LibraryChapterContentController].
class LibraryChapterContentControllerFamily
    extends Family<AsyncValue<LibraryChapterContentState>> {
  /// See also [LibraryChapterContentController].
  const LibraryChapterContentControllerFamily();

  /// See also [LibraryChapterContentController].
  LibraryChapterContentControllerProvider call(String level, String chapter) {
    return LibraryChapterContentControllerProvider(level, chapter);
  }

  @override
  LibraryChapterContentControllerProvider getProviderOverride(
    covariant LibraryChapterContentControllerProvider provider,
  ) {
    return call(provider.level, provider.chapter);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'libraryChapterContentControllerProvider';
}

/// See also [LibraryChapterContentController].
class LibraryChapterContentControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          LibraryChapterContentController,
          LibraryChapterContentState
        > {
  /// See also [LibraryChapterContentController].
  LibraryChapterContentControllerProvider(String level, String chapter)
    : this._internal(
        () =>
            LibraryChapterContentController()
              ..level = level
              ..chapter = chapter,
        from: libraryChapterContentControllerProvider,
        name: r'libraryChapterContentControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$libraryChapterContentControllerHash,
        dependencies: LibraryChapterContentControllerFamily._dependencies,
        allTransitiveDependencies:
            LibraryChapterContentControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
      );

  LibraryChapterContentControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
  }) : super.internal();

  final String level;
  final String chapter;

  @override
  FutureOr<LibraryChapterContentState> runNotifierBuild(
    covariant LibraryChapterContentController notifier,
  ) {
    return notifier.build(level, chapter);
  }

  @override
  Override overrideWith(LibraryChapterContentController Function() create) {
    return ProviderOverride(
      origin: this,
      override: LibraryChapterContentControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    LibraryChapterContentController,
    LibraryChapterContentState
  >
  createElement() {
    return _LibraryChapterContentControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LibraryChapterContentControllerProvider &&
        other.level == level &&
        other.chapter == chapter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LibraryChapterContentControllerRef
    on AutoDisposeAsyncNotifierProviderRef<LibraryChapterContentState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;
}

class _LibraryChapterContentControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          LibraryChapterContentController,
          LibraryChapterContentState
        >
    with LibraryChapterContentControllerRef {
  _LibraryChapterContentControllerProviderElement(super.provider);

  @override
  String get level => (origin as LibraryChapterContentControllerProvider).level;
  @override
  String get chapter =>
      (origin as LibraryChapterContentControllerProvider).chapter;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
