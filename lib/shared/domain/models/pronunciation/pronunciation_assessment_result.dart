// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'pronunciation_assessment_result.freezed.dart';
part 'pronunciation_assessment_result.g.dart';

@freezed
sealed class PronunciationAssessmentResult
    with _$PronunciationAssessmentResult {
  factory PronunciationAssessmentResult({
    @JsonKey(name: 'result') required PronunciationAssessmentResultData result,
  }) = _PronunciationAssessmentResult;

  factory PronunciationAssessmentResult.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAssessmentResultFromJson(json);
}

@freezed
sealed class PronunciationAssessmentResultData
    with _$PronunciationAssessmentResultData {
  factory PronunciationAssessmentResultData({
    @JsonKey(name: 'privPronJson')
    required PronunciationAssessmentPriv privPronJson,
  }) = _PronunciationAssessmentResultData;

  factory PronunciationAssessmentResultData.from<PERSON><PERSON>(
    Map<String, dynamic> json,
  ) => _$PronunciationAssessmentResultDataFromJson(json);
}

@freezed
sealed class PronunciationAssessmentPriv with _$PronunciationAssessmentPriv {
  @JsonSerializable(explicitToJson: true)
  factory PronunciationAssessmentPriv({
    @JsonKey(name: 'Confidence') required double confidence,
    @JsonKey(name: 'Lexical') required String lexical,
    @JsonKey(name: 'ITN') required String itn,
    @JsonKey(name: 'MaskedITN') required String maskedItn,
    @JsonKey(name: 'Display') required String display,
    @JsonKey(name: 'PronunciationAssessment')
    required PronunciationAssessmentValue pronunciationAssessment,
    @JsonKey(name: 'Words') required List<PronunciationAssessmentWord> words,
  }) = _PronunciationAssessmentPriv;

  factory PronunciationAssessmentPriv.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAssessmentPrivFromJson(json);
}

@freezed
sealed class PronunciationAssessmentValue with _$PronunciationAssessmentValue {
  @JsonSerializable(explicitToJson: true)
  factory PronunciationAssessmentValue({
    @JsonKey(name: 'AccuracyScore') required double accuracyScore,
    @JsonKey(name: 'FluencyScore') required double fluencyScore,
    @JsonKey(name: 'ProsodyScore') required double prosodyScore,
    @JsonKey(name: 'CompletenessScore') required double completenessScore,
    @JsonKey(name: 'PronScore') required double pronScore,
  }) = _PronunciationAssessmentValue;

  factory PronunciationAssessmentValue.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAssessmentValueFromJson(json);
}

@freezed
sealed class PronunciationAssessmentWord with _$PronunciationAssessmentWord {
  const factory PronunciationAssessmentWord({
    @JsonKey(name: "Offset") int? offset,
    @JsonKey(name: "PronunciationAssessment")
    required WordPronunciationAssessment pronunciationAssessment,
    @JsonKey(name: "Duration") int? duration,
    @JsonKey(name: "Syllables") List<Syllable>? syllables,
    @JsonKey(name: "Word") required String word,
    @JsonKey(name: "Phonemes")
    required List<PronunciationAssessmentPhoneme> phonemes,
  }) = _PronunciationAssessmentWord;

  factory PronunciationAssessmentWord.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAssessmentWordFromJson(json);
}

@freezed
sealed class PronunciationAssessmentPhoneme
    with _$PronunciationAssessmentPhoneme {
  const factory PronunciationAssessmentPhoneme({
    @JsonKey(name: "Phoneme") required String phoneme,
    @JsonKey(name: "Offset") int? offset,
    @JsonKey(name: "PronunciationAssessment")
    required PhonemePronunciationAssessment pronunciationAssessment,
    @JsonKey(name: "Duration") int? duration,
  }) = _PronunciationAssessmentPhoneme;

  factory PronunciationAssessmentPhoneme.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAssessmentPhonemeFromJson(json);
}

@freezed
sealed class PhonemePronunciationAssessment
    with _$PhonemePronunciationAssessment {
  const factory PhonemePronunciationAssessment({
    @JsonKey(name: "AccuracyScore") required double accuracyScore,
  }) = _PhonemePronunciationAssessment;

  factory PhonemePronunciationAssessment.fromJson(Map<String, dynamic> json) =>
      _$PhonemePronunciationAssessmentFromJson(json);
}

@freezed
sealed class WordPronunciationAssessment with _$WordPronunciationAssessment {
  const factory WordPronunciationAssessment({
    @JsonKey(name: "ErrorType") required String errorType,
    @JsonKey(name: "AccuracyScore") double? accuracyScore,
    @JsonKey(name: "Feedback") Feedback? feedback,
  }) = _WordPronunciationAssessment;

  factory WordPronunciationAssessment.fromJson(Map<String, dynamic> json) =>
      _$WordPronunciationAssessmentFromJson(json);
}

@freezed
sealed class Feedback with _$Feedback {
  const factory Feedback({@JsonKey(name: "Prosody") required Prosody prosody}) =
      _Feedback;

  factory Feedback.fromJson(Map<String, dynamic> json) =>
      _$FeedbackFromJson(json);
}

@freezed
sealed class Prosody with _$Prosody {
  const factory Prosody({
    @JsonKey(name: "Break") required Break prosodyBreak,
    @JsonKey(name: "Intonation") required Intonation intonation,
  }) = _Prosody;

  factory Prosody.fromJson(Map<String, dynamic> json) =>
      _$ProsodyFromJson(json);
}

@freezed
sealed class Intonation with _$Intonation {
  const factory Intonation({
    @JsonKey(name: "ErrorTypes") required List<String> errorTypes,
    @JsonKey(name: "Monotone") required Monotone monotone,
  }) = _Intonation;

  factory Intonation.fromJson(Map<String, dynamic> json) =>
      _$IntonationFromJson(json);
}

@freezed
sealed class Monotone with _$Monotone {
  const factory Monotone({
    @JsonKey(name: "SyllablePitchDeltaConfidence")
    required double syllablePitchDeltaConfidence,
  }) = _Monotone;

  factory Monotone.fromJson(Map<String, dynamic> json) =>
      _$MonotoneFromJson(json);
}

@freezed
sealed class Break with _$Break {
  const factory Break({
    @JsonKey(name: "ErrorTypes") required List<String> errorTypes,
    @JsonKey(name: "UnexpectedBreak") UnexpectedBreak? unexpectedBreak,
    @JsonKey(name: "MissingBreak") MissingBreak? missingBreak,
    @JsonKey(name: "BreakLength") required int breakLength,
  }) = _Break;

  factory Break.fromJson(Map<String, dynamic> json) => _$BreakFromJson(json);
}

@freezed
sealed class UnexpectedBreak with _$UnexpectedBreak {
  const factory UnexpectedBreak({
    @JsonKey(name: "Confidence") required double confidence,
  }) = _UnexpectedBreak;

  factory UnexpectedBreak.fromJson(Map<String, dynamic> json) =>
      _$UnexpectedBreakFromJson(json);
}

@freezed
sealed class MissingBreak with _$MissingBreak {
  const factory MissingBreak({
    @JsonKey(name: "Confidence") required double confidence,
  }) = _MissingBreak;

  factory MissingBreak.fromJson(Map<String, dynamic> json) =>
      _$MissingBreakFromJson(json);
}

@freezed
sealed class Syllable with _$Syllable {
  const factory Syllable({
    @JsonKey(name: "Offset") int? offset,
    @JsonKey(name: "PronunciationAssessment")
    required PhonemePronunciationAssessment pronunciationAssessment,
    @JsonKey(name: "Grapheme") String? grapheme,
    @JsonKey(name: "Syllable") String? syllable,
    @JsonKey(name: "Duration") int? duration,
  }) = _Syllable;

  factory Syllable.fromJson(Map<String, dynamic> json) =>
      _$SyllableFromJson(json);
}
