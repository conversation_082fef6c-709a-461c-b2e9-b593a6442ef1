import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:change_case/change_case.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/profile_avatar.dart';

class DashboardHeader extends StatefulWidget {
  const DashboardHeader({super.key});

  @override
  State<DashboardHeader> createState() => _DashboardHeaderState();
}

class _DashboardHeaderState extends State<DashboardHeader>
    with AutomaticKeepAliveClientMixin {
  User? _cachedUser;
  bool? _cachedIsNewUser;
  String? _cachedUserName;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _cacheUserData();
  }

  void _cacheUserData() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null && _cachedUser?.uid != user.uid) {
      _cachedUser = user;
      _cachedIsNewUser =
          user.metadata.creationTime == user.metadata.lastSignInTime;
      _cachedUserName = user.displayName?.toCapitalCase() ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Use cached data to prevent unnecessary rebuilds
    _cacheUserData(); // Update cache if needed

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 5, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _UserGreeting(
            userName: _cachedUserName ?? '',
            isNewUser: _cachedIsNewUser ?? false,
          ),
          const ProfileAvatar(),
        ],
      ),
    );
  }
}

// Separate widget for user greeting to prevent rebuilds
class _UserGreeting extends StatelessWidget {
  final String userName;
  final bool isNewUser;

  const _UserGreeting({required this.userName, required this.isNewUser});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.loc.hiUser(userName),
          style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
        ),
        Text(
          isNewUser ? context.loc.welcome : context.loc.welcome_back,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is _UserGreeting &&
        other.userName == userName &&
        other.isNewUser == isNewUser;
  }

  @override
  int get hashCode => Object.hash(userName, isNewUser);
}
