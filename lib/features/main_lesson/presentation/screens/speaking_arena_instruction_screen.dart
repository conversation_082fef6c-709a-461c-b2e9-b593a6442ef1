import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/desc_with_image.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';

class SpeakingArenaInstructionScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const SpeakingArenaInstructionScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<SpeakingArenaInstructionScreen> createState() =>
      _SpeakingArenaInstructionScreenState();
}

class _SpeakingArenaInstructionScreenState
    extends ConsumerState<SpeakingArenaInstructionScreen>
    with TickerProviderStateMixin {
  late List<DefaultModel> _listItem;
  late List<DefaultModel> _listItem2;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  void init() {
    _isInitialized = true;
    _listItem = [
      DefaultModel(
        title: '1',
        image: '$assetImageMainLesson/speaking_arena/Panduan.png',
        child: DescWithImage(
          prefix: context.loc.click_the_button,
          sufix: context.loc.lm_instruction_decs1,
          image:
              '$assetImageMainLesson/speaking_arena/Icon - playpause material syms.png',
        ),
      ),
      DefaultModel(
        title: '2',
        image: '$assetImageMainLesson/speaking_arena/Panduan-1.png',
        child: DescWithImage(
          prefix: context.loc.click_the_button,
          sufix: context.loc.lm_instruction_decs2,
          image:
              '$assetImageMainLesson/speaking_arena/Icon - playpause material syms-1.png',
        ),
      ),
      DefaultModel(
        title: '3',
        image: '$assetImageMainLesson/speaking_arena/Panduan-2.png',
        child: DescWithImage(prefix: context.loc.lm_instruction_decs3),
      ),
      DefaultModel(
        title: '4',
        image:
            '$assetImageMainLesson/speaking_arena/Panduan-3${context.loc.localeName == 'en' ? '-Eng' : ''}.png',
        child: DescWithImage(
          prefix: context.loc.lm_instruction_decs4a,
          sufix: context.loc.lm_instruction_decs4b,
          customItem: Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            decoration: BoxDecoration(
              color: Color(0xffC00017),
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            child: Text(
              context.loc.next,
              style: Theme.of(
                context,
              ).textTheme.labelSmall!.copyWith(color: Colors.white),
            ),
          ),
        ),
      ),
    ];
    _listItem2 = [
      DefaultModel(
        title: '1',
        image: '$assetImageMainLesson/speaking_arena/Panduan1.1.png',
        child: DescWithImage(
          prefix: context.loc.click_the_button,
          sufix: context.loc.pc_instruction_decs2,
          image: '$assetImageMainLesson/speaking_arena/material syms.png',
        ),
      ),
      DefaultModel(
        title: '2',
        image: '$assetImageMainLesson/speaking_arena/Panduan1.2.png',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs3a,
          sufix: context.loc.pc_instruction_decs3b,
        ),
      ),
      DefaultModel(
        title: '3',
        image: '$assetImageMainLesson/speaking_arena/Panduan1.3.png',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs4a,
          sufix: context.loc.pc_instruction_decs4b,
          image: '$assetImageMainLesson/speaking_arena/Loading.png',
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) init();

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    LinearProgressIndicator(
                      value: 1 / 1,
                      backgroundColor: const Color(0xffFFDAD2),
                      valueColor: AlwaysStoppedAnimation(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 26),
                    Text(
                      '${context.loc.how_to_answer}: ${context.loc.stage} 1',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 32),
                    Column(
                      children:
                          _listItem
                              .map<Widget>(
                                (item) => instructionItem(item: item),
                              )
                              .toList(),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      '${context.loc.how_to_answer}: ${context.loc.stage} 2&3',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 32),
                    Column(
                      children:
                          _listItem2
                              .map<Widget>(
                                (item) => instructionItem(item: item),
                              )
                              .toList(),
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: VButtonGradient(
                  title: context.loc.next,
                  onTap: () {
                    customNav(
                      context,
                      RouterName.speakingArena,
                      isReplace: true,
                      params: {
                        'level': widget.level,
                        'chapter': widget.chapter,
                        'path': 'blankpath',
                      },
                    );
                  },
                  isBorder: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container instructionItem({required DefaultModel item}) => Container(
    padding: const EdgeInsets.symmetric(vertical: 20),
    child: Row(
      children: [
        Text('${item.title}.', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(width: 12),
        Container(
          height: 90,
          width: 90,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            image: DecorationImage(
              image: AssetImage(item.image),
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
        const SizedBox(width: 12),
        item.child,
      ],
    ),
  );
}
