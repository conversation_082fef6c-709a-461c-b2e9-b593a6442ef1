// observe_recall_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';

part 'memory_flash_state.freezed.dart';

enum GameStage { topic, loading, observe, recall, result, finished, gameOver }

@freezed
sealed class MemoryFlashState with _$MemoryFlashState {
  factory MemoryFlashState({
    @Default(GameStage.loading) GameStage gameStage,
    @Default([]) List<WordObject> activeWords,
    @Default([]) List<MemoryFlash> memoryFlash,
    @Default([]) List<String> words,
    @Default([]) List<String> wordRandom,
    @Default([]) List<String> wordChoices,
    @Default(0) int score,
    @Default(0) int selectedTopic,
    @Default(0.3) double gameSpeed,
    @Default(false) bool isPlaying,
    MemoryFlashTopic? topic,
    @Default(0) int currentTopicIndex,
    @Default(0) int currentLevelIndex,
  }) = _MemoryFlashState;
}
