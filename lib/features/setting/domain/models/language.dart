// ignore_for_file: invalid_annotation_target

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'language.freezed.dart';
part 'language.g.dart';

typedef LanguageList = List<Language>;

@freezed
sealed class Language with _$Language {
  factory Language({
    @Default('') String title,
    @Default('') String icon,
    @Default('') String imageBackground,
    @JsonKey(includeFromJson: false) Locale? localValue,
  }) = _Language;

  factory Language.fromJson(Map<String, dynamic> json) =>
      _$LanguageFromJson(json);
}
