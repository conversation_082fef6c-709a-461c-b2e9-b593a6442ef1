import 'dart:ui';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/setting_cache_service/data/datasource/setting_local_datasource.dart';
import 'package:selfeng/services/setting_cache_service/data/repositories/setting_repository_impl.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

// Mock classes
class MockSettingDataSource extends Mock implements SettingDataSource {}

void main() {
  group('SettingCacheRepositoryImpl', () {
    late SettingCacheRepositoryImpl repository;
    late MockSettingDataSource mockDataSource;

    setUp(() {
      mockDataSource = MockSettingDataSource();
      repository = SettingCacheRepositoryImpl(mockDataSource);
    });

    group('fetchLocale', () {
      test('should fetch locale from data source', () async {
        // Arrange
        final locale = const Locale('en', 'US');
        when(
          () => mockDataSource.fetchLocale(),
        ).thenAnswer((_) async => Right(locale));

        // Act
        final result = await repository.fetchLocale();

        // Assert
        expect(result, isA<Right<AppException, Locale>>());
        expect((result as Right<AppException, Locale>).value, equals(locale));
        verify(() => mockDataSource.fetchLocale()).called(1);
      });

      test(
        'should return Left with AppException when fetching locale fails',
        () async {
          // Arrange
          final exception = AppException(
            identifier: 'SettingLocalDatasource',
            statusCode: 404,
            message: 'User not found',
          );
          when(
            () => mockDataSource.fetchLocale(),
          ).thenAnswer((_) async => Left(exception));

          // Act
          final result = await repository.fetchLocale();

          // Assert
          expect(result, isA<Left<AppException, Locale>>());
          expect(
            (result as Left<AppException, Locale>).value,
            equals(exception),
          );
          verify(() => mockDataSource.fetchLocale()).called(1);
        },
      );
    });

    group('saveLocale', () {
      test('should save locale to data source', () async {
        // Arrange
        final locale = const Locale('en', 'US');
        when(
          () => mockDataSource.saveLocale(locale: locale),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.saveLocale(locale: locale);

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.saveLocale(locale: locale)).called(1);
      });

      test('should return false when saving locale fails', () async {
        // Arrange
        final locale = const Locale('en', 'US');
        when(
          () => mockDataSource.saveLocale(locale: locale),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.saveLocale(locale: locale);

        // Assert
        expect(result, isFalse);
        verify(() => mockDataSource.saveLocale(locale: locale)).called(1);
      });
    });

    group('deleteLocale', () {
      test('should delete locale from data source', () async {
        // Arrange
        when(() => mockDataSource.deleteLocale()).thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteLocale();

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.deleteLocale()).called(1);
      });

      test('should return false when deleting locale fails', () async {
        // Arrange
        when(
          () => mockDataSource.deleteLocale(),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.deleteLocale();

        // Assert
        expect(result, isFalse);
        verify(() => mockDataSource.deleteLocale()).called(1);
      });
    });

    group('hasLocale', () {
      test('should check if locale exists in data source', () async {
        // Arrange
        when(() => mockDataSource.hasLocale()).thenAnswer((_) async => true);

        // Act
        final result = await repository.hasLocale();

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.hasLocale()).called(1);
      });

      test(
        'should return false when locale does not exist in data source',
        () async {
          // Arrange
          when(() => mockDataSource.hasLocale()).thenAnswer((_) async => false);

          // Act
          final result = await repository.hasLocale();

          // Assert
          expect(result, isFalse);
          verify(() => mockDataSource.hasLocale()).called(1);
        },
      );
    });

    group('getAudioEnabled', () {
      test('should get audio enabled state from data source', () async {
        // Arrange
        when(
          () => mockDataSource.getAudioEnabled(),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await repository.getAudioEnabled();

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isTrue);
        verify(() => mockDataSource.getAudioEnabled()).called(1);
      });

      test(
        'should return Left with AppException when getting audio enabled fails',
        () async {
          // Arrange
          final exception = AppException(
            identifier: 'SettingLocalDatasource.getAudioEnabled',
            message: 'Failed to get audio state',
            statusCode: 500,
          );
          when(
            () => mockDataSource.getAudioEnabled(),
          ).thenAnswer((_) async => Left(exception));

          // Act
          final result = await repository.getAudioEnabled();

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect((result as Left<AppException, bool>).value, equals(exception));
          verify(() => mockDataSource.getAudioEnabled()).called(1);
        },
      );
    });

    group('saveAudioEnabled', () {
      test('should save audio enabled state to data source', () async {
        // Arrange
        when(
          () => mockDataSource.saveAudioEnabled(enabled: true),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.saveAudioEnabled(enabled: true);

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.saveAudioEnabled(enabled: true)).called(1);
      });

      test(
        'should return false when saving audio enabled state fails',
        () async {
          // Arrange
          when(
            () => mockDataSource.saveAudioEnabled(enabled: false),
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.saveAudioEnabled(enabled: false);

          // Assert
          expect(result, isFalse);
          verify(
            () => mockDataSource.saveAudioEnabled(enabled: false),
          ).called(1);
        },
      );
    });
  });
}
