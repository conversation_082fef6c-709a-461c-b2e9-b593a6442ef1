import 'package:flutter/material.dart';

class Desc<PERSON>ithImage extends StatelessWidget {
  const DescWithImage({
    super.key,
    required this.prefix,
    this.sufix,
    this.image,
    this.customItem,
  });

  final String prefix;
  final String? sufix;
  final String? image;
  final Widget? customItem;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width / 2,
      child: RichText(
        text: TextSpan(
          style: Theme.of(context).textTheme.titleLarge,
          children: [
            TextSpan(text: '$prefix '),
            if (customItem != null) WidgetSpan(child: customItem!),
            if (image != null)
              WidgetSpan(child: Image.asset(image!, height: 32)),
            if (sufix != null) TextSpan(text: ' $sufix'),
          ],
        ),
      ),
    );
  }
}
