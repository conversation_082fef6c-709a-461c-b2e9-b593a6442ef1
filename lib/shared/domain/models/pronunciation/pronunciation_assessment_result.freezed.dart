// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pronunciation_assessment_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PronunciationAssessmentResult {

@JsonKey(name: 'result') PronunciationAssessmentResultData get result;
/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentResultCopyWith<PronunciationAssessmentResult> get copyWith => _$PronunciationAssessmentResultCopyWithImpl<PronunciationAssessmentResult>(this as PronunciationAssessmentResult, _$identity);

  /// Serializes this PronunciationAssessmentResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentResult&&(identical(other.result, result) || other.result == result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString() {
  return 'PronunciationAssessmentResult(result: $result)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentResultCopyWith<$Res>  {
  factory $PronunciationAssessmentResultCopyWith(PronunciationAssessmentResult value, $Res Function(PronunciationAssessmentResult) _then) = _$PronunciationAssessmentResultCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'result') PronunciationAssessmentResultData result
});


$PronunciationAssessmentResultDataCopyWith<$Res> get result;

}
/// @nodoc
class _$PronunciationAssessmentResultCopyWithImpl<$Res>
    implements $PronunciationAssessmentResultCopyWith<$Res> {
  _$PronunciationAssessmentResultCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentResult _self;
  final $Res Function(PronunciationAssessmentResult) _then;

/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? result = null,}) {
  return _then(_self.copyWith(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentResultData,
  ));
}
/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentResultDataCopyWith<$Res> get result {
  
  return $PronunciationAssessmentResultDataCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationAssessmentResult].
extension PronunciationAssessmentResultPatterns on PronunciationAssessmentResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentResult value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentResult value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'result')  PronunciationAssessmentResultData result)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResult() when $default != null:
return $default(_that.result);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'result')  PronunciationAssessmentResultData result)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResult():
return $default(_that.result);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'result')  PronunciationAssessmentResultData result)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResult() when $default != null:
return $default(_that.result);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationAssessmentResult implements PronunciationAssessmentResult {
   _PronunciationAssessmentResult({@JsonKey(name: 'result') required this.result});
  factory _PronunciationAssessmentResult.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentResultFromJson(json);

@override@JsonKey(name: 'result') final  PronunciationAssessmentResultData result;

/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentResultCopyWith<_PronunciationAssessmentResult> get copyWith => __$PronunciationAssessmentResultCopyWithImpl<_PronunciationAssessmentResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentResult&&(identical(other.result, result) || other.result == result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString() {
  return 'PronunciationAssessmentResult(result: $result)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentResultCopyWith<$Res> implements $PronunciationAssessmentResultCopyWith<$Res> {
  factory _$PronunciationAssessmentResultCopyWith(_PronunciationAssessmentResult value, $Res Function(_PronunciationAssessmentResult) _then) = __$PronunciationAssessmentResultCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'result') PronunciationAssessmentResultData result
});


@override $PronunciationAssessmentResultDataCopyWith<$Res> get result;

}
/// @nodoc
class __$PronunciationAssessmentResultCopyWithImpl<$Res>
    implements _$PronunciationAssessmentResultCopyWith<$Res> {
  __$PronunciationAssessmentResultCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentResult _self;
  final $Res Function(_PronunciationAssessmentResult) _then;

/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_PronunciationAssessmentResult(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentResultData,
  ));
}

/// Create a copy of PronunciationAssessmentResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentResultDataCopyWith<$Res> get result {
  
  return $PronunciationAssessmentResultDataCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}


/// @nodoc
mixin _$PronunciationAssessmentResultData {

@JsonKey(name: 'privPronJson') PronunciationAssessmentPriv get privPronJson;
/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentResultDataCopyWith<PronunciationAssessmentResultData> get copyWith => _$PronunciationAssessmentResultDataCopyWithImpl<PronunciationAssessmentResultData>(this as PronunciationAssessmentResultData, _$identity);

  /// Serializes this PronunciationAssessmentResultData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentResultData&&(identical(other.privPronJson, privPronJson) || other.privPronJson == privPronJson));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,privPronJson);

@override
String toString() {
  return 'PronunciationAssessmentResultData(privPronJson: $privPronJson)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentResultDataCopyWith<$Res>  {
  factory $PronunciationAssessmentResultDataCopyWith(PronunciationAssessmentResultData value, $Res Function(PronunciationAssessmentResultData) _then) = _$PronunciationAssessmentResultDataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'privPronJson') PronunciationAssessmentPriv privPronJson
});


$PronunciationAssessmentPrivCopyWith<$Res> get privPronJson;

}
/// @nodoc
class _$PronunciationAssessmentResultDataCopyWithImpl<$Res>
    implements $PronunciationAssessmentResultDataCopyWith<$Res> {
  _$PronunciationAssessmentResultDataCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentResultData _self;
  final $Res Function(PronunciationAssessmentResultData) _then;

/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? privPronJson = null,}) {
  return _then(_self.copyWith(
privPronJson: null == privPronJson ? _self.privPronJson : privPronJson // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentPriv,
  ));
}
/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentPrivCopyWith<$Res> get privPronJson {
  
  return $PronunciationAssessmentPrivCopyWith<$Res>(_self.privPronJson, (value) {
    return _then(_self.copyWith(privPronJson: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationAssessmentResultData].
extension PronunciationAssessmentResultDataPatterns on PronunciationAssessmentResultData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentResultData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentResultData value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentResultData value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'privPronJson')  PronunciationAssessmentPriv privPronJson)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData() when $default != null:
return $default(_that.privPronJson);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'privPronJson')  PronunciationAssessmentPriv privPronJson)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData():
return $default(_that.privPronJson);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'privPronJson')  PronunciationAssessmentPriv privPronJson)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentResultData() when $default != null:
return $default(_that.privPronJson);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationAssessmentResultData implements PronunciationAssessmentResultData {
   _PronunciationAssessmentResultData({@JsonKey(name: 'privPronJson') required this.privPronJson});
  factory _PronunciationAssessmentResultData.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentResultDataFromJson(json);

@override@JsonKey(name: 'privPronJson') final  PronunciationAssessmentPriv privPronJson;

/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentResultDataCopyWith<_PronunciationAssessmentResultData> get copyWith => __$PronunciationAssessmentResultDataCopyWithImpl<_PronunciationAssessmentResultData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentResultDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentResultData&&(identical(other.privPronJson, privPronJson) || other.privPronJson == privPronJson));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,privPronJson);

@override
String toString() {
  return 'PronunciationAssessmentResultData(privPronJson: $privPronJson)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentResultDataCopyWith<$Res> implements $PronunciationAssessmentResultDataCopyWith<$Res> {
  factory _$PronunciationAssessmentResultDataCopyWith(_PronunciationAssessmentResultData value, $Res Function(_PronunciationAssessmentResultData) _then) = __$PronunciationAssessmentResultDataCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'privPronJson') PronunciationAssessmentPriv privPronJson
});


@override $PronunciationAssessmentPrivCopyWith<$Res> get privPronJson;

}
/// @nodoc
class __$PronunciationAssessmentResultDataCopyWithImpl<$Res>
    implements _$PronunciationAssessmentResultDataCopyWith<$Res> {
  __$PronunciationAssessmentResultDataCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentResultData _self;
  final $Res Function(_PronunciationAssessmentResultData) _then;

/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? privPronJson = null,}) {
  return _then(_PronunciationAssessmentResultData(
privPronJson: null == privPronJson ? _self.privPronJson : privPronJson // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentPriv,
  ));
}

/// Create a copy of PronunciationAssessmentResultData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentPrivCopyWith<$Res> get privPronJson {
  
  return $PronunciationAssessmentPrivCopyWith<$Res>(_self.privPronJson, (value) {
    return _then(_self.copyWith(privPronJson: value));
  });
}
}


/// @nodoc
mixin _$PronunciationAssessmentPriv {

@JsonKey(name: 'Confidence') double get confidence;@JsonKey(name: 'Lexical') String get lexical;@JsonKey(name: 'ITN') String get itn;@JsonKey(name: 'MaskedITN') String get maskedItn;@JsonKey(name: 'Display') String get display;@JsonKey(name: 'PronunciationAssessment') PronunciationAssessmentValue get pronunciationAssessment;@JsonKey(name: 'Words') List<PronunciationAssessmentWord> get words;
/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentPrivCopyWith<PronunciationAssessmentPriv> get copyWith => _$PronunciationAssessmentPrivCopyWithImpl<PronunciationAssessmentPriv>(this as PronunciationAssessmentPriv, _$identity);

  /// Serializes this PronunciationAssessmentPriv to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentPriv&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.lexical, lexical) || other.lexical == lexical)&&(identical(other.itn, itn) || other.itn == itn)&&(identical(other.maskedItn, maskedItn) || other.maskedItn == maskedItn)&&(identical(other.display, display) || other.display == display)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&const DeepCollectionEquality().equals(other.words, words));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence,lexical,itn,maskedItn,display,pronunciationAssessment,const DeepCollectionEquality().hash(words));

@override
String toString() {
  return 'PronunciationAssessmentPriv(confidence: $confidence, lexical: $lexical, itn: $itn, maskedItn: $maskedItn, display: $display, pronunciationAssessment: $pronunciationAssessment, words: $words)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentPrivCopyWith<$Res>  {
  factory $PronunciationAssessmentPrivCopyWith(PronunciationAssessmentPriv value, $Res Function(PronunciationAssessmentPriv) _then) = _$PronunciationAssessmentPrivCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'Confidence') double confidence,@JsonKey(name: 'Lexical') String lexical,@JsonKey(name: 'ITN') String itn,@JsonKey(name: 'MaskedITN') String maskedItn,@JsonKey(name: 'Display') String display,@JsonKey(name: 'PronunciationAssessment') PronunciationAssessmentValue pronunciationAssessment,@JsonKey(name: 'Words') List<PronunciationAssessmentWord> words
});


$PronunciationAssessmentValueCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class _$PronunciationAssessmentPrivCopyWithImpl<$Res>
    implements $PronunciationAssessmentPrivCopyWith<$Res> {
  _$PronunciationAssessmentPrivCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentPriv _self;
  final $Res Function(PronunciationAssessmentPriv) _then;

/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? confidence = null,Object? lexical = null,Object? itn = null,Object? maskedItn = null,Object? display = null,Object? pronunciationAssessment = null,Object? words = null,}) {
  return _then(_self.copyWith(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,lexical: null == lexical ? _self.lexical : lexical // ignore: cast_nullable_to_non_nullable
as String,itn: null == itn ? _self.itn : itn // ignore: cast_nullable_to_non_nullable
as String,maskedItn: null == maskedItn ? _self.maskedItn : maskedItn // ignore: cast_nullable_to_non_nullable
as String,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as String,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentValue,words: null == words ? _self.words : words // ignore: cast_nullable_to_non_nullable
as List<PronunciationAssessmentWord>,
  ));
}
/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentValueCopyWith<$Res> get pronunciationAssessment {
  
  return $PronunciationAssessmentValueCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationAssessmentPriv].
extension PronunciationAssessmentPrivPatterns on PronunciationAssessmentPriv {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentPriv value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentPriv value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentPriv value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'Confidence')  double confidence, @JsonKey(name: 'Lexical')  String lexical, @JsonKey(name: 'ITN')  String itn, @JsonKey(name: 'MaskedITN')  String maskedItn, @JsonKey(name: 'Display')  String display, @JsonKey(name: 'PronunciationAssessment')  PronunciationAssessmentValue pronunciationAssessment, @JsonKey(name: 'Words')  List<PronunciationAssessmentWord> words)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv() when $default != null:
return $default(_that.confidence,_that.lexical,_that.itn,_that.maskedItn,_that.display,_that.pronunciationAssessment,_that.words);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'Confidence')  double confidence, @JsonKey(name: 'Lexical')  String lexical, @JsonKey(name: 'ITN')  String itn, @JsonKey(name: 'MaskedITN')  String maskedItn, @JsonKey(name: 'Display')  String display, @JsonKey(name: 'PronunciationAssessment')  PronunciationAssessmentValue pronunciationAssessment, @JsonKey(name: 'Words')  List<PronunciationAssessmentWord> words)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv():
return $default(_that.confidence,_that.lexical,_that.itn,_that.maskedItn,_that.display,_that.pronunciationAssessment,_that.words);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'Confidence')  double confidence, @JsonKey(name: 'Lexical')  String lexical, @JsonKey(name: 'ITN')  String itn, @JsonKey(name: 'MaskedITN')  String maskedItn, @JsonKey(name: 'Display')  String display, @JsonKey(name: 'PronunciationAssessment')  PronunciationAssessmentValue pronunciationAssessment, @JsonKey(name: 'Words')  List<PronunciationAssessmentWord> words)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPriv() when $default != null:
return $default(_that.confidence,_that.lexical,_that.itn,_that.maskedItn,_that.display,_that.pronunciationAssessment,_that.words);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _PronunciationAssessmentPriv implements PronunciationAssessmentPriv {
   _PronunciationAssessmentPriv({@JsonKey(name: 'Confidence') required this.confidence, @JsonKey(name: 'Lexical') required this.lexical, @JsonKey(name: 'ITN') required this.itn, @JsonKey(name: 'MaskedITN') required this.maskedItn, @JsonKey(name: 'Display') required this.display, @JsonKey(name: 'PronunciationAssessment') required this.pronunciationAssessment, @JsonKey(name: 'Words') required final  List<PronunciationAssessmentWord> words}): _words = words;
  factory _PronunciationAssessmentPriv.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentPrivFromJson(json);

@override@JsonKey(name: 'Confidence') final  double confidence;
@override@JsonKey(name: 'Lexical') final  String lexical;
@override@JsonKey(name: 'ITN') final  String itn;
@override@JsonKey(name: 'MaskedITN') final  String maskedItn;
@override@JsonKey(name: 'Display') final  String display;
@override@JsonKey(name: 'PronunciationAssessment') final  PronunciationAssessmentValue pronunciationAssessment;
 final  List<PronunciationAssessmentWord> _words;
@override@JsonKey(name: 'Words') List<PronunciationAssessmentWord> get words {
  if (_words is EqualUnmodifiableListView) return _words;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_words);
}


/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentPrivCopyWith<_PronunciationAssessmentPriv> get copyWith => __$PronunciationAssessmentPrivCopyWithImpl<_PronunciationAssessmentPriv>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentPrivToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentPriv&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.lexical, lexical) || other.lexical == lexical)&&(identical(other.itn, itn) || other.itn == itn)&&(identical(other.maskedItn, maskedItn) || other.maskedItn == maskedItn)&&(identical(other.display, display) || other.display == display)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&const DeepCollectionEquality().equals(other._words, _words));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence,lexical,itn,maskedItn,display,pronunciationAssessment,const DeepCollectionEquality().hash(_words));

@override
String toString() {
  return 'PronunciationAssessmentPriv(confidence: $confidence, lexical: $lexical, itn: $itn, maskedItn: $maskedItn, display: $display, pronunciationAssessment: $pronunciationAssessment, words: $words)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentPrivCopyWith<$Res> implements $PronunciationAssessmentPrivCopyWith<$Res> {
  factory _$PronunciationAssessmentPrivCopyWith(_PronunciationAssessmentPriv value, $Res Function(_PronunciationAssessmentPriv) _then) = __$PronunciationAssessmentPrivCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'Confidence') double confidence,@JsonKey(name: 'Lexical') String lexical,@JsonKey(name: 'ITN') String itn,@JsonKey(name: 'MaskedITN') String maskedItn,@JsonKey(name: 'Display') String display,@JsonKey(name: 'PronunciationAssessment') PronunciationAssessmentValue pronunciationAssessment,@JsonKey(name: 'Words') List<PronunciationAssessmentWord> words
});


@override $PronunciationAssessmentValueCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class __$PronunciationAssessmentPrivCopyWithImpl<$Res>
    implements _$PronunciationAssessmentPrivCopyWith<$Res> {
  __$PronunciationAssessmentPrivCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentPriv _self;
  final $Res Function(_PronunciationAssessmentPriv) _then;

/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? confidence = null,Object? lexical = null,Object? itn = null,Object? maskedItn = null,Object? display = null,Object? pronunciationAssessment = null,Object? words = null,}) {
  return _then(_PronunciationAssessmentPriv(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,lexical: null == lexical ? _self.lexical : lexical // ignore: cast_nullable_to_non_nullable
as String,itn: null == itn ? _self.itn : itn // ignore: cast_nullable_to_non_nullable
as String,maskedItn: null == maskedItn ? _self.maskedItn : maskedItn // ignore: cast_nullable_to_non_nullable
as String,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as String,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PronunciationAssessmentValue,words: null == words ? _self._words : words // ignore: cast_nullable_to_non_nullable
as List<PronunciationAssessmentWord>,
  ));
}

/// Create a copy of PronunciationAssessmentPriv
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAssessmentValueCopyWith<$Res> get pronunciationAssessment {
  
  return $PronunciationAssessmentValueCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// @nodoc
mixin _$PronunciationAssessmentValue {

@JsonKey(name: 'AccuracyScore') double get accuracyScore;@JsonKey(name: 'FluencyScore') double get fluencyScore;@JsonKey(name: 'ProsodyScore') double get prosodyScore;@JsonKey(name: 'CompletenessScore') double get completenessScore;@JsonKey(name: 'PronScore') double get pronScore;
/// Create a copy of PronunciationAssessmentValue
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentValueCopyWith<PronunciationAssessmentValue> get copyWith => _$PronunciationAssessmentValueCopyWithImpl<PronunciationAssessmentValue>(this as PronunciationAssessmentValue, _$identity);

  /// Serializes this PronunciationAssessmentValue to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentValue&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore);

@override
String toString() {
  return 'PronunciationAssessmentValue(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentValueCopyWith<$Res>  {
  factory $PronunciationAssessmentValueCopyWith(PronunciationAssessmentValue value, $Res Function(PronunciationAssessmentValue) _then) = _$PronunciationAssessmentValueCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'AccuracyScore') double accuracyScore,@JsonKey(name: 'FluencyScore') double fluencyScore,@JsonKey(name: 'ProsodyScore') double prosodyScore,@JsonKey(name: 'CompletenessScore') double completenessScore,@JsonKey(name: 'PronScore') double pronScore
});




}
/// @nodoc
class _$PronunciationAssessmentValueCopyWithImpl<$Res>
    implements $PronunciationAssessmentValueCopyWith<$Res> {
  _$PronunciationAssessmentValueCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentValue _self;
  final $Res Function(PronunciationAssessmentValue) _then;

/// Create a copy of PronunciationAssessmentValue
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,}) {
  return _then(_self.copyWith(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationAssessmentValue].
extension PronunciationAssessmentValuePatterns on PronunciationAssessmentValue {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentValue value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentValue() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentValue value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentValue():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentValue value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentValue() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'AccuracyScore')  double accuracyScore, @JsonKey(name: 'FluencyScore')  double fluencyScore, @JsonKey(name: 'ProsodyScore')  double prosodyScore, @JsonKey(name: 'CompletenessScore')  double completenessScore, @JsonKey(name: 'PronScore')  double pronScore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentValue() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'AccuracyScore')  double accuracyScore, @JsonKey(name: 'FluencyScore')  double fluencyScore, @JsonKey(name: 'ProsodyScore')  double prosodyScore, @JsonKey(name: 'CompletenessScore')  double completenessScore, @JsonKey(name: 'PronScore')  double pronScore)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentValue():
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'AccuracyScore')  double accuracyScore, @JsonKey(name: 'FluencyScore')  double fluencyScore, @JsonKey(name: 'ProsodyScore')  double prosodyScore, @JsonKey(name: 'CompletenessScore')  double completenessScore, @JsonKey(name: 'PronScore')  double pronScore)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentValue() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _PronunciationAssessmentValue implements PronunciationAssessmentValue {
   _PronunciationAssessmentValue({@JsonKey(name: 'AccuracyScore') required this.accuracyScore, @JsonKey(name: 'FluencyScore') required this.fluencyScore, @JsonKey(name: 'ProsodyScore') required this.prosodyScore, @JsonKey(name: 'CompletenessScore') required this.completenessScore, @JsonKey(name: 'PronScore') required this.pronScore});
  factory _PronunciationAssessmentValue.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentValueFromJson(json);

@override@JsonKey(name: 'AccuracyScore') final  double accuracyScore;
@override@JsonKey(name: 'FluencyScore') final  double fluencyScore;
@override@JsonKey(name: 'ProsodyScore') final  double prosodyScore;
@override@JsonKey(name: 'CompletenessScore') final  double completenessScore;
@override@JsonKey(name: 'PronScore') final  double pronScore;

/// Create a copy of PronunciationAssessmentValue
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentValueCopyWith<_PronunciationAssessmentValue> get copyWith => __$PronunciationAssessmentValueCopyWithImpl<_PronunciationAssessmentValue>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentValueToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentValue&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore);

@override
String toString() {
  return 'PronunciationAssessmentValue(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentValueCopyWith<$Res> implements $PronunciationAssessmentValueCopyWith<$Res> {
  factory _$PronunciationAssessmentValueCopyWith(_PronunciationAssessmentValue value, $Res Function(_PronunciationAssessmentValue) _then) = __$PronunciationAssessmentValueCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'AccuracyScore') double accuracyScore,@JsonKey(name: 'FluencyScore') double fluencyScore,@JsonKey(name: 'ProsodyScore') double prosodyScore,@JsonKey(name: 'CompletenessScore') double completenessScore,@JsonKey(name: 'PronScore') double pronScore
});




}
/// @nodoc
class __$PronunciationAssessmentValueCopyWithImpl<$Res>
    implements _$PronunciationAssessmentValueCopyWith<$Res> {
  __$PronunciationAssessmentValueCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentValue _self;
  final $Res Function(_PronunciationAssessmentValue) _then;

/// Create a copy of PronunciationAssessmentValue
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,}) {
  return _then(_PronunciationAssessmentValue(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$PronunciationAssessmentWord {

@JsonKey(name: "Offset") int? get offset;@JsonKey(name: "PronunciationAssessment") WordPronunciationAssessment get pronunciationAssessment;@JsonKey(name: "Duration") int? get duration;@JsonKey(name: "Syllables") List<Syllable>? get syllables;@JsonKey(name: "Word") String get word;@JsonKey(name: "Phonemes") List<PronunciationAssessmentPhoneme> get phonemes;
/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentWordCopyWith<PronunciationAssessmentWord> get copyWith => _$PronunciationAssessmentWordCopyWithImpl<PronunciationAssessmentWord>(this as PronunciationAssessmentWord, _$identity);

  /// Serializes this PronunciationAssessmentWord to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentWord&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other.syllables, syllables)&&(identical(other.word, word) || other.word == word)&&const DeepCollectionEquality().equals(other.phonemes, phonemes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,offset,pronunciationAssessment,duration,const DeepCollectionEquality().hash(syllables),word,const DeepCollectionEquality().hash(phonemes));

@override
String toString() {
  return 'PronunciationAssessmentWord(offset: $offset, pronunciationAssessment: $pronunciationAssessment, duration: $duration, syllables: $syllables, word: $word, phonemes: $phonemes)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentWordCopyWith<$Res>  {
  factory $PronunciationAssessmentWordCopyWith(PronunciationAssessmentWord value, $Res Function(PronunciationAssessmentWord) _then) = _$PronunciationAssessmentWordCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") WordPronunciationAssessment pronunciationAssessment,@JsonKey(name: "Duration") int? duration,@JsonKey(name: "Syllables") List<Syllable>? syllables,@JsonKey(name: "Word") String word,@JsonKey(name: "Phonemes") List<PronunciationAssessmentPhoneme> phonemes
});


$WordPronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class _$PronunciationAssessmentWordCopyWithImpl<$Res>
    implements $PronunciationAssessmentWordCopyWith<$Res> {
  _$PronunciationAssessmentWordCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentWord _self;
  final $Res Function(PronunciationAssessmentWord) _then;

/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? offset = freezed,Object? pronunciationAssessment = null,Object? duration = freezed,Object? syllables = freezed,Object? word = null,Object? phonemes = null,}) {
  return _then(_self.copyWith(
offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as WordPronunciationAssessment,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,syllables: freezed == syllables ? _self.syllables : syllables // ignore: cast_nullable_to_non_nullable
as List<Syllable>?,word: null == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String,phonemes: null == phonemes ? _self.phonemes : phonemes // ignore: cast_nullable_to_non_nullable
as List<PronunciationAssessmentPhoneme>,
  ));
}
/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordPronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $WordPronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationAssessmentWord].
extension PronunciationAssessmentWordPatterns on PronunciationAssessmentWord {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentWord value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentWord() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentWord value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentWord():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentWord value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentWord() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  WordPronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration, @JsonKey(name: "Syllables")  List<Syllable>? syllables, @JsonKey(name: "Word")  String word, @JsonKey(name: "Phonemes")  List<PronunciationAssessmentPhoneme> phonemes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentWord() when $default != null:
return $default(_that.offset,_that.pronunciationAssessment,_that.duration,_that.syllables,_that.word,_that.phonemes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  WordPronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration, @JsonKey(name: "Syllables")  List<Syllable>? syllables, @JsonKey(name: "Word")  String word, @JsonKey(name: "Phonemes")  List<PronunciationAssessmentPhoneme> phonemes)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentWord():
return $default(_that.offset,_that.pronunciationAssessment,_that.duration,_that.syllables,_that.word,_that.phonemes);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  WordPronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration, @JsonKey(name: "Syllables")  List<Syllable>? syllables, @JsonKey(name: "Word")  String word, @JsonKey(name: "Phonemes")  List<PronunciationAssessmentPhoneme> phonemes)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentWord() when $default != null:
return $default(_that.offset,_that.pronunciationAssessment,_that.duration,_that.syllables,_that.word,_that.phonemes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationAssessmentWord implements PronunciationAssessmentWord {
  const _PronunciationAssessmentWord({@JsonKey(name: "Offset") this.offset, @JsonKey(name: "PronunciationAssessment") required this.pronunciationAssessment, @JsonKey(name: "Duration") this.duration, @JsonKey(name: "Syllables") final  List<Syllable>? syllables, @JsonKey(name: "Word") required this.word, @JsonKey(name: "Phonemes") required final  List<PronunciationAssessmentPhoneme> phonemes}): _syllables = syllables,_phonemes = phonemes;
  factory _PronunciationAssessmentWord.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentWordFromJson(json);

@override@JsonKey(name: "Offset") final  int? offset;
@override@JsonKey(name: "PronunciationAssessment") final  WordPronunciationAssessment pronunciationAssessment;
@override@JsonKey(name: "Duration") final  int? duration;
 final  List<Syllable>? _syllables;
@override@JsonKey(name: "Syllables") List<Syllable>? get syllables {
  final value = _syllables;
  if (value == null) return null;
  if (_syllables is EqualUnmodifiableListView) return _syllables;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey(name: "Word") final  String word;
 final  List<PronunciationAssessmentPhoneme> _phonemes;
@override@JsonKey(name: "Phonemes") List<PronunciationAssessmentPhoneme> get phonemes {
  if (_phonemes is EqualUnmodifiableListView) return _phonemes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_phonemes);
}


/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentWordCopyWith<_PronunciationAssessmentWord> get copyWith => __$PronunciationAssessmentWordCopyWithImpl<_PronunciationAssessmentWord>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentWordToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentWord&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other._syllables, _syllables)&&(identical(other.word, word) || other.word == word)&&const DeepCollectionEquality().equals(other._phonemes, _phonemes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,offset,pronunciationAssessment,duration,const DeepCollectionEquality().hash(_syllables),word,const DeepCollectionEquality().hash(_phonemes));

@override
String toString() {
  return 'PronunciationAssessmentWord(offset: $offset, pronunciationAssessment: $pronunciationAssessment, duration: $duration, syllables: $syllables, word: $word, phonemes: $phonemes)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentWordCopyWith<$Res> implements $PronunciationAssessmentWordCopyWith<$Res> {
  factory _$PronunciationAssessmentWordCopyWith(_PronunciationAssessmentWord value, $Res Function(_PronunciationAssessmentWord) _then) = __$PronunciationAssessmentWordCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") WordPronunciationAssessment pronunciationAssessment,@JsonKey(name: "Duration") int? duration,@JsonKey(name: "Syllables") List<Syllable>? syllables,@JsonKey(name: "Word") String word,@JsonKey(name: "Phonemes") List<PronunciationAssessmentPhoneme> phonemes
});


@override $WordPronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class __$PronunciationAssessmentWordCopyWithImpl<$Res>
    implements _$PronunciationAssessmentWordCopyWith<$Res> {
  __$PronunciationAssessmentWordCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentWord _self;
  final $Res Function(_PronunciationAssessmentWord) _then;

/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? offset = freezed,Object? pronunciationAssessment = null,Object? duration = freezed,Object? syllables = freezed,Object? word = null,Object? phonemes = null,}) {
  return _then(_PronunciationAssessmentWord(
offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as WordPronunciationAssessment,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,syllables: freezed == syllables ? _self._syllables : syllables // ignore: cast_nullable_to_non_nullable
as List<Syllable>?,word: null == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String,phonemes: null == phonemes ? _self._phonemes : phonemes // ignore: cast_nullable_to_non_nullable
as List<PronunciationAssessmentPhoneme>,
  ));
}

/// Create a copy of PronunciationAssessmentWord
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordPronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $WordPronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// @nodoc
mixin _$PronunciationAssessmentPhoneme {

@JsonKey(name: "Phoneme") String get phoneme;@JsonKey(name: "Offset") int? get offset;@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment get pronunciationAssessment;@JsonKey(name: "Duration") int? get duration;
/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAssessmentPhonemeCopyWith<PronunciationAssessmentPhoneme> get copyWith => _$PronunciationAssessmentPhonemeCopyWithImpl<PronunciationAssessmentPhoneme>(this as PronunciationAssessmentPhoneme, _$identity);

  /// Serializes this PronunciationAssessmentPhoneme to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAssessmentPhoneme&&(identical(other.phoneme, phoneme) || other.phoneme == phoneme)&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.duration, duration) || other.duration == duration));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phoneme,offset,pronunciationAssessment,duration);

@override
String toString() {
  return 'PronunciationAssessmentPhoneme(phoneme: $phoneme, offset: $offset, pronunciationAssessment: $pronunciationAssessment, duration: $duration)';
}


}

/// @nodoc
abstract mixin class $PronunciationAssessmentPhonemeCopyWith<$Res>  {
  factory $PronunciationAssessmentPhonemeCopyWith(PronunciationAssessmentPhoneme value, $Res Function(PronunciationAssessmentPhoneme) _then) = _$PronunciationAssessmentPhonemeCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Phoneme") String phoneme,@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment pronunciationAssessment,@JsonKey(name: "Duration") int? duration
});


$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class _$PronunciationAssessmentPhonemeCopyWithImpl<$Res>
    implements $PronunciationAssessmentPhonemeCopyWith<$Res> {
  _$PronunciationAssessmentPhonemeCopyWithImpl(this._self, this._then);

  final PronunciationAssessmentPhoneme _self;
  final $Res Function(PronunciationAssessmentPhoneme) _then;

/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phoneme = null,Object? offset = freezed,Object? pronunciationAssessment = null,Object? duration = freezed,}) {
  return _then(_self.copyWith(
phoneme: null == phoneme ? _self.phoneme : phoneme // ignore: cast_nullable_to_non_nullable
as String,offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PhonemePronunciationAssessment,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}
/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $PhonemePronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationAssessmentPhoneme].
extension PronunciationAssessmentPhonemePatterns on PronunciationAssessmentPhoneme {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAssessmentPhoneme value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAssessmentPhoneme value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAssessmentPhoneme value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Phoneme")  String phoneme, @JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme() when $default != null:
return $default(_that.phoneme,_that.offset,_that.pronunciationAssessment,_that.duration);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Phoneme")  String phoneme, @JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme():
return $default(_that.phoneme,_that.offset,_that.pronunciationAssessment,_that.duration);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Phoneme")  String phoneme, @JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Duration")  int? duration)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAssessmentPhoneme() when $default != null:
return $default(_that.phoneme,_that.offset,_that.pronunciationAssessment,_that.duration);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationAssessmentPhoneme implements PronunciationAssessmentPhoneme {
  const _PronunciationAssessmentPhoneme({@JsonKey(name: "Phoneme") required this.phoneme, @JsonKey(name: "Offset") this.offset, @JsonKey(name: "PronunciationAssessment") required this.pronunciationAssessment, @JsonKey(name: "Duration") this.duration});
  factory _PronunciationAssessmentPhoneme.fromJson(Map<String, dynamic> json) => _$PronunciationAssessmentPhonemeFromJson(json);

@override@JsonKey(name: "Phoneme") final  String phoneme;
@override@JsonKey(name: "Offset") final  int? offset;
@override@JsonKey(name: "PronunciationAssessment") final  PhonemePronunciationAssessment pronunciationAssessment;
@override@JsonKey(name: "Duration") final  int? duration;

/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAssessmentPhonemeCopyWith<_PronunciationAssessmentPhoneme> get copyWith => __$PronunciationAssessmentPhonemeCopyWithImpl<_PronunciationAssessmentPhoneme>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAssessmentPhonemeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAssessmentPhoneme&&(identical(other.phoneme, phoneme) || other.phoneme == phoneme)&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.duration, duration) || other.duration == duration));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phoneme,offset,pronunciationAssessment,duration);

@override
String toString() {
  return 'PronunciationAssessmentPhoneme(phoneme: $phoneme, offset: $offset, pronunciationAssessment: $pronunciationAssessment, duration: $duration)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAssessmentPhonemeCopyWith<$Res> implements $PronunciationAssessmentPhonemeCopyWith<$Res> {
  factory _$PronunciationAssessmentPhonemeCopyWith(_PronunciationAssessmentPhoneme value, $Res Function(_PronunciationAssessmentPhoneme) _then) = __$PronunciationAssessmentPhonemeCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Phoneme") String phoneme,@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment pronunciationAssessment,@JsonKey(name: "Duration") int? duration
});


@override $PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class __$PronunciationAssessmentPhonemeCopyWithImpl<$Res>
    implements _$PronunciationAssessmentPhonemeCopyWith<$Res> {
  __$PronunciationAssessmentPhonemeCopyWithImpl(this._self, this._then);

  final _PronunciationAssessmentPhoneme _self;
  final $Res Function(_PronunciationAssessmentPhoneme) _then;

/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phoneme = null,Object? offset = freezed,Object? pronunciationAssessment = null,Object? duration = freezed,}) {
  return _then(_PronunciationAssessmentPhoneme(
phoneme: null == phoneme ? _self.phoneme : phoneme // ignore: cast_nullable_to_non_nullable
as String,offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PhonemePronunciationAssessment,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

/// Create a copy of PronunciationAssessmentPhoneme
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $PhonemePronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// @nodoc
mixin _$PhonemePronunciationAssessment {

@JsonKey(name: "AccuracyScore") double get accuracyScore;
/// Create a copy of PhonemePronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PhonemePronunciationAssessmentCopyWith<PhonemePronunciationAssessment> get copyWith => _$PhonemePronunciationAssessmentCopyWithImpl<PhonemePronunciationAssessment>(this as PhonemePronunciationAssessment, _$identity);

  /// Serializes this PhonemePronunciationAssessment to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PhonemePronunciationAssessment&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore);

@override
String toString() {
  return 'PhonemePronunciationAssessment(accuracyScore: $accuracyScore)';
}


}

/// @nodoc
abstract mixin class $PhonemePronunciationAssessmentCopyWith<$Res>  {
  factory $PhonemePronunciationAssessmentCopyWith(PhonemePronunciationAssessment value, $Res Function(PhonemePronunciationAssessment) _then) = _$PhonemePronunciationAssessmentCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "AccuracyScore") double accuracyScore
});




}
/// @nodoc
class _$PhonemePronunciationAssessmentCopyWithImpl<$Res>
    implements $PhonemePronunciationAssessmentCopyWith<$Res> {
  _$PhonemePronunciationAssessmentCopyWithImpl(this._self, this._then);

  final PhonemePronunciationAssessment _self;
  final $Res Function(PhonemePronunciationAssessment) _then;

/// Create a copy of PhonemePronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accuracyScore = null,}) {
  return _then(_self.copyWith(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [PhonemePronunciationAssessment].
extension PhonemePronunciationAssessmentPatterns on PhonemePronunciationAssessment {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PhonemePronunciationAssessment value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PhonemePronunciationAssessment value)  $default,){
final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PhonemePronunciationAssessment value)?  $default,){
final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "AccuracyScore")  double accuracyScore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment() when $default != null:
return $default(_that.accuracyScore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "AccuracyScore")  double accuracyScore)  $default,) {final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment():
return $default(_that.accuracyScore);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "AccuracyScore")  double accuracyScore)?  $default,) {final _that = this;
switch (_that) {
case _PhonemePronunciationAssessment() when $default != null:
return $default(_that.accuracyScore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PhonemePronunciationAssessment implements PhonemePronunciationAssessment {
  const _PhonemePronunciationAssessment({@JsonKey(name: "AccuracyScore") required this.accuracyScore});
  factory _PhonemePronunciationAssessment.fromJson(Map<String, dynamic> json) => _$PhonemePronunciationAssessmentFromJson(json);

@override@JsonKey(name: "AccuracyScore") final  double accuracyScore;

/// Create a copy of PhonemePronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhonemePronunciationAssessmentCopyWith<_PhonemePronunciationAssessment> get copyWith => __$PhonemePronunciationAssessmentCopyWithImpl<_PhonemePronunciationAssessment>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PhonemePronunciationAssessmentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhonemePronunciationAssessment&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore);

@override
String toString() {
  return 'PhonemePronunciationAssessment(accuracyScore: $accuracyScore)';
}


}

/// @nodoc
abstract mixin class _$PhonemePronunciationAssessmentCopyWith<$Res> implements $PhonemePronunciationAssessmentCopyWith<$Res> {
  factory _$PhonemePronunciationAssessmentCopyWith(_PhonemePronunciationAssessment value, $Res Function(_PhonemePronunciationAssessment) _then) = __$PhonemePronunciationAssessmentCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "AccuracyScore") double accuracyScore
});




}
/// @nodoc
class __$PhonemePronunciationAssessmentCopyWithImpl<$Res>
    implements _$PhonemePronunciationAssessmentCopyWith<$Res> {
  __$PhonemePronunciationAssessmentCopyWithImpl(this._self, this._then);

  final _PhonemePronunciationAssessment _self;
  final $Res Function(_PhonemePronunciationAssessment) _then;

/// Create a copy of PhonemePronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accuracyScore = null,}) {
  return _then(_PhonemePronunciationAssessment(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$WordPronunciationAssessment {

@JsonKey(name: "ErrorType") String get errorType;@JsonKey(name: "AccuracyScore") double? get accuracyScore;@JsonKey(name: "Feedback") Feedback? get feedback;
/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WordPronunciationAssessmentCopyWith<WordPronunciationAssessment> get copyWith => _$WordPronunciationAssessmentCopyWithImpl<WordPronunciationAssessment>(this as WordPronunciationAssessment, _$identity);

  /// Serializes this WordPronunciationAssessment to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WordPronunciationAssessment&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.feedback, feedback) || other.feedback == feedback));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,errorType,accuracyScore,feedback);

@override
String toString() {
  return 'WordPronunciationAssessment(errorType: $errorType, accuracyScore: $accuracyScore, feedback: $feedback)';
}


}

/// @nodoc
abstract mixin class $WordPronunciationAssessmentCopyWith<$Res>  {
  factory $WordPronunciationAssessmentCopyWith(WordPronunciationAssessment value, $Res Function(WordPronunciationAssessment) _then) = _$WordPronunciationAssessmentCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "ErrorType") String errorType,@JsonKey(name: "AccuracyScore") double? accuracyScore,@JsonKey(name: "Feedback") Feedback? feedback
});


$FeedbackCopyWith<$Res>? get feedback;

}
/// @nodoc
class _$WordPronunciationAssessmentCopyWithImpl<$Res>
    implements $WordPronunciationAssessmentCopyWith<$Res> {
  _$WordPronunciationAssessmentCopyWithImpl(this._self, this._then);

  final WordPronunciationAssessment _self;
  final $Res Function(WordPronunciationAssessment) _then;

/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? errorType = null,Object? accuracyScore = freezed,Object? feedback = freezed,}) {
  return _then(_self.copyWith(
errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: freezed == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double?,feedback: freezed == feedback ? _self.feedback : feedback // ignore: cast_nullable_to_non_nullable
as Feedback?,
  ));
}
/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeedbackCopyWith<$Res>? get feedback {
    if (_self.feedback == null) {
    return null;
  }

  return $FeedbackCopyWith<$Res>(_self.feedback!, (value) {
    return _then(_self.copyWith(feedback: value));
  });
}
}


/// Adds pattern-matching-related methods to [WordPronunciationAssessment].
extension WordPronunciationAssessmentPatterns on WordPronunciationAssessment {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WordPronunciationAssessment value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WordPronunciationAssessment() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WordPronunciationAssessment value)  $default,){
final _that = this;
switch (_that) {
case _WordPronunciationAssessment():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WordPronunciationAssessment value)?  $default,){
final _that = this;
switch (_that) {
case _WordPronunciationAssessment() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorType")  String errorType, @JsonKey(name: "AccuracyScore")  double? accuracyScore, @JsonKey(name: "Feedback")  Feedback? feedback)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WordPronunciationAssessment() when $default != null:
return $default(_that.errorType,_that.accuracyScore,_that.feedback);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorType")  String errorType, @JsonKey(name: "AccuracyScore")  double? accuracyScore, @JsonKey(name: "Feedback")  Feedback? feedback)  $default,) {final _that = this;
switch (_that) {
case _WordPronunciationAssessment():
return $default(_that.errorType,_that.accuracyScore,_that.feedback);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "ErrorType")  String errorType, @JsonKey(name: "AccuracyScore")  double? accuracyScore, @JsonKey(name: "Feedback")  Feedback? feedback)?  $default,) {final _that = this;
switch (_that) {
case _WordPronunciationAssessment() when $default != null:
return $default(_that.errorType,_that.accuracyScore,_that.feedback);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WordPronunciationAssessment implements WordPronunciationAssessment {
  const _WordPronunciationAssessment({@JsonKey(name: "ErrorType") required this.errorType, @JsonKey(name: "AccuracyScore") this.accuracyScore, @JsonKey(name: "Feedback") this.feedback});
  factory _WordPronunciationAssessment.fromJson(Map<String, dynamic> json) => _$WordPronunciationAssessmentFromJson(json);

@override@JsonKey(name: "ErrorType") final  String errorType;
@override@JsonKey(name: "AccuracyScore") final  double? accuracyScore;
@override@JsonKey(name: "Feedback") final  Feedback? feedback;

/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WordPronunciationAssessmentCopyWith<_WordPronunciationAssessment> get copyWith => __$WordPronunciationAssessmentCopyWithImpl<_WordPronunciationAssessment>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WordPronunciationAssessmentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WordPronunciationAssessment&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.feedback, feedback) || other.feedback == feedback));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,errorType,accuracyScore,feedback);

@override
String toString() {
  return 'WordPronunciationAssessment(errorType: $errorType, accuracyScore: $accuracyScore, feedback: $feedback)';
}


}

/// @nodoc
abstract mixin class _$WordPronunciationAssessmentCopyWith<$Res> implements $WordPronunciationAssessmentCopyWith<$Res> {
  factory _$WordPronunciationAssessmentCopyWith(_WordPronunciationAssessment value, $Res Function(_WordPronunciationAssessment) _then) = __$WordPronunciationAssessmentCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "ErrorType") String errorType,@JsonKey(name: "AccuracyScore") double? accuracyScore,@JsonKey(name: "Feedback") Feedback? feedback
});


@override $FeedbackCopyWith<$Res>? get feedback;

}
/// @nodoc
class __$WordPronunciationAssessmentCopyWithImpl<$Res>
    implements _$WordPronunciationAssessmentCopyWith<$Res> {
  __$WordPronunciationAssessmentCopyWithImpl(this._self, this._then);

  final _WordPronunciationAssessment _self;
  final $Res Function(_WordPronunciationAssessment) _then;

/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? errorType = null,Object? accuracyScore = freezed,Object? feedback = freezed,}) {
  return _then(_WordPronunciationAssessment(
errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: freezed == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double?,feedback: freezed == feedback ? _self.feedback : feedback // ignore: cast_nullable_to_non_nullable
as Feedback?,
  ));
}

/// Create a copy of WordPronunciationAssessment
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeedbackCopyWith<$Res>? get feedback {
    if (_self.feedback == null) {
    return null;
  }

  return $FeedbackCopyWith<$Res>(_self.feedback!, (value) {
    return _then(_self.copyWith(feedback: value));
  });
}
}


/// @nodoc
mixin _$Feedback {

@JsonKey(name: "Prosody") Prosody get prosody;
/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FeedbackCopyWith<Feedback> get copyWith => _$FeedbackCopyWithImpl<Feedback>(this as Feedback, _$identity);

  /// Serializes this Feedback to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Feedback&&(identical(other.prosody, prosody) || other.prosody == prosody));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosody);

@override
String toString() {
  return 'Feedback(prosody: $prosody)';
}


}

/// @nodoc
abstract mixin class $FeedbackCopyWith<$Res>  {
  factory $FeedbackCopyWith(Feedback value, $Res Function(Feedback) _then) = _$FeedbackCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Prosody") Prosody prosody
});


$ProsodyCopyWith<$Res> get prosody;

}
/// @nodoc
class _$FeedbackCopyWithImpl<$Res>
    implements $FeedbackCopyWith<$Res> {
  _$FeedbackCopyWithImpl(this._self, this._then);

  final Feedback _self;
  final $Res Function(Feedback) _then;

/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prosody = null,}) {
  return _then(_self.copyWith(
prosody: null == prosody ? _self.prosody : prosody // ignore: cast_nullable_to_non_nullable
as Prosody,
  ));
}
/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProsodyCopyWith<$Res> get prosody {
  
  return $ProsodyCopyWith<$Res>(_self.prosody, (value) {
    return _then(_self.copyWith(prosody: value));
  });
}
}


/// Adds pattern-matching-related methods to [Feedback].
extension FeedbackPatterns on Feedback {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Feedback value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Feedback() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Feedback value)  $default,){
final _that = this;
switch (_that) {
case _Feedback():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Feedback value)?  $default,){
final _that = this;
switch (_that) {
case _Feedback() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Prosody")  Prosody prosody)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Feedback() when $default != null:
return $default(_that.prosody);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Prosody")  Prosody prosody)  $default,) {final _that = this;
switch (_that) {
case _Feedback():
return $default(_that.prosody);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Prosody")  Prosody prosody)?  $default,) {final _that = this;
switch (_that) {
case _Feedback() when $default != null:
return $default(_that.prosody);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Feedback implements Feedback {
  const _Feedback({@JsonKey(name: "Prosody") required this.prosody});
  factory _Feedback.fromJson(Map<String, dynamic> json) => _$FeedbackFromJson(json);

@override@JsonKey(name: "Prosody") final  Prosody prosody;

/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FeedbackCopyWith<_Feedback> get copyWith => __$FeedbackCopyWithImpl<_Feedback>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FeedbackToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Feedback&&(identical(other.prosody, prosody) || other.prosody == prosody));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosody);

@override
String toString() {
  return 'Feedback(prosody: $prosody)';
}


}

/// @nodoc
abstract mixin class _$FeedbackCopyWith<$Res> implements $FeedbackCopyWith<$Res> {
  factory _$FeedbackCopyWith(_Feedback value, $Res Function(_Feedback) _then) = __$FeedbackCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Prosody") Prosody prosody
});


@override $ProsodyCopyWith<$Res> get prosody;

}
/// @nodoc
class __$FeedbackCopyWithImpl<$Res>
    implements _$FeedbackCopyWith<$Res> {
  __$FeedbackCopyWithImpl(this._self, this._then);

  final _Feedback _self;
  final $Res Function(_Feedback) _then;

/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prosody = null,}) {
  return _then(_Feedback(
prosody: null == prosody ? _self.prosody : prosody // ignore: cast_nullable_to_non_nullable
as Prosody,
  ));
}

/// Create a copy of Feedback
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProsodyCopyWith<$Res> get prosody {
  
  return $ProsodyCopyWith<$Res>(_self.prosody, (value) {
    return _then(_self.copyWith(prosody: value));
  });
}
}


/// @nodoc
mixin _$Prosody {

@JsonKey(name: "Break") Break get prosodyBreak;@JsonKey(name: "Intonation") Intonation get intonation;
/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProsodyCopyWith<Prosody> get copyWith => _$ProsodyCopyWithImpl<Prosody>(this as Prosody, _$identity);

  /// Serializes this Prosody to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Prosody&&(identical(other.prosodyBreak, prosodyBreak) || other.prosodyBreak == prosodyBreak)&&(identical(other.intonation, intonation) || other.intonation == intonation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosodyBreak,intonation);

@override
String toString() {
  return 'Prosody(prosodyBreak: $prosodyBreak, intonation: $intonation)';
}


}

/// @nodoc
abstract mixin class $ProsodyCopyWith<$Res>  {
  factory $ProsodyCopyWith(Prosody value, $Res Function(Prosody) _then) = _$ProsodyCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Break") Break prosodyBreak,@JsonKey(name: "Intonation") Intonation intonation
});


$BreakCopyWith<$Res> get prosodyBreak;$IntonationCopyWith<$Res> get intonation;

}
/// @nodoc
class _$ProsodyCopyWithImpl<$Res>
    implements $ProsodyCopyWith<$Res> {
  _$ProsodyCopyWithImpl(this._self, this._then);

  final Prosody _self;
  final $Res Function(Prosody) _then;

/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prosodyBreak = null,Object? intonation = null,}) {
  return _then(_self.copyWith(
prosodyBreak: null == prosodyBreak ? _self.prosodyBreak : prosodyBreak // ignore: cast_nullable_to_non_nullable
as Break,intonation: null == intonation ? _self.intonation : intonation // ignore: cast_nullable_to_non_nullable
as Intonation,
  ));
}
/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BreakCopyWith<$Res> get prosodyBreak {
  
  return $BreakCopyWith<$Res>(_self.prosodyBreak, (value) {
    return _then(_self.copyWith(prosodyBreak: value));
  });
}/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$IntonationCopyWith<$Res> get intonation {
  
  return $IntonationCopyWith<$Res>(_self.intonation, (value) {
    return _then(_self.copyWith(intonation: value));
  });
}
}


/// Adds pattern-matching-related methods to [Prosody].
extension ProsodyPatterns on Prosody {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Prosody value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Prosody() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Prosody value)  $default,){
final _that = this;
switch (_that) {
case _Prosody():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Prosody value)?  $default,){
final _that = this;
switch (_that) {
case _Prosody() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Break")  Break prosodyBreak, @JsonKey(name: "Intonation")  Intonation intonation)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Prosody() when $default != null:
return $default(_that.prosodyBreak,_that.intonation);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Break")  Break prosodyBreak, @JsonKey(name: "Intonation")  Intonation intonation)  $default,) {final _that = this;
switch (_that) {
case _Prosody():
return $default(_that.prosodyBreak,_that.intonation);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Break")  Break prosodyBreak, @JsonKey(name: "Intonation")  Intonation intonation)?  $default,) {final _that = this;
switch (_that) {
case _Prosody() when $default != null:
return $default(_that.prosodyBreak,_that.intonation);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Prosody implements Prosody {
  const _Prosody({@JsonKey(name: "Break") required this.prosodyBreak, @JsonKey(name: "Intonation") required this.intonation});
  factory _Prosody.fromJson(Map<String, dynamic> json) => _$ProsodyFromJson(json);

@override@JsonKey(name: "Break") final  Break prosodyBreak;
@override@JsonKey(name: "Intonation") final  Intonation intonation;

/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProsodyCopyWith<_Prosody> get copyWith => __$ProsodyCopyWithImpl<_Prosody>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProsodyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Prosody&&(identical(other.prosodyBreak, prosodyBreak) || other.prosodyBreak == prosodyBreak)&&(identical(other.intonation, intonation) || other.intonation == intonation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosodyBreak,intonation);

@override
String toString() {
  return 'Prosody(prosodyBreak: $prosodyBreak, intonation: $intonation)';
}


}

/// @nodoc
abstract mixin class _$ProsodyCopyWith<$Res> implements $ProsodyCopyWith<$Res> {
  factory _$ProsodyCopyWith(_Prosody value, $Res Function(_Prosody) _then) = __$ProsodyCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Break") Break prosodyBreak,@JsonKey(name: "Intonation") Intonation intonation
});


@override $BreakCopyWith<$Res> get prosodyBreak;@override $IntonationCopyWith<$Res> get intonation;

}
/// @nodoc
class __$ProsodyCopyWithImpl<$Res>
    implements _$ProsodyCopyWith<$Res> {
  __$ProsodyCopyWithImpl(this._self, this._then);

  final _Prosody _self;
  final $Res Function(_Prosody) _then;

/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prosodyBreak = null,Object? intonation = null,}) {
  return _then(_Prosody(
prosodyBreak: null == prosodyBreak ? _self.prosodyBreak : prosodyBreak // ignore: cast_nullable_to_non_nullable
as Break,intonation: null == intonation ? _self.intonation : intonation // ignore: cast_nullable_to_non_nullable
as Intonation,
  ));
}

/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BreakCopyWith<$Res> get prosodyBreak {
  
  return $BreakCopyWith<$Res>(_self.prosodyBreak, (value) {
    return _then(_self.copyWith(prosodyBreak: value));
  });
}/// Create a copy of Prosody
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$IntonationCopyWith<$Res> get intonation {
  
  return $IntonationCopyWith<$Res>(_self.intonation, (value) {
    return _then(_self.copyWith(intonation: value));
  });
}
}


/// @nodoc
mixin _$Intonation {

@JsonKey(name: "ErrorTypes") List<String> get errorTypes;@JsonKey(name: "Monotone") Monotone get monotone;
/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IntonationCopyWith<Intonation> get copyWith => _$IntonationCopyWithImpl<Intonation>(this as Intonation, _$identity);

  /// Serializes this Intonation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Intonation&&const DeepCollectionEquality().equals(other.errorTypes, errorTypes)&&(identical(other.monotone, monotone) || other.monotone == monotone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(errorTypes),monotone);

@override
String toString() {
  return 'Intonation(errorTypes: $errorTypes, monotone: $monotone)';
}


}

/// @nodoc
abstract mixin class $IntonationCopyWith<$Res>  {
  factory $IntonationCopyWith(Intonation value, $Res Function(Intonation) _then) = _$IntonationCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "ErrorTypes") List<String> errorTypes,@JsonKey(name: "Monotone") Monotone monotone
});


$MonotoneCopyWith<$Res> get monotone;

}
/// @nodoc
class _$IntonationCopyWithImpl<$Res>
    implements $IntonationCopyWith<$Res> {
  _$IntonationCopyWithImpl(this._self, this._then);

  final Intonation _self;
  final $Res Function(Intonation) _then;

/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? errorTypes = null,Object? monotone = null,}) {
  return _then(_self.copyWith(
errorTypes: null == errorTypes ? _self.errorTypes : errorTypes // ignore: cast_nullable_to_non_nullable
as List<String>,monotone: null == monotone ? _self.monotone : monotone // ignore: cast_nullable_to_non_nullable
as Monotone,
  ));
}
/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MonotoneCopyWith<$Res> get monotone {
  
  return $MonotoneCopyWith<$Res>(_self.monotone, (value) {
    return _then(_self.copyWith(monotone: value));
  });
}
}


/// Adds pattern-matching-related methods to [Intonation].
extension IntonationPatterns on Intonation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Intonation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Intonation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Intonation value)  $default,){
final _that = this;
switch (_that) {
case _Intonation():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Intonation value)?  $default,){
final _that = this;
switch (_that) {
case _Intonation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "Monotone")  Monotone monotone)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Intonation() when $default != null:
return $default(_that.errorTypes,_that.monotone);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "Monotone")  Monotone monotone)  $default,) {final _that = this;
switch (_that) {
case _Intonation():
return $default(_that.errorTypes,_that.monotone);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "Monotone")  Monotone monotone)?  $default,) {final _that = this;
switch (_that) {
case _Intonation() when $default != null:
return $default(_that.errorTypes,_that.monotone);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Intonation implements Intonation {
  const _Intonation({@JsonKey(name: "ErrorTypes") required final  List<String> errorTypes, @JsonKey(name: "Monotone") required this.monotone}): _errorTypes = errorTypes;
  factory _Intonation.fromJson(Map<String, dynamic> json) => _$IntonationFromJson(json);

 final  List<String> _errorTypes;
@override@JsonKey(name: "ErrorTypes") List<String> get errorTypes {
  if (_errorTypes is EqualUnmodifiableListView) return _errorTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_errorTypes);
}

@override@JsonKey(name: "Monotone") final  Monotone monotone;

/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IntonationCopyWith<_Intonation> get copyWith => __$IntonationCopyWithImpl<_Intonation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IntonationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Intonation&&const DeepCollectionEquality().equals(other._errorTypes, _errorTypes)&&(identical(other.monotone, monotone) || other.monotone == monotone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_errorTypes),monotone);

@override
String toString() {
  return 'Intonation(errorTypes: $errorTypes, monotone: $monotone)';
}


}

/// @nodoc
abstract mixin class _$IntonationCopyWith<$Res> implements $IntonationCopyWith<$Res> {
  factory _$IntonationCopyWith(_Intonation value, $Res Function(_Intonation) _then) = __$IntonationCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "ErrorTypes") List<String> errorTypes,@JsonKey(name: "Monotone") Monotone monotone
});


@override $MonotoneCopyWith<$Res> get monotone;

}
/// @nodoc
class __$IntonationCopyWithImpl<$Res>
    implements _$IntonationCopyWith<$Res> {
  __$IntonationCopyWithImpl(this._self, this._then);

  final _Intonation _self;
  final $Res Function(_Intonation) _then;

/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? errorTypes = null,Object? monotone = null,}) {
  return _then(_Intonation(
errorTypes: null == errorTypes ? _self._errorTypes : errorTypes // ignore: cast_nullable_to_non_nullable
as List<String>,monotone: null == monotone ? _self.monotone : monotone // ignore: cast_nullable_to_non_nullable
as Monotone,
  ));
}

/// Create a copy of Intonation
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MonotoneCopyWith<$Res> get monotone {
  
  return $MonotoneCopyWith<$Res>(_self.monotone, (value) {
    return _then(_self.copyWith(monotone: value));
  });
}
}


/// @nodoc
mixin _$Monotone {

@JsonKey(name: "SyllablePitchDeltaConfidence") double get syllablePitchDeltaConfidence;
/// Create a copy of Monotone
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MonotoneCopyWith<Monotone> get copyWith => _$MonotoneCopyWithImpl<Monotone>(this as Monotone, _$identity);

  /// Serializes this Monotone to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Monotone&&(identical(other.syllablePitchDeltaConfidence, syllablePitchDeltaConfidence) || other.syllablePitchDeltaConfidence == syllablePitchDeltaConfidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,syllablePitchDeltaConfidence);

@override
String toString() {
  return 'Monotone(syllablePitchDeltaConfidence: $syllablePitchDeltaConfidence)';
}


}

/// @nodoc
abstract mixin class $MonotoneCopyWith<$Res>  {
  factory $MonotoneCopyWith(Monotone value, $Res Function(Monotone) _then) = _$MonotoneCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "SyllablePitchDeltaConfidence") double syllablePitchDeltaConfidence
});




}
/// @nodoc
class _$MonotoneCopyWithImpl<$Res>
    implements $MonotoneCopyWith<$Res> {
  _$MonotoneCopyWithImpl(this._self, this._then);

  final Monotone _self;
  final $Res Function(Monotone) _then;

/// Create a copy of Monotone
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? syllablePitchDeltaConfidence = null,}) {
  return _then(_self.copyWith(
syllablePitchDeltaConfidence: null == syllablePitchDeltaConfidence ? _self.syllablePitchDeltaConfidence : syllablePitchDeltaConfidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [Monotone].
extension MonotonePatterns on Monotone {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Monotone value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Monotone() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Monotone value)  $default,){
final _that = this;
switch (_that) {
case _Monotone():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Monotone value)?  $default,){
final _that = this;
switch (_that) {
case _Monotone() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "SyllablePitchDeltaConfidence")  double syllablePitchDeltaConfidence)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Monotone() when $default != null:
return $default(_that.syllablePitchDeltaConfidence);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "SyllablePitchDeltaConfidence")  double syllablePitchDeltaConfidence)  $default,) {final _that = this;
switch (_that) {
case _Monotone():
return $default(_that.syllablePitchDeltaConfidence);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "SyllablePitchDeltaConfidence")  double syllablePitchDeltaConfidence)?  $default,) {final _that = this;
switch (_that) {
case _Monotone() when $default != null:
return $default(_that.syllablePitchDeltaConfidence);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Monotone implements Monotone {
  const _Monotone({@JsonKey(name: "SyllablePitchDeltaConfidence") required this.syllablePitchDeltaConfidence});
  factory _Monotone.fromJson(Map<String, dynamic> json) => _$MonotoneFromJson(json);

@override@JsonKey(name: "SyllablePitchDeltaConfidence") final  double syllablePitchDeltaConfidence;

/// Create a copy of Monotone
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MonotoneCopyWith<_Monotone> get copyWith => __$MonotoneCopyWithImpl<_Monotone>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MonotoneToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Monotone&&(identical(other.syllablePitchDeltaConfidence, syllablePitchDeltaConfidence) || other.syllablePitchDeltaConfidence == syllablePitchDeltaConfidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,syllablePitchDeltaConfidence);

@override
String toString() {
  return 'Monotone(syllablePitchDeltaConfidence: $syllablePitchDeltaConfidence)';
}


}

/// @nodoc
abstract mixin class _$MonotoneCopyWith<$Res> implements $MonotoneCopyWith<$Res> {
  factory _$MonotoneCopyWith(_Monotone value, $Res Function(_Monotone) _then) = __$MonotoneCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "SyllablePitchDeltaConfidence") double syllablePitchDeltaConfidence
});




}
/// @nodoc
class __$MonotoneCopyWithImpl<$Res>
    implements _$MonotoneCopyWith<$Res> {
  __$MonotoneCopyWithImpl(this._self, this._then);

  final _Monotone _self;
  final $Res Function(_Monotone) _then;

/// Create a copy of Monotone
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? syllablePitchDeltaConfidence = null,}) {
  return _then(_Monotone(
syllablePitchDeltaConfidence: null == syllablePitchDeltaConfidence ? _self.syllablePitchDeltaConfidence : syllablePitchDeltaConfidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$Break {

@JsonKey(name: "ErrorTypes") List<String> get errorTypes;@JsonKey(name: "UnexpectedBreak") UnexpectedBreak? get unexpectedBreak;@JsonKey(name: "MissingBreak") MissingBreak? get missingBreak;@JsonKey(name: "BreakLength") int get breakLength;
/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BreakCopyWith<Break> get copyWith => _$BreakCopyWithImpl<Break>(this as Break, _$identity);

  /// Serializes this Break to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Break&&const DeepCollectionEquality().equals(other.errorTypes, errorTypes)&&(identical(other.unexpectedBreak, unexpectedBreak) || other.unexpectedBreak == unexpectedBreak)&&(identical(other.missingBreak, missingBreak) || other.missingBreak == missingBreak)&&(identical(other.breakLength, breakLength) || other.breakLength == breakLength));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(errorTypes),unexpectedBreak,missingBreak,breakLength);

@override
String toString() {
  return 'Break(errorTypes: $errorTypes, unexpectedBreak: $unexpectedBreak, missingBreak: $missingBreak, breakLength: $breakLength)';
}


}

/// @nodoc
abstract mixin class $BreakCopyWith<$Res>  {
  factory $BreakCopyWith(Break value, $Res Function(Break) _then) = _$BreakCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "ErrorTypes") List<String> errorTypes,@JsonKey(name: "UnexpectedBreak") UnexpectedBreak? unexpectedBreak,@JsonKey(name: "MissingBreak") MissingBreak? missingBreak,@JsonKey(name: "BreakLength") int breakLength
});


$UnexpectedBreakCopyWith<$Res>? get unexpectedBreak;$MissingBreakCopyWith<$Res>? get missingBreak;

}
/// @nodoc
class _$BreakCopyWithImpl<$Res>
    implements $BreakCopyWith<$Res> {
  _$BreakCopyWithImpl(this._self, this._then);

  final Break _self;
  final $Res Function(Break) _then;

/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? errorTypes = null,Object? unexpectedBreak = freezed,Object? missingBreak = freezed,Object? breakLength = null,}) {
  return _then(_self.copyWith(
errorTypes: null == errorTypes ? _self.errorTypes : errorTypes // ignore: cast_nullable_to_non_nullable
as List<String>,unexpectedBreak: freezed == unexpectedBreak ? _self.unexpectedBreak : unexpectedBreak // ignore: cast_nullable_to_non_nullable
as UnexpectedBreak?,missingBreak: freezed == missingBreak ? _self.missingBreak : missingBreak // ignore: cast_nullable_to_non_nullable
as MissingBreak?,breakLength: null == breakLength ? _self.breakLength : breakLength // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UnexpectedBreakCopyWith<$Res>? get unexpectedBreak {
    if (_self.unexpectedBreak == null) {
    return null;
  }

  return $UnexpectedBreakCopyWith<$Res>(_self.unexpectedBreak!, (value) {
    return _then(_self.copyWith(unexpectedBreak: value));
  });
}/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MissingBreakCopyWith<$Res>? get missingBreak {
    if (_self.missingBreak == null) {
    return null;
  }

  return $MissingBreakCopyWith<$Res>(_self.missingBreak!, (value) {
    return _then(_self.copyWith(missingBreak: value));
  });
}
}


/// Adds pattern-matching-related methods to [Break].
extension BreakPatterns on Break {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Break value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Break() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Break value)  $default,){
final _that = this;
switch (_that) {
case _Break():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Break value)?  $default,){
final _that = this;
switch (_that) {
case _Break() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "UnexpectedBreak")  UnexpectedBreak? unexpectedBreak, @JsonKey(name: "MissingBreak")  MissingBreak? missingBreak, @JsonKey(name: "BreakLength")  int breakLength)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Break() when $default != null:
return $default(_that.errorTypes,_that.unexpectedBreak,_that.missingBreak,_that.breakLength);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "UnexpectedBreak")  UnexpectedBreak? unexpectedBreak, @JsonKey(name: "MissingBreak")  MissingBreak? missingBreak, @JsonKey(name: "BreakLength")  int breakLength)  $default,) {final _that = this;
switch (_that) {
case _Break():
return $default(_that.errorTypes,_that.unexpectedBreak,_that.missingBreak,_that.breakLength);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "ErrorTypes")  List<String> errorTypes, @JsonKey(name: "UnexpectedBreak")  UnexpectedBreak? unexpectedBreak, @JsonKey(name: "MissingBreak")  MissingBreak? missingBreak, @JsonKey(name: "BreakLength")  int breakLength)?  $default,) {final _that = this;
switch (_that) {
case _Break() when $default != null:
return $default(_that.errorTypes,_that.unexpectedBreak,_that.missingBreak,_that.breakLength);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Break implements Break {
  const _Break({@JsonKey(name: "ErrorTypes") required final  List<String> errorTypes, @JsonKey(name: "UnexpectedBreak") this.unexpectedBreak, @JsonKey(name: "MissingBreak") this.missingBreak, @JsonKey(name: "BreakLength") required this.breakLength}): _errorTypes = errorTypes;
  factory _Break.fromJson(Map<String, dynamic> json) => _$BreakFromJson(json);

 final  List<String> _errorTypes;
@override@JsonKey(name: "ErrorTypes") List<String> get errorTypes {
  if (_errorTypes is EqualUnmodifiableListView) return _errorTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_errorTypes);
}

@override@JsonKey(name: "UnexpectedBreak") final  UnexpectedBreak? unexpectedBreak;
@override@JsonKey(name: "MissingBreak") final  MissingBreak? missingBreak;
@override@JsonKey(name: "BreakLength") final  int breakLength;

/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BreakCopyWith<_Break> get copyWith => __$BreakCopyWithImpl<_Break>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BreakToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Break&&const DeepCollectionEquality().equals(other._errorTypes, _errorTypes)&&(identical(other.unexpectedBreak, unexpectedBreak) || other.unexpectedBreak == unexpectedBreak)&&(identical(other.missingBreak, missingBreak) || other.missingBreak == missingBreak)&&(identical(other.breakLength, breakLength) || other.breakLength == breakLength));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_errorTypes),unexpectedBreak,missingBreak,breakLength);

@override
String toString() {
  return 'Break(errorTypes: $errorTypes, unexpectedBreak: $unexpectedBreak, missingBreak: $missingBreak, breakLength: $breakLength)';
}


}

/// @nodoc
abstract mixin class _$BreakCopyWith<$Res> implements $BreakCopyWith<$Res> {
  factory _$BreakCopyWith(_Break value, $Res Function(_Break) _then) = __$BreakCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "ErrorTypes") List<String> errorTypes,@JsonKey(name: "UnexpectedBreak") UnexpectedBreak? unexpectedBreak,@JsonKey(name: "MissingBreak") MissingBreak? missingBreak,@JsonKey(name: "BreakLength") int breakLength
});


@override $UnexpectedBreakCopyWith<$Res>? get unexpectedBreak;@override $MissingBreakCopyWith<$Res>? get missingBreak;

}
/// @nodoc
class __$BreakCopyWithImpl<$Res>
    implements _$BreakCopyWith<$Res> {
  __$BreakCopyWithImpl(this._self, this._then);

  final _Break _self;
  final $Res Function(_Break) _then;

/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? errorTypes = null,Object? unexpectedBreak = freezed,Object? missingBreak = freezed,Object? breakLength = null,}) {
  return _then(_Break(
errorTypes: null == errorTypes ? _self._errorTypes : errorTypes // ignore: cast_nullable_to_non_nullable
as List<String>,unexpectedBreak: freezed == unexpectedBreak ? _self.unexpectedBreak : unexpectedBreak // ignore: cast_nullable_to_non_nullable
as UnexpectedBreak?,missingBreak: freezed == missingBreak ? _self.missingBreak : missingBreak // ignore: cast_nullable_to_non_nullable
as MissingBreak?,breakLength: null == breakLength ? _self.breakLength : breakLength // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UnexpectedBreakCopyWith<$Res>? get unexpectedBreak {
    if (_self.unexpectedBreak == null) {
    return null;
  }

  return $UnexpectedBreakCopyWith<$Res>(_self.unexpectedBreak!, (value) {
    return _then(_self.copyWith(unexpectedBreak: value));
  });
}/// Create a copy of Break
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MissingBreakCopyWith<$Res>? get missingBreak {
    if (_self.missingBreak == null) {
    return null;
  }

  return $MissingBreakCopyWith<$Res>(_self.missingBreak!, (value) {
    return _then(_self.copyWith(missingBreak: value));
  });
}
}


/// @nodoc
mixin _$UnexpectedBreak {

@JsonKey(name: "Confidence") double get confidence;
/// Create a copy of UnexpectedBreak
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UnexpectedBreakCopyWith<UnexpectedBreak> get copyWith => _$UnexpectedBreakCopyWithImpl<UnexpectedBreak>(this as UnexpectedBreak, _$identity);

  /// Serializes this UnexpectedBreak to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UnexpectedBreak&&(identical(other.confidence, confidence) || other.confidence == confidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence);

@override
String toString() {
  return 'UnexpectedBreak(confidence: $confidence)';
}


}

/// @nodoc
abstract mixin class $UnexpectedBreakCopyWith<$Res>  {
  factory $UnexpectedBreakCopyWith(UnexpectedBreak value, $Res Function(UnexpectedBreak) _then) = _$UnexpectedBreakCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Confidence") double confidence
});




}
/// @nodoc
class _$UnexpectedBreakCopyWithImpl<$Res>
    implements $UnexpectedBreakCopyWith<$Res> {
  _$UnexpectedBreakCopyWithImpl(this._self, this._then);

  final UnexpectedBreak _self;
  final $Res Function(UnexpectedBreak) _then;

/// Create a copy of UnexpectedBreak
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? confidence = null,}) {
  return _then(_self.copyWith(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [UnexpectedBreak].
extension UnexpectedBreakPatterns on UnexpectedBreak {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UnexpectedBreak value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UnexpectedBreak() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UnexpectedBreak value)  $default,){
final _that = this;
switch (_that) {
case _UnexpectedBreak():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UnexpectedBreak value)?  $default,){
final _that = this;
switch (_that) {
case _UnexpectedBreak() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Confidence")  double confidence)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UnexpectedBreak() when $default != null:
return $default(_that.confidence);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Confidence")  double confidence)  $default,) {final _that = this;
switch (_that) {
case _UnexpectedBreak():
return $default(_that.confidence);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Confidence")  double confidence)?  $default,) {final _that = this;
switch (_that) {
case _UnexpectedBreak() when $default != null:
return $default(_that.confidence);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UnexpectedBreak implements UnexpectedBreak {
  const _UnexpectedBreak({@JsonKey(name: "Confidence") required this.confidence});
  factory _UnexpectedBreak.fromJson(Map<String, dynamic> json) => _$UnexpectedBreakFromJson(json);

@override@JsonKey(name: "Confidence") final  double confidence;

/// Create a copy of UnexpectedBreak
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UnexpectedBreakCopyWith<_UnexpectedBreak> get copyWith => __$UnexpectedBreakCopyWithImpl<_UnexpectedBreak>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UnexpectedBreakToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UnexpectedBreak&&(identical(other.confidence, confidence) || other.confidence == confidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence);

@override
String toString() {
  return 'UnexpectedBreak(confidence: $confidence)';
}


}

/// @nodoc
abstract mixin class _$UnexpectedBreakCopyWith<$Res> implements $UnexpectedBreakCopyWith<$Res> {
  factory _$UnexpectedBreakCopyWith(_UnexpectedBreak value, $Res Function(_UnexpectedBreak) _then) = __$UnexpectedBreakCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Confidence") double confidence
});




}
/// @nodoc
class __$UnexpectedBreakCopyWithImpl<$Res>
    implements _$UnexpectedBreakCopyWith<$Res> {
  __$UnexpectedBreakCopyWithImpl(this._self, this._then);

  final _UnexpectedBreak _self;
  final $Res Function(_UnexpectedBreak) _then;

/// Create a copy of UnexpectedBreak
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? confidence = null,}) {
  return _then(_UnexpectedBreak(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$MissingBreak {

@JsonKey(name: "Confidence") double get confidence;
/// Create a copy of MissingBreak
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MissingBreakCopyWith<MissingBreak> get copyWith => _$MissingBreakCopyWithImpl<MissingBreak>(this as MissingBreak, _$identity);

  /// Serializes this MissingBreak to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MissingBreak&&(identical(other.confidence, confidence) || other.confidence == confidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence);

@override
String toString() {
  return 'MissingBreak(confidence: $confidence)';
}


}

/// @nodoc
abstract mixin class $MissingBreakCopyWith<$Res>  {
  factory $MissingBreakCopyWith(MissingBreak value, $Res Function(MissingBreak) _then) = _$MissingBreakCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Confidence") double confidence
});




}
/// @nodoc
class _$MissingBreakCopyWithImpl<$Res>
    implements $MissingBreakCopyWith<$Res> {
  _$MissingBreakCopyWithImpl(this._self, this._then);

  final MissingBreak _self;
  final $Res Function(MissingBreak) _then;

/// Create a copy of MissingBreak
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? confidence = null,}) {
  return _then(_self.copyWith(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [MissingBreak].
extension MissingBreakPatterns on MissingBreak {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MissingBreak value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MissingBreak() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MissingBreak value)  $default,){
final _that = this;
switch (_that) {
case _MissingBreak():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MissingBreak value)?  $default,){
final _that = this;
switch (_that) {
case _MissingBreak() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Confidence")  double confidence)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MissingBreak() when $default != null:
return $default(_that.confidence);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Confidence")  double confidence)  $default,) {final _that = this;
switch (_that) {
case _MissingBreak():
return $default(_that.confidence);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Confidence")  double confidence)?  $default,) {final _that = this;
switch (_that) {
case _MissingBreak() when $default != null:
return $default(_that.confidence);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MissingBreak implements MissingBreak {
  const _MissingBreak({@JsonKey(name: "Confidence") required this.confidence});
  factory _MissingBreak.fromJson(Map<String, dynamic> json) => _$MissingBreakFromJson(json);

@override@JsonKey(name: "Confidence") final  double confidence;

/// Create a copy of MissingBreak
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MissingBreakCopyWith<_MissingBreak> get copyWith => __$MissingBreakCopyWithImpl<_MissingBreak>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MissingBreakToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MissingBreak&&(identical(other.confidence, confidence) || other.confidence == confidence));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,confidence);

@override
String toString() {
  return 'MissingBreak(confidence: $confidence)';
}


}

/// @nodoc
abstract mixin class _$MissingBreakCopyWith<$Res> implements $MissingBreakCopyWith<$Res> {
  factory _$MissingBreakCopyWith(_MissingBreak value, $Res Function(_MissingBreak) _then) = __$MissingBreakCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Confidence") double confidence
});




}
/// @nodoc
class __$MissingBreakCopyWithImpl<$Res>
    implements _$MissingBreakCopyWith<$Res> {
  __$MissingBreakCopyWithImpl(this._self, this._then);

  final _MissingBreak _self;
  final $Res Function(_MissingBreak) _then;

/// Create a copy of MissingBreak
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? confidence = null,}) {
  return _then(_MissingBreak(
confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$Syllable {

@JsonKey(name: "Offset") int? get offset;@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment get pronunciationAssessment;@JsonKey(name: "Grapheme") String? get grapheme;@JsonKey(name: "Syllable") String? get syllable;@JsonKey(name: "Duration") int? get duration;
/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SyllableCopyWith<Syllable> get copyWith => _$SyllableCopyWithImpl<Syllable>(this as Syllable, _$identity);

  /// Serializes this Syllable to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Syllable&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.grapheme, grapheme) || other.grapheme == grapheme)&&(identical(other.syllable, syllable) || other.syllable == syllable)&&(identical(other.duration, duration) || other.duration == duration));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,offset,pronunciationAssessment,grapheme,syllable,duration);

@override
String toString() {
  return 'Syllable(offset: $offset, pronunciationAssessment: $pronunciationAssessment, grapheme: $grapheme, syllable: $syllable, duration: $duration)';
}


}

/// @nodoc
abstract mixin class $SyllableCopyWith<$Res>  {
  factory $SyllableCopyWith(Syllable value, $Res Function(Syllable) _then) = _$SyllableCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment pronunciationAssessment,@JsonKey(name: "Grapheme") String? grapheme,@JsonKey(name: "Syllable") String? syllable,@JsonKey(name: "Duration") int? duration
});


$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class _$SyllableCopyWithImpl<$Res>
    implements $SyllableCopyWith<$Res> {
  _$SyllableCopyWithImpl(this._self, this._then);

  final Syllable _self;
  final $Res Function(Syllable) _then;

/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? offset = freezed,Object? pronunciationAssessment = null,Object? grapheme = freezed,Object? syllable = freezed,Object? duration = freezed,}) {
  return _then(_self.copyWith(
offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PhonemePronunciationAssessment,grapheme: freezed == grapheme ? _self.grapheme : grapheme // ignore: cast_nullable_to_non_nullable
as String?,syllable: freezed == syllable ? _self.syllable : syllable // ignore: cast_nullable_to_non_nullable
as String?,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}
/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $PhonemePronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}


/// Adds pattern-matching-related methods to [Syllable].
extension SyllablePatterns on Syllable {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Syllable value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Syllable() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Syllable value)  $default,){
final _that = this;
switch (_that) {
case _Syllable():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Syllable value)?  $default,){
final _that = this;
switch (_that) {
case _Syllable() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Grapheme")  String? grapheme, @JsonKey(name: "Syllable")  String? syllable, @JsonKey(name: "Duration")  int? duration)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Syllable() when $default != null:
return $default(_that.offset,_that.pronunciationAssessment,_that.grapheme,_that.syllable,_that.duration);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Grapheme")  String? grapheme, @JsonKey(name: "Syllable")  String? syllable, @JsonKey(name: "Duration")  int? duration)  $default,) {final _that = this;
switch (_that) {
case _Syllable():
return $default(_that.offset,_that.pronunciationAssessment,_that.grapheme,_that.syllable,_that.duration);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "Offset")  int? offset, @JsonKey(name: "PronunciationAssessment")  PhonemePronunciationAssessment pronunciationAssessment, @JsonKey(name: "Grapheme")  String? grapheme, @JsonKey(name: "Syllable")  String? syllable, @JsonKey(name: "Duration")  int? duration)?  $default,) {final _that = this;
switch (_that) {
case _Syllable() when $default != null:
return $default(_that.offset,_that.pronunciationAssessment,_that.grapheme,_that.syllable,_that.duration);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Syllable implements Syllable {
  const _Syllable({@JsonKey(name: "Offset") this.offset, @JsonKey(name: "PronunciationAssessment") required this.pronunciationAssessment, @JsonKey(name: "Grapheme") this.grapheme, @JsonKey(name: "Syllable") this.syllable, @JsonKey(name: "Duration") this.duration});
  factory _Syllable.fromJson(Map<String, dynamic> json) => _$SyllableFromJson(json);

@override@JsonKey(name: "Offset") final  int? offset;
@override@JsonKey(name: "PronunciationAssessment") final  PhonemePronunciationAssessment pronunciationAssessment;
@override@JsonKey(name: "Grapheme") final  String? grapheme;
@override@JsonKey(name: "Syllable") final  String? syllable;
@override@JsonKey(name: "Duration") final  int? duration;

/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SyllableCopyWith<_Syllable> get copyWith => __$SyllableCopyWithImpl<_Syllable>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SyllableToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Syllable&&(identical(other.offset, offset) || other.offset == offset)&&(identical(other.pronunciationAssessment, pronunciationAssessment) || other.pronunciationAssessment == pronunciationAssessment)&&(identical(other.grapheme, grapheme) || other.grapheme == grapheme)&&(identical(other.syllable, syllable) || other.syllable == syllable)&&(identical(other.duration, duration) || other.duration == duration));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,offset,pronunciationAssessment,grapheme,syllable,duration);

@override
String toString() {
  return 'Syllable(offset: $offset, pronunciationAssessment: $pronunciationAssessment, grapheme: $grapheme, syllable: $syllable, duration: $duration)';
}


}

/// @nodoc
abstract mixin class _$SyllableCopyWith<$Res> implements $SyllableCopyWith<$Res> {
  factory _$SyllableCopyWith(_Syllable value, $Res Function(_Syllable) _then) = __$SyllableCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "Offset") int? offset,@JsonKey(name: "PronunciationAssessment") PhonemePronunciationAssessment pronunciationAssessment,@JsonKey(name: "Grapheme") String? grapheme,@JsonKey(name: "Syllable") String? syllable,@JsonKey(name: "Duration") int? duration
});


@override $PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment;

}
/// @nodoc
class __$SyllableCopyWithImpl<$Res>
    implements _$SyllableCopyWith<$Res> {
  __$SyllableCopyWithImpl(this._self, this._then);

  final _Syllable _self;
  final $Res Function(_Syllable) _then;

/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? offset = freezed,Object? pronunciationAssessment = null,Object? grapheme = freezed,Object? syllable = freezed,Object? duration = freezed,}) {
  return _then(_Syllable(
offset: freezed == offset ? _self.offset : offset // ignore: cast_nullable_to_non_nullable
as int?,pronunciationAssessment: null == pronunciationAssessment ? _self.pronunciationAssessment : pronunciationAssessment // ignore: cast_nullable_to_non_nullable
as PhonemePronunciationAssessment,grapheme: freezed == grapheme ? _self.grapheme : grapheme // ignore: cast_nullable_to_non_nullable
as String?,syllable: freezed == syllable ? _self.syllable : syllable // ignore: cast_nullable_to_non_nullable
as String?,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

/// Create a copy of Syllable
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PhonemePronunciationAssessmentCopyWith<$Res> get pronunciationAssessment {
  
  return $PhonemePronunciationAssessmentCopyWith<$Res>(_self.pronunciationAssessment, (value) {
    return _then(_self.copyWith(pronunciationAssessment: value));
  });
}
}

// dart format on
