import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';

void main() {
  group('QuestionModel Tests', () {
    group('Constructor and Properties', () {
      test('should create QuestionModel with default values', () {
        final question = QuestionModel();

        expect(question.questionId, isNull);
        expect(question.order, equals(0));
        expect(question.question, equals(''));
        expect(question.image, equals(''));
        expect(question.isActive, isTrue);
        expect(question.choices, equals([]));
        expect(question.state, equals(QuestionConcreteState.initial));
        expect(question.isCorrect, isFalse);
        expect(question.answer, isNull);
      });

      test('should create QuestionModel with custom values', () {
        final choices = [
          Choice(text: 'Choice 1', value: '1', isCorrect: true),
          Choice(text: 'Choice 2', value: '2', isCorrect: false),
        ];

        final question = QuestionModel(
          questionId: 'q1',
          order: 1,
          question: 'What is 2+2?',
          image: 'image_url',
          isActive: false,
          choices: choices,
          state: QuestionConcreteState.answered,
          isCorrect: true,
          answer: choices[0],
        );

        expect(question.questionId, equals('q1'));
        expect(question.order, equals(1));
        expect(question.question, equals('What is 2+2?'));
        expect(question.image, equals('image_url'));
        expect(question.isActive, isFalse);
        expect(question.choices, equals(choices));
        expect(question.state, equals(QuestionConcreteState.answered));
        expect(question.isCorrect, isTrue);
        expect(question.answer, equals(choices[0]));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final choices = [
          Choice(text: 'Choice 1', value: '1', isCorrect: true),
          Choice(text: 'Choice 2', value: '2', isCorrect: false),
        ];

        final question = QuestionModel(
          questionId: 'q1',
          order: 1,
          question: 'What is 2+2?',
          image: 'image_url',
          isActive: false,
          choices: choices,
        );

        final json = question.toJson();

        expect(json['question_id'], equals('q1'));
        expect(json['order'], equals(1));
        expect(json['question'], equals('What is 2+2?'));
        expect(json['image_url'], equals('image_url'));
        expect(json['is_active'], isFalse);
        expect(json['choices'], isNotNull);
        expect((json['choices'] as List).length, equals(2));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'question_id': 'q1',
          'order': 1,
          'question': 'What is 2+2?',
          'image_url': 'image_url',
          'is_active': false,
          'choices': [
            {'text': 'Choice 1', 'value': '1', 'is_correct': true},
            {'text': 'Choice 2', 'value': '2', 'is_correct': false},
          ],
        };

        final question = QuestionModel.fromJson(json);

        expect(question.questionId, equals('q1'));
        expect(question.order, equals(1));
        expect(question.question, equals('What is 2+2?'));
        expect(question.image, equals('image_url'));
        expect(question.isActive, isFalse);
        expect(question.choices.length, equals(2));
        expect(question.choices[0].text, equals('Choice 1'));
        expect(question.choices[0].value, equals('1'));
        expect(question.choices[0].isCorrect, isTrue);
        expect(question.choices[1].text, equals('Choice 2'));
        expect(question.choices[1].value, equals('2'));
        expect(question.choices[1].isCorrect, isFalse);
      });

      test('should handle missing optional fields in JSON', () {
        final json = {'question': 'What is 2+2?'};

        final question = QuestionModel.fromJson(json);

        expect(question.questionId, isNull);
        expect(question.order, equals(0)); // Default value
        expect(question.question, equals('What is 2+2?'));
        expect(question.image, equals('')); // Default value
        expect(question.isActive, isTrue); // Default value
        expect(question.choices, equals([])); // Default value
        expect(
          question.state,
          equals(QuestionConcreteState.initial),
        ); // Default value
        expect(question.isCorrect, isFalse); // Default value
        expect(question.answer, isNull);
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final choices = [Choice(text: 'Choice 1', value: '1', isCorrect: true)];

        final question1 = QuestionModel(
          questionId: 'q1',
          order: 1,
          question: 'What is 2+2?',
          choices: choices,
        );

        final question2 = QuestionModel(
          questionId: 'q1',
          order: 1,
          question: 'What is 2+2?',
          choices: choices,
        );

        expect(question1, equals(question2));
        expect(question1.hashCode, equals(question2.hashCode));
      });

      test('should not be equal when questionId is different', () {
        final question1 = QuestionModel(questionId: 'q1');
        final question2 = QuestionModel(questionId: 'q2');

        expect(question1, isNot(equals(question2)));
        expect(question1.hashCode, isNot(equals(question2.hashCode)));
      });

      test('should not be equal when order is different', () {
        final question1 = QuestionModel(order: 1);
        final question2 = QuestionModel(order: 2);

        expect(question1, isNot(equals(question2)));
        expect(question1.hashCode, isNot(equals(question2.hashCode)));
      });

      test('should not be equal when question text is different', () {
        final question1 = QuestionModel(question: 'Question 1');
        final question2 = QuestionModel(question: 'Question 2');

        expect(question1, isNot(equals(question2)));
        expect(question1.hashCode, isNot(equals(question2.hashCode)));
      });

      test('should not be equal when choices are different', () {
        final choices1 = [Choice(text: 'Choice 1')];
        final choices2 = [Choice(text: 'Choice 2')];

        final question1 = QuestionModel(choices: choices1);
        final question2 = QuestionModel(choices: choices2);

        expect(question1, isNot(equals(question2)));
        expect(question1.hashCode, isNot(equals(question2.hashCode)));
      });
    });

    group('CopyWith Method', () {
      test('should create copy with updated questionId', () {
        final original = QuestionModel(
          questionId: 'q1',
          question: 'Original question',
        );

        final updated = original.copyWith(questionId: 'q2');

        expect(updated.questionId, equals('q2'));
        expect(updated.question, equals(original.question));
      });

      test('should create copy with updated question text', () {
        final original = QuestionModel(
          questionId: 'q1',
          question: 'Original question',
        );

        final updated = original.copyWith(question: 'Updated question');

        expect(updated.questionId, equals(original.questionId));
        expect(updated.question, equals('Updated question'));
      });

      test('should create copy with updated choices', () {
        final originalChoices = [Choice(text: 'Original choice')];
        final newChoices = [Choice(text: 'New choice')];

        final original = QuestionModel(choices: originalChoices);
        final updated = original.copyWith(choices: newChoices);

        expect(updated.choices, equals(newChoices));
        expect(updated.choices, isNot(equals(originalChoices)));
      });

      test('should create copy with updated state', () {
        final original = QuestionModel();

        final updated = original.copyWith(
          state: QuestionConcreteState.answered,
        );

        expect(updated.state, equals(QuestionConcreteState.answered));
        expect(original.state, equals(QuestionConcreteState.initial));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final question = QuestionModel(
          questionId: 'q1',
          question: 'What is 2+2?',
        );

        final stringRepresentation = question.toString();

        expect(stringRepresentation, contains('QuestionModel'));
        expect(stringRepresentation, contains('q1'));
        expect(stringRepresentation, contains('What is 2+2?'));
      });
    });
  });

  group('Choice Tests', () {
    group('Constructor and Properties', () {
      test('should create Choice with default values', () {
        final choice = Choice();

        expect(choice.text, equals(''));
        expect(choice.value, equals(''));
        expect(choice.isCorrect, isFalse);
      });

      test('should create Choice with custom values', () {
        final choice = Choice(
          text: 'Choice text',
          value: 'choice_value',
          isCorrect: true,
        );

        expect(choice.text, equals('Choice text'));
        expect(choice.value, equals('choice_value'));
        expect(choice.isCorrect, isTrue);
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final choice = Choice(
          text: 'Choice text',
          value: 'choice_value',
          isCorrect: true,
        );

        final json = choice.toJson();

        expect(json['text'], equals('Choice text'));
        expect(json['value'], equals('choice_value'));
        expect(json['is_correct'], isTrue);
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'text': 'Choice text',
          'value': 'choice_value',
          'is_correct': false,
        };

        final choice = Choice.fromJson(json);

        expect(choice.text, equals('Choice text'));
        expect(choice.value, equals('choice_value'));
        expect(choice.isCorrect, isFalse);
      });

      test('should handle missing optional fields in JSON', () {
        final json = {'text': 'Choice text'};

        final choice = Choice.fromJson(json);

        expect(choice.text, equals('Choice text'));
        expect(choice.value, equals('')); // Default value
        expect(choice.isCorrect, isFalse); // Default value
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final choice1 = Choice(
          text: 'Choice text',
          value: 'choice_value',
          isCorrect: true,
        );

        final choice2 = Choice(
          text: 'Choice text',
          value: 'choice_value',
          isCorrect: true,
        );

        expect(choice1, equals(choice2));
        expect(choice1.hashCode, equals(choice2.hashCode));
      });

      test('should not be equal when text is different', () {
        final choice1 = Choice(text: 'Choice 1');
        final choice2 = Choice(text: 'Choice 2');

        expect(choice1, isNot(equals(choice2)));
        expect(choice1.hashCode, isNot(equals(choice2.hashCode)));
      });

      test('should not be equal when value is different', () {
        final choice1 = Choice(value: 'value1');
        final choice2 = Choice(value: 'value2');

        expect(choice1, isNot(equals(choice2)));
        expect(choice1.hashCode, isNot(equals(choice2.hashCode)));
      });

      test('should not be equal when isCorrect is different', () {
        final choice1 = Choice(isCorrect: true);
        final choice2 = Choice(isCorrect: false);

        expect(choice1, isNot(equals(choice2)));
        expect(choice1.hashCode, isNot(equals(choice2.hashCode)));
      });
    });

    group('CopyWith Method', () {
      test('should create copy with updated text', () {
        final original = Choice(text: 'Original text');

        final updated = original.copyWith(text: 'Updated text');

        expect(updated.text, equals('Updated text'));
        expect(original.text, equals('Original text'));
      });

      test('should create copy with updated value', () {
        final original = Choice(value: 'original_value');

        final updated = original.copyWith(value: 'updated_value');

        expect(updated.value, equals('updated_value'));
        expect(original.value, equals('original_value'));
      });

      test('should create copy with updated isCorrect', () {
        final original = Choice(isCorrect: false);

        final updated = original.copyWith(isCorrect: true);

        expect(updated.isCorrect, isTrue);
        expect(original.isCorrect, isFalse);
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final choice = Choice(
          text: 'Choice text',
          value: 'choice_value',
          isCorrect: true,
        );

        final stringRepresentation = choice.toString();

        expect(stringRepresentation, contains('Choice'));
        expect(stringRepresentation, contains('Choice text'));
        expect(stringRepresentation, contains('choice_value'));
        expect(stringRepresentation, contains('true'));
      });
    });
  });

  group('QuestionConcreteState Enum Tests', () {
    test('should have correct values', () {
      expect(QuestionConcreteState.initial.name, equals('initial'));
      expect(QuestionConcreteState.answered.name, equals('answered'));
    });

    test('should compare states correctly', () {
      expect(
        QuestionConcreteState.initial,
        isNot(equals(QuestionConcreteState.answered)),
      );
      expect(
        QuestionConcreteState.answered,
        isNot(equals(QuestionConcreteState.initial)),
      );
    });
  });

  group('QuestionResultModel Tests', () {
    group('Constructor and Properties', () {
      test('should create QuestionResultModel with default values', () {
        final result = QuestionResultModel();

        expect(result.point, equals(0));
        expect(result.maxValue, equals(0));
        expect(result.minValue, equals(0));
        expect(result.level, isNull);
        expect(result.description, isNull);
      });

      test('should create QuestionResultModel with custom values', () {
        final result = QuestionResultModel(
          point: 10,
          maxValue: 20,
          minValue: 5,
          level: {'name': 'A1', 'description': 'Beginner'},
          description: {'en': 'Good job!', 'es': '¡Buen trabajo!'},
        );

        expect(result.point, equals(10));
        expect(result.maxValue, equals(20));
        expect(result.minValue, equals(5));
        expect(result.level, equals({'name': 'A1', 'description': 'Beginner'}));
        expect(
          result.description,
          equals({'en': 'Good job!', 'es': '¡Buen trabajo!'}),
        );
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final result = QuestionResultModel(
          point: 15,
          maxValue: 25,
          minValue: 10,
          level: {'name': 'B1'},
          description: {'en': 'Well done!'},
        );

        final json = result.toJson();

        expect(json['point'], equals(15));
        expect(json['maxValue'], equals(25));
        expect(json['minValue'], equals(10));
        expect(json['level'], equals({'name': 'B1'}));
        expect(json['description'], equals({'en': 'Well done!'}));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'point': 12,
          'maxValue': 22,
          'minValue': 7,
          'level': {'name': 'A2', 'description': 'Elementary'},
          'description': {'en': 'Nice work!', 'es': '¡Buen trabajo!'},
        };

        final result = QuestionResultModel.fromJson(json);

        expect(result.point, equals(12));
        expect(result.maxValue, equals(22));
        expect(result.minValue, equals(7));
        expect(
          result.level,
          equals({'name': 'A2', 'description': 'Elementary'}),
        );
        expect(
          result.description,
          equals({'en': 'Nice work!', 'es': '¡Buen trabajo!'}),
        );
      });

      test('should handle missing optional fields in JSON', () {
        final json = {'point': 5};

        final result = QuestionResultModel.fromJson(json);

        expect(result.point, equals(5));
        expect(result.maxValue, equals(0)); // Default value
        expect(result.minValue, equals(0)); // Default value
        expect(result.level, isNull);
        expect(result.description, isNull);
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final result1 = QuestionResultModel(
          point: 10,
          maxValue: 20,
          minValue: 5,
          level: {'name': 'A1'},
          description: {'en': 'Good job!'},
        );

        final result2 = QuestionResultModel(
          point: 10,
          maxValue: 20,
          minValue: 5,
          level: {'name': 'A1'},
          description: {'en': 'Good job!'},
        );

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when point is different', () {
        final result1 = QuestionResultModel(point: 10);
        final result2 = QuestionResultModel(point: 15);

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });

      test('should not be equal when level is different', () {
        final result1 = QuestionResultModel(level: {'name': 'A1'});
        final result2 = QuestionResultModel(level: {'name': 'A2'});

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('CopyWith Method', () {
      test('should create copy with updated point', () {
        final original = QuestionResultModel(point: 5);

        final updated = original.copyWith(point: 10);

        expect(updated.point, equals(10));
        expect(original.point, equals(5));
      });

      test('should create copy with updated level', () {
        final originalLevel = {'name': 'A1'};
        final newLevel = {'name': 'A2'};

        final original = QuestionResultModel(level: originalLevel);
        final updated = original.copyWith(level: newLevel);

        expect(updated.level, equals(newLevel));
        expect(original.level, equals(originalLevel));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final result = QuestionResultModel(point: 15, maxValue: 25);

        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('QuestionResultModel'));
        expect(stringRepresentation, contains('15'));
        expect(stringRepresentation, contains('25'));
      });
    });
  });
}
