// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chapter_title_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChapterTitleState {

 List<ChapterIndexData> get paths; ChapterData? get chapter;
/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChapterTitleStateCopyWith<ChapterTitleState> get copyWith => _$ChapterTitleStateCopyWithImpl<ChapterTitleState>(this as ChapterTitleState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChapterTitleState&&const DeepCollectionEquality().equals(other.paths, paths)&&(identical(other.chapter, chapter) || other.chapter == chapter));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(paths),chapter);

@override
String toString() {
  return 'ChapterTitleState(paths: $paths, chapter: $chapter)';
}


}

/// @nodoc
abstract mixin class $ChapterTitleStateCopyWith<$Res>  {
  factory $ChapterTitleStateCopyWith(ChapterTitleState value, $Res Function(ChapterTitleState) _then) = _$ChapterTitleStateCopyWithImpl;
@useResult
$Res call({
 List<ChapterIndexData> paths, ChapterData? chapter
});


$ChapterDataCopyWith<$Res>? get chapter;

}
/// @nodoc
class _$ChapterTitleStateCopyWithImpl<$Res>
    implements $ChapterTitleStateCopyWith<$Res> {
  _$ChapterTitleStateCopyWithImpl(this._self, this._then);

  final ChapterTitleState _self;
  final $Res Function(ChapterTitleState) _then;

/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paths = null,Object? chapter = freezed,}) {
  return _then(_self.copyWith(
paths: null == paths ? _self.paths : paths // ignore: cast_nullable_to_non_nullable
as List<ChapterIndexData>,chapter: freezed == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as ChapterData?,
  ));
}
/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDataCopyWith<$Res>? get chapter {
    if (_self.chapter == null) {
    return null;
  }

  return $ChapterDataCopyWith<$Res>(_self.chapter!, (value) {
    return _then(_self.copyWith(chapter: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChapterTitleState].
extension ChapterTitleStatePatterns on ChapterTitleState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChapterTitleState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChapterTitleState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChapterTitleState value)  $default,){
final _that = this;
switch (_that) {
case _ChapterTitleState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChapterTitleState value)?  $default,){
final _that = this;
switch (_that) {
case _ChapterTitleState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ChapterIndexData> paths,  ChapterData? chapter)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChapterTitleState() when $default != null:
return $default(_that.paths,_that.chapter);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ChapterIndexData> paths,  ChapterData? chapter)  $default,) {final _that = this;
switch (_that) {
case _ChapterTitleState():
return $default(_that.paths,_that.chapter);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ChapterIndexData> paths,  ChapterData? chapter)?  $default,) {final _that = this;
switch (_that) {
case _ChapterTitleState() when $default != null:
return $default(_that.paths,_that.chapter);case _:
  return null;

}
}

}

/// @nodoc


class _ChapterTitleState extends ChapterTitleState {
   _ChapterTitleState({final  List<ChapterIndexData> paths = const [], this.chapter}): _paths = paths,super._();
  

 final  List<ChapterIndexData> _paths;
@override@JsonKey() List<ChapterIndexData> get paths {
  if (_paths is EqualUnmodifiableListView) return _paths;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_paths);
}

@override final  ChapterData? chapter;

/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChapterTitleStateCopyWith<_ChapterTitleState> get copyWith => __$ChapterTitleStateCopyWithImpl<_ChapterTitleState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChapterTitleState&&const DeepCollectionEquality().equals(other._paths, _paths)&&(identical(other.chapter, chapter) || other.chapter == chapter));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_paths),chapter);

@override
String toString() {
  return 'ChapterTitleState(paths: $paths, chapter: $chapter)';
}


}

/// @nodoc
abstract mixin class _$ChapterTitleStateCopyWith<$Res> implements $ChapterTitleStateCopyWith<$Res> {
  factory _$ChapterTitleStateCopyWith(_ChapterTitleState value, $Res Function(_ChapterTitleState) _then) = __$ChapterTitleStateCopyWithImpl;
@override @useResult
$Res call({
 List<ChapterIndexData> paths, ChapterData? chapter
});


@override $ChapterDataCopyWith<$Res>? get chapter;

}
/// @nodoc
class __$ChapterTitleStateCopyWithImpl<$Res>
    implements _$ChapterTitleStateCopyWith<$Res> {
  __$ChapterTitleStateCopyWithImpl(this._self, this._then);

  final _ChapterTitleState _self;
  final $Res Function(_ChapterTitleState) _then;

/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paths = null,Object? chapter = freezed,}) {
  return _then(_ChapterTitleState(
paths: null == paths ? _self._paths : paths // ignore: cast_nullable_to_non_nullable
as List<ChapterIndexData>,chapter: freezed == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as ChapterData?,
  ));
}

/// Create a copy of ChapterTitleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChapterDataCopyWith<$Res>? get chapter {
    if (_self.chapter == null) {
    return null;
  }

  return $ChapterDataCopyWith<$Res>(_self.chapter!, (value) {
    return _then(_self.copyWith(chapter: value));
  });
}
}

// dart format on
