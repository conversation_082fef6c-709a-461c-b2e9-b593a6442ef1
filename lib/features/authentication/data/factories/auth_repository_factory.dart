import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:selfeng/features/authentication/data/repositories/auth_repository_impl.dart';
import 'package:selfeng/features/authentication/data/repositories/google_sign_in_repository_impl.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';

class AuthRepositoryFactory {
  static AuthRepository createAuthRepository([
    firebase_auth.FirebaseAuth? firebaseAuth,
  ]) {
    return AuthRepositoryImpl(
      firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
    );
  }

  static GoogleSignInRepository createGoogleSignInRepository([
    GoogleSignIn? googleSignIn,
  ]) {
    return GoogleSignInRepositoryImpl(googleSignIn ?? GoogleSignIn.instance);
  }
}
