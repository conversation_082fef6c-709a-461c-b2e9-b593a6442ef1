import 'package:flutter/material.dart';

class StageTransitionWidget extends StatelessWidget {
  final String image;
  final String title;
  final String subTitle;

  const StageTransitionWidget({
    super.key,
    required this.image,
    required this.title,
    required this.subTitle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(title, style: Theme.of(context).textTheme.headlineMedium),
          const Sized<PERSON><PERSON>(height: 62),
          Container(
            width: 253,
            height: 253,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [
                  Color(0xffFE754C),
                  Color(0xffE21F29),
                  Color(0xffC3151F),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              image: DecorationImage(
                image: AssetImage(image),
                fit: BoxFit.fill,
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          const SizedBox(height: 24),
          Text(
            subTitle,
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
