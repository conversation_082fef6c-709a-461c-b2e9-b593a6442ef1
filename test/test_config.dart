import 'package:flutter_test/flutter_test.dart';

/// Test configuration and setup for the SelfEng project
class TestConfig {
  TestConfig._();

  /// Sets up the test environment
  static void setupTests() {
    TestWidgetsFlutterBinding.ensureInitialized();
  }

  /// Tears down the test environment
  static void tearDownTests() {
    // Add any global cleanup here
  }
}

/// Common test constants
class TestConstants {
  TestConstants._();

  static const String testEmail = '<EMAIL>';
  static const String testUserId = 'test_user_id';
  static const String testUserName = 'Test User';
  static const Duration testTimeout = Duration(seconds: 5);
}
