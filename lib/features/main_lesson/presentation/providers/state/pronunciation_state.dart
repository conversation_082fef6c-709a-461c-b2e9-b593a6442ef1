import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

part 'pronunciation_state.freezed.dart';

@freezed
abstract class PronunciationState with _$PronunciationState {
  factory PronunciationState({
    @Default([]) List<ContentIndexData> paths,
    ContentIndexData? currentPath,
    PronunciationSubPart? data,
    @Default(0) int currentPage,
    AudioPath? audioPath,
    PronunciationResultFormatted? response,
    @Default(0) int selectedIndex,
    @Default(false) bool nextSection,
    @Default(false) bool isLoading,
    @Default(true) bool isNewSubpart,
    @Default(false) bool isIntro,
    @Default(false) bool isRecording,
    PronunciationAgregateScore? agregateScore,
  }) = _PronunciationState;

  // Allow custom getters / setters
  const PronunciationState._();

  bool get isResponse => response != null;
  bool get isRecord => !isResponse && !nextSection && !isLoading;
}
