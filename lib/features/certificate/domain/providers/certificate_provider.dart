import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';

import '../repositories/certificate_repository.dart';
import '../../data/repositories/certificate_repository_impl.dart';

final certificateRepositoryProvider = Provider<CertificateRepository>((ref) {
  final FirestoreServiceRepository firestoreService = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return CertificateRepositoryImpl(firestoreService);
});
