# Notification Service Architecture

This document describes the improved notification service architecture that replaces the legacy `notification.dart` implementation.

## Overview

The new architecture separates concerns into distinct services:

1. **FCMServiceRepository** - Handles FCM token management and device identification
2. **NotificationServiceRepository** - Manages local and push notifications
3. **UserDataServiceRepository** - Extended to handle FCM token storage in Firestore

## Key Improvements

### 1. Proper Service Architecture
- Uses dependency injection with Riverpod providers
- Follows the existing repository pattern
- Integrates with the existing `UserDataServiceRepository`

### 2. Better Error Handling
- Uses the `Either<AppException, T>` pattern for consistent error handling
- Provides detailed error messages and identifiers

### 3. Consistent Data Storage
- FCM tokens are stored in the `user-data` collection (consistent with other user data)
- Includes metadata like platform, timestamp, and device information
- Uses proper Firestore merge operations

### 4. Separation of Concerns
- FCM token management is separate from notification display
- Device identification is handled independently
- Permission management is centralized

## Usage

### Basic Initialization

```dart
// In your main app initialization
class MyApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      home: FutureBuilder(
        future: _initializeServices(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const CircularProgressIndicator();
          }
          return const HomeScreen();
        },
      ),
    );
  }

  Future<void> _initializeServices(WidgetRef ref) async {
    final notificationService = ref.read(notificationServiceProvider);
    final result = await notificationService.initialize();
    
    result.fold(
      (error) => throw Exception('Failed to initialize notifications: ${error.message}'),
      (_) => {}, // Success
    );
  }
}
```

### Working with FCM Tokens

```dart
class NotificationManager extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fcmService = ref.watch(fcmServiceProvider);
    
    return ElevatedButton(
      onPressed: () async {
        // Get current FCM token
        final tokenResult = await fcmService.getFCMToken();
        tokenResult.fold(
          (error) => print('Error: ${error.message}'),
          (token) => print('FCM Token: $token'),
        );
        
        // Refresh token
        final refreshResult = await fcmService.refreshFCMToken();
        refreshResult.fold(
          (error) => print('Refresh failed: ${error.message}'),
          (_) => print('Token refreshed successfully'),
        );
      },
      child: const Text('Manage FCM Token'),
    );
  }
}
```

### Notification Count Management

```dart
class NotificationCounter extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationService = ref.watch(notificationServiceProvider);
    
    return FutureBuilder<Either<AppException, int>>(
      future: notificationService.getNotificationCount(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const CircularProgressIndicator();
        
        return snapshot.data!.fold(
          (error) => Text('Error: ${error.message}'),
          (count) => Badge(
            label: Text('$count'),
            child: const Icon(Icons.notifications),
          ),
        );
      },
    );
  }
}
```

## Data Structure

FCM tokens are stored in Firestore with the following structure:

```json
{
  "user-data/{userId}": {
    "fcm_tokens": {
      "{deviceId}": {
        "token": "fcm_token_string",
        "updated_at": "2024-01-01T00:00:00Z",
        "platform": "ios" // or "android"
      }
    }
  }
}
```

## Migration from Legacy Code

### Before (Legacy)
```dart
// Old approach - direct Firestore access
await setupFlutterNotifications();
```

### After (New Architecture)
```dart
// New approach - proper service injection
final notificationService = ref.read(notificationServiceProvider);
await notificationService.initialize();
```

## Service Dependencies

```
NotificationServiceRepository
├── FCMServiceRepository
│   ├── UserDataServiceRepository
│   ├── FirebaseMessaging
│   └── DeviceInfoPlugin
└── FirebaseMessaging
```

## Error Handling

All service methods return `Either<AppException, T>` for consistent error handling:

```dart
final result = await notificationService.initialize();
result.fold(
  (error) {
    // Handle error
    print('Error: ${error.identifier} - ${error.message}');
  },
  (success) {
    // Handle success
    print('Notification service initialized successfully');
  },
);
```

## Testing

The new architecture is more testable because:
- Dependencies are injected through providers
- Each service has a single responsibility
- Error handling is consistent and predictable
- Services can be easily mocked for unit tests

## Backward Compatibility

The legacy `setupFlutterNotifications()` function is maintained for backward compatibility but is deprecated. It internally uses the new service architecture.

## Future Enhancements

1. **Local Notification Scheduling** - Add support for scheduled local notifications
2. **Notification Categories** - Support for different notification types and categories
3. **Analytics Integration** - Track notification engagement metrics
4. **Multi-device Token Management** - Better handling of tokens across multiple devices
