import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

class AssessmentCard extends StatelessWidget {
  const AssessmentCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: const [
              Icon(Icons.assessment_rounded, size: 24, color: Colors.black87),
              SizedBox(width: 8),
              Text(
                'Take Self Assessment Test',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Complete a quick assessment to help us personalize your learning experience.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () {
              customNav(context, RouterName.questionnaireScreen);
            },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xffFDD8D8),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Icon(
                    Icons.play_circle_outline_rounded,
                    size: 20,
                    color: Colors.black87,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Start Assessment',
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
