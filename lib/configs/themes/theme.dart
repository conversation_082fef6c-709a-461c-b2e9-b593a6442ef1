import 'package:flutter/material.dart';

final darkTheme = ThemeData.dark();
Map<int, Color> color = {
  50: const Color.fromRGBO(255, 144, 14, .1),
  100: const Color.fromRGBO(255, 144, 14, .2),
  200: const Color.fromRGBO(255, 144, 14, .3),
  300: const Color.fromRGBO(255, 144, 14, .4),
  400: const Color.fromRGBO(255, 144, 14, .5),
  500: const Color.fromRGBO(255, 144, 14, .6),
  600: const Color.fromRGBO(255, 144, 14, .7),
  700: const Color.fromRGBO(255, 144, 14, .8),
  800: const Color.fromRGBO(255, 144, 14, .9),
  900: const Color.fromRGBO(255, 144, 14, 1),
};
// ThemeMode _themeMode = ThemeMode.system;
final lightTheme = ThemeData(
  useMaterial3: false,
  primarySwatch: MaterialColor(0xFFFF900E, color),
  primaryColor: const Color(0xFFB01515),
  primaryColorDark: Colors.black,
  scaffoldBackgroundColor: const Color(0xFFFFFFFF),
  brightness: Brightness.light,
  colorScheme: const ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFFB01515),
    onPrimary: Colors.white,
    secondary: Colors.orange,
    onSecondary: Colors.white,
    error: Color(0xFFB01515),
    onError: Color(0xFFB01515),
    surface: Colors.white,
    onSurface: Colors.black,
  ),
  dividerColor: const Color(0xFFF1F2F2),
  canvasColor: Colors.transparent,
);
