import 'package:flutter/material.dart';
import 'package:selfeng/shared/domain/models/models.dart';

const List<Color> kOnboardingGradientColors = [
  Color(0xffC3151F),
  Color(0xffE21F29),
  Color(0xffFE754C),
];

class OnboardingPage extends StatelessWidget {
  const OnboardingPage({
    super.key,
    required this.item,
    required this.screenWidth,
  });

  final DefaultModel item;
  final double screenWidth;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: kOnboardingGradientColors,
          begin: Alignment.bottomRight,
          end: Alignment.topLeft,
        ),
      ),
      child: Stack(
        children: [
          Align(
            alignment: FractionalOffset.bottomCenter,
            child: Image.asset(
              item.image,
              fit: BoxFit.fitWidth,
              width: screenWidth,
            ),
          ),
          Positioned(
            top: 120,
            left: 16,
            child: SizedBox(
              width: screenWidth - 32,
              child: Text(
                item.title,
                textAlign: TextAlign.center,
                style: Theme.of(
                  context,
                ).textTheme.headlineMedium?.copyWith(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
