// topic_game_controller.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/games/domain/providers/game_provider.dart';
import 'package:selfeng/features/games/presentation/providers/state/topic_game_state.dart';

part 'topic_game_controller.g.dart';

@Riverpod(keepAlive: true)
class TopicGameController extends _$TopicGameController {
  @override
  FutureOr<TopicGameState> build() async {
    final gameRepository = ref.watch(gameRepositoryProvider);
    final contents = await gameRepository.getTopicsMemoryFlash();

    return contents.fold(
      (failure) => throw failure,
      (data) => TopicGameState(topics: data),
    );
  }

  void nextTopic() {
    final currentState = state.valueOrNull;
    if (currentState == null || currentState.topics.isEmpty) return;

    final nextIndex =
        (currentState.currentTopicIndex + 1) % currentState.topics.length;
    state = AsyncData(currentState.copyWith(currentTopicIndex: nextIndex));
  }

  void previousTopic() {
    final currentState = state.valueOrNull;
    if (currentState == null || currentState.topics.isEmpty) return;

    final prevIndex =
        (currentState.currentTopicIndex - 1 + currentState.topics.length) %
        currentState.topics.length;
    state = AsyncData(currentState.copyWith(currentTopicIndex: prevIndex));
  }

  void selectTopic(int index) {
    final currentState = state.valueOrNull;
    if (currentState == null ||
        index < 0 ||
        index >= currentState.topics.length) {
      return;
    }

    state = AsyncData(currentState.copyWith(currentTopicIndex: index));
  }

  /// Called when a game is completed successfully
  /// This signals that the topic should advance to the next one
  void markGameCompleted() {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    state = AsyncData(currentState.copyWith(shouldAdvanceToNextTopic: true));
  }

  /// Called to advance to the next topic and reset the completion flag
  void advanceToNextTopicIfNeeded() {
    final currentState = state.valueOrNull;
    if (currentState == null || !currentState.shouldAdvanceToNextTopic) return;

    // Advance to next topic
    final nextIndex =
        (currentState.currentTopicIndex + 1) % currentState.topics.length;

    // Reset the completion flag and update the topic index
    state = AsyncData(
      currentState.copyWith(
        currentTopicIndex: nextIndex,
        shouldAdvanceToNextTopic: false,
      ),
    );
  }

  /// Reset the completion flag without advancing topic
  void resetCompletionFlag() {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    state = AsyncData(currentState.copyWith(shouldAdvanceToNextTopic: false));
  }
}
