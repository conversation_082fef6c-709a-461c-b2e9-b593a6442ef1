import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:google_sign_in/google_sign_in.dart';

// Repository Mocks - commented out due to import issues
// import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
// import 'package:selfeng/features/library/domain/repositories/library_repository.dart';
// import 'package:selfeng/features/questionnaire/domain/repositories/questionnaire_repository.dart';

/// Factory class for creating mock objects used in testing
/// Centralizes mock creation and provides consistent mock objects across tests
class MockFactories {
  MockFactories._();

  // Firebase Service Mocks
  static MockFirebaseAuth createMockFirebaseAuth() => MockFirebaseAuth();
  static MockFirebaseFirestore createMockFirebaseFirestore() =>
      MockFirebaseFirestore();
  static MockFirebaseStorage createMockFirebaseStorage() =>
      MockFirebaseStorage();
  static MockFirebaseMessaging createMockFirebaseMessaging() =>
      MockFirebaseMessaging();
  static MockUser createMockUser() => MockUser();
  static MockUserCredential createMockUserCredential() => MockUserCredential();
  // Firebase sealed class factories commented out
  // static MockCollectionReference createMockCollectionReference() => MockCollectionReference();
  // static MockDocumentReference createMockDocumentReference() => MockDocumentReference();
  // static MockDocumentSnapshot createMockDocumentSnapshot() => MockDocumentSnapshot();
  // static MockQuerySnapshot createMockQuerySnapshot() => MockQuerySnapshot();
  // static MockQueryDocumentSnapshot createMockQueryDocumentSnapshot() => MockQueryDocumentSnapshot();

  // External Service Mocks
  static MockSharedPreferences createMockSharedPreferences() =>
      MockSharedPreferences();
  static MockDio createMockDio() => MockDio();
  static MockResponse createMockResponse() => MockResponse();
  static MockFlutterLocalNotificationsPlugin
  createMockFlutterLocalNotificationsPlugin() =>
      MockFlutterLocalNotificationsPlugin();
  static MockGoogleSignIn createMockGoogleSignIn() => MockGoogleSignIn();
  static MockGoogleSignInAccount createMockGoogleSignInAccount() =>
      MockGoogleSignInAccount();
  static MockGoogleSignInAuthentication
  createMockGoogleSignInAuthentication() => MockGoogleSignInAuthentication();

  // Repository Mocks - commented out due to import issues
  // static MockMainLessonRepository createMockMainLessonRepository() => MockMainLessonRepository();
  // static MockLibraryRepository createMockLibraryRepository() => MockLibraryRepository();
  // static MockQuestionnaireRepository createMockQuestionnaireRepository() => MockQuestionnaireRepository();

  /// Sets up common mock behaviors for Firebase Auth
  static void setupFirebaseAuthMocks(
    MockFirebaseAuth mockAuth,
    MockUser mockUser,
  ) {
    when(() => mockAuth.currentUser).thenReturn(mockUser);
    when(
      () => mockAuth.authStateChanges(),
    ).thenAnswer((_) => Stream.value(mockUser));
    when(
      () => mockAuth.userChanges(),
    ).thenAnswer((_) => Stream.value(mockUser));
    when(() => mockUser.uid).thenReturn('test_user_id');
    when(() => mockUser.email).thenReturn('<EMAIL>');
    when(() => mockUser.displayName).thenReturn('Test User');
    when(() => mockUser.emailVerified).thenReturn(true);
  }

  /// Sets up common mock behaviors for Firestore - commented out due to sealed classes
  /*
  static void setupFirestoreMocks(
    MockFirebaseFirestore mockFirestore,
    MockCollectionReference mockCollection,
    MockDocumentReference mockDocument,
  ) {
    when(() => mockFirestore.collection(any())).thenReturn(mockCollection);
    when(() => mockCollection.doc(any())).thenReturn(mockDocument);
    when(() => mockDocument.collection(any())).thenReturn(mockCollection);
  }
  */

  /// Sets up common mock behaviors for SharedPreferences
  static void setupSharedPreferencesMocks(MockSharedPreferences mockPrefs) {
    when(() => mockPrefs.getString(any())).thenReturn(null);
    when(() => mockPrefs.getBool(any())).thenReturn(null);
    when(() => mockPrefs.getInt(any())).thenReturn(null);
    when(() => mockPrefs.getDouble(any())).thenReturn(null);
    when(() => mockPrefs.getStringList(any())).thenReturn(null);
    when(() => mockPrefs.setString(any(), any())).thenAnswer((_) async => true);
    when(() => mockPrefs.setBool(any(), any())).thenAnswer((_) async => true);
    when(() => mockPrefs.setInt(any(), any())).thenAnswer((_) async => true);
    when(() => mockPrefs.setDouble(any(), any())).thenAnswer((_) async => true);
    when(
      () => mockPrefs.setStringList(any(), any()),
    ).thenAnswer((_) async => true);
    when(() => mockPrefs.remove(any())).thenAnswer((_) async => true);
    when(() => mockPrefs.clear()).thenAnswer((_) async => true);
  }

  /// Sets up common mock behaviors for Dio HTTP client
  static void setupDioMocks(MockDio mockDio, MockResponse mockResponse) {
    when(() => mockResponse.statusCode).thenReturn(200);
    when(() => mockResponse.statusMessage).thenReturn('OK');
    when(() => mockResponse.data).thenReturn({});
    when(() => mockDio.get(any())).thenAnswer((_) async => mockResponse);
    when(
      () => mockDio.post(any(), data: any(named: 'data')),
    ).thenAnswer((_) async => mockResponse);
    when(
      () => mockDio.put(any(), data: any(named: 'data')),
    ).thenAnswer((_) async => mockResponse);
    when(() => mockDio.delete(any())).thenAnswer((_) async => mockResponse);
  }

  /// Sets up common mock behaviors for Google Sign In - commented out due to API issues
  /*
  static void setupGoogleSignInMocks(
    MockGoogleSignIn mockGoogleSignIn,
    MockGoogleSignInAccount mockAccount,
    MockGoogleSignInAuthentication mockAuth,
  ) {
    when(() => mockGoogleSignIn.signIn()).thenAnswer((_) async => mockAccount);
    when(() => mockGoogleSignIn.signOut()).thenAnswer((_) async => mockAccount);
    when(() => mockAccount.authentication).thenAnswer((_) async => mockAuth);
    when(() => mockAccount.email).thenReturn('<EMAIL>');
    when(() => mockAccount.displayName).thenReturn('Test User');
    when(() => mockAccount.id).thenReturn('test_google_id');
    when(() => mockAuth.accessToken).thenReturn('test_access_token');
    when(() => mockAuth.idToken).thenReturn('test_id_token');
  }
  */

  /// Creates a mock with common fallback behaviors
  static T createMockWithFallbacks<T extends Mock>(T mock) {
    // Register common fallback values for mocktail
    registerFallbackValue(Uri.parse('https://example.com'));
    registerFallbackValue(const Duration(seconds: 1));
    registerFallbackValue(<String, dynamic>{});
    registerFallbackValue(<String>[]);

    return mock;
  }
}

// Firebase Service Mock Classes
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirebaseFirestore extends Mock implements FirebaseFirestore {}

class MockFirebaseStorage extends Mock implements FirebaseStorage {}

class MockFirebaseMessaging extends Mock implements FirebaseMessaging {}

class MockUser extends Mock implements User {}

class MockUserCredential extends Mock implements UserCredential {}

// Firebase sealed classes cannot be mocked - commented out
// class MockCollectionReference extends Mock implements CollectionReference<Map<String, dynamic>> {}
// class MockDocumentReference extends Mock implements DocumentReference<Map<String, dynamic>> {}
// class MockDocumentSnapshot extends Mock implements DocumentSnapshot<Map<String, dynamic>> {}
// class MockQuerySnapshot extends Mock implements QuerySnapshot<Map<String, dynamic>> {}
// class MockQueryDocumentSnapshot extends Mock implements QueryDocumentSnapshot<Map<String, dynamic>> {}

// External Service Mock Classes
class MockSharedPreferences extends Mock implements SharedPreferences {}

class MockDio extends Mock implements Dio {}

class MockResponse extends Mock implements Response {}

class MockFlutterLocalNotificationsPlugin extends Mock
    implements FlutterLocalNotificationsPlugin {}

class MockAndroidFlutterLocalNotificationsPlugin extends Mock
    implements AndroidFlutterLocalNotificationsPlugin {}

class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}

class MockGoogleSignInAuthentication extends Mock
    implements GoogleSignInAuthentication {}

// Repository Mock Classes - commented out due to import issues
// class MockMainLessonRepository extends Mock implements MainLessonRepository {}
// class MockLibraryRepository extends Mock implements LibraryRepository {}
// class MockQuestionnaireRepository extends Mock implements QuestionnaireRepository {}

/// Utility class for setting up complex mock scenarios
class MockScenarios {
  MockScenarios._();

  /// Sets up a complete authenticated user scenario
  static void setupAuthenticatedUserScenario({
    required MockFirebaseAuth mockAuth,
    required MockUser mockUser,
    String userId = 'test_user_id',
    String email = '<EMAIL>',
  }) {
    MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);
    when(() => mockUser.uid).thenReturn(userId);
    when(() => mockUser.email).thenReturn(email);
  }

  /// Sets up a network error scenario
  static void setupNetworkErrorScenario(MockDio mockDio) {
    when(() => mockDio.get(any())).thenThrow(
      DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
        message: 'Connection timeout',
      ),
    );
  }

  /// Sets up a successful API response scenario
  static void setupSuccessfulApiScenario(
    MockDio mockDio,
    Map<String, dynamic> responseData,
  ) {
    final mockResponse = MockFactories.createMockResponse();
    when(() => mockResponse.statusCode).thenReturn(200);
    when(() => mockResponse.data).thenReturn(responseData);
    when(() => mockDio.get(any())).thenAnswer((_) async => mockResponse);
    when(
      () => mockDio.post(any(), data: any(named: 'data')),
    ).thenAnswer((_) async => mockResponse);
  }
}
