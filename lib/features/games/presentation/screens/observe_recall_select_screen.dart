// import 'dart:async';
// import 'dart:math';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:go_router/go_router.dart';
// import 'package:selfeng/features/games/domain/models/game_word.dart';
//
// class ObserveRecallScreen extends StatefulWidget {
//   final List<String> words;
//   final double initialSpeed;
//
//   const ObserveRecallScreen({
//     super.key,
//     this.words = const [
//       'Good morning.',
//       'Hello!',
//       'Welcome!',
//       'Great day!',
//       'Flutter!',
//     ],
//     this.initialSpeed = 1.5,
//   });
//
//   @override
//   State<ObserveRecallScreen> createState() => _ObserveRecallScreenState();
// }
//
//
// enum GameStage { observe, recall, finished }
//
// class _ObserveRecallScreenState extends State<ObserveRecallScreen>
//     with SingleTickerProviderStateMixin {
//   late List<WordObject> activeWords;
//   late List<String> _wordRandom;
//   late List<String> _wordChoices;
//   late Random random;
//   int score = 0;
//   late double gameSpeed;
//   Timer? gameTimer;
//   bool isPlaying = false;
//   late AnimationController _animationController;
//   late String _correctWord;
//   late GameStage _currentStage;
//   int _currentWordIndex = 0;
//   int _score = 0;
//
//   @override
//   void initState() {
//     super.initState();
//     _currentStage = GameStage.observe;
//     // Force landscape orientation
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.landscapeLeft,
//       DeviceOrientation.landscapeRight,
//     ]);
//
//     random = Random();
//     activeWords = [];
//     _wordRandom = [];
//     _wordChoices = [];
//     gameSpeed = widget.initialSpeed;
//
//     // Initialize the animation controller properly
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 16),
//     );
//
//     // Use animation controller to update word positions
//     _animationController.addListener(_updateWordPositions);
//     _animationController.repeat();
//   }
//
//   @override
//   void dispose() {
//     // Reset to all orientations when leaving screen
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//       DeviceOrientation.portraitDown,
//       DeviceOrientation.landscapeLeft,
//       DeviceOrientation.landscapeRight,
//     ]);
//
//     gameTimer?.cancel();
//     _animationController.removeListener(_updateWordPositions);
//     _animationController.dispose();
//     super.dispose();
//   }
//
//   void startGame() {
//     if (isPlaying) return;
//
//     setState(() {
//       isPlaying = true;
//       score = 0;
//       activeWords = [];
//       _wordRandom = widget.words;
//     });
//
//     // Timer to add new words periodically
//     gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
//       if (!isPlaying) {
//         timer.cancel();
//         return;
//       }
//
//       setState(() {
//         // Add a new word with random vertical position
//         final word = widget.words[random.nextInt(widget.words.length)];
//         final verticalPosition =
//             random.nextDouble() * 0.8 + 0.1; // Between 10% and 90% of height
//
//         activeWords.add(
//           WordObject(
//             word: word,
//             verticalPosition: verticalPosition,
//             horizontalPosition: 0.0,
//             speed:
//             gameSpeed *
//                 (0.8 + random.nextDouble() * 0.4), // Random speed variation
//           ),
//         );
//       });
//     });
//   }
//
//   void stopGame() {
//     setState(() {
//       isPlaying = false;
//       _currentStage = GameStage.recall;
//       gameTimer?.cancel();
//     });
//   }
//
//   void exitGame() {
//     // Set orientation back to portrait when exiting game
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//       DeviceOrientation.portraitDown,
//     ]);
//
//     // Stop the game if it's running
//     if (isPlaying) {
//       stopGame();
//     }
//
//     // Navigate back if there's a previous screen
//     // Navigator.of(context).pop();
//     if (Navigator.canPop(context)) {
//       context.pop();
//     }
//   }
//
//   void increaseSpeed() {
//     setState(() {
//       gameSpeed += 0.5;
//     });
//   }
//
//   void decreaseSpeed() {
//     setState(() {
//       if (gameSpeed > 0.5) {
//         gameSpeed -= 0.5;
//       }
//     });
//   }
//
//   // void catchWord(int index) {
//   //   setState(() {
//   //     score += 1;
//   //     activeWords.removeAt(index);
//   //   });
//   // }
//
//   void _updateWordPositions() {
//     if (!isPlaying) return;
//
//     // Create a new list for the updated words
//     final updatedWords = <WordObject>[];
//
//     for (final word in activeWords) {
//       // Only keep words that are still on screen
//       if (word.horizontalPosition! <= 1.2) {
//         updatedWords.add(
//           word.copyWith(
//             horizontalPosition: word.horizontalPosition! + (word.speed! / 100),
//           ),
//         );
//       }
//     }
//
//     if (updatedWords.length != activeWords.length) {
//       // Only call setState if the list has changed
//       setState(() {
//         activeWords = updatedWords;
//       });
//     } else if (updatedWords.isNotEmpty) {
//       // Update positions without removing anything
//       setState(() {
//         activeWords = updatedWords;
//       });
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       // Handle back button press
//       onPopInvokedWithResult: (didPop, result) {
//         exitGame();
//         // return false; // Prevent default back navigation
//       },
//       child: Scaffold(
//         appBar: AppBar(
//           title: const Text(
//             'Word Game',
//             style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
//           ),
//           backgroundColor: Colors.deepOrange[900],
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.white),
//             onPressed: exitGame,
//           ),
//         ),
//         body: Container(
//           decoration: const BoxDecoration(
//             // Yellowish-red/amber gradient background
//             gradient: LinearGradient(
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//               colors: [
//                 Color(0xFFD84315), // Deep orange
//                 Color(0xFFFFA000), // Amber
//               ],
//             ),
//           ),
//           child: Stack(
//             children: [
//               // Background pattern
//               CustomPaint(size: Size.infinite, painter: ArrowPatternPainter()),
//               // Game content
//               Container(
//                 padding: EdgeInsets.symmetric(horizontal: 40),
//                 child: _buildContent(),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildContent() {
//     switch (_currentStage) {
//       case GameStage.observe:
//         return _buildObserveStage();
//       case GameStage.recall:
//         return _buildRecallStage();
//       case GameStage.finished:
//         return _buildFinishedStage();
//     }
//   }
//
//   Widget _buildObserveStage() {
//     return Column(
//       children: [
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Speed: ${gameSpeed.toStringAsFixed(1)}',
//                 style: const TextStyle(
//                   color: Colors.white,
//                   fontSize: 20,
//                   fontWeight: FontWeight.bold,
//                   shadows: [
//                     Shadow(
//                       offset: Offset(1.0, 1.0),
//                       blurRadius: 3.0,
//                       color: Color.fromARGB(255, 0, 0, 0),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//
//         Expanded(
//           child: Stack(
//             children: [
//               for (int i = 0; i < activeWords.length; i++)
//                 Positioned(
//                   left:
//                   MediaQuery.of(context).size.width *
//                       activeWords[i].horizontalPosition!,
//                   top:
//                   MediaQuery.of(context).size.height *
//                       activeWords[i].verticalPosition!,
//                   child: GestureDetector(
//                     // onTap: () => catchWord(i),
//                     child: Container(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 16,
//                         vertical: 8,
//                       ),
//                       decoration: BoxDecoration(
//                         color: Colors.white.withValues(alpha: .2),
//                         borderRadius: BorderRadius.circular(12),
//                         border: Border.all(
//                           color: Colors.white.withValues(alpha: .5),
//                           width: 2,
//                         ),
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black.withValues(alpha: .2),
//                             blurRadius: 4,
//                             offset: const Offset(2, 2),
//                           ),
//                         ],
//                       ),
//                       child: Text(
//                         activeWords[i].word,
//                         style: const TextStyle(
//                           color: Colors.white,
//                           fontSize: 24,
//                           fontWeight: FontWeight.bold,
//                           shadows: [
//                             Shadow(
//                               offset: Offset(1.0, 1.0),
//                               blurRadius: 2.0,
//                               color: Color.fromARGB(255, 0, 0, 0),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               ElevatedButton(
//                 onPressed: decreaseSpeed,
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.deepOrange[900],
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('Slower'),
//               ),
//               ElevatedButton(
//                 onPressed: isPlaying ? stopGame : startGame,
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor:
//                   isPlaying ? Colors.red[900] : Colors.green[900],
//                   foregroundColor: Colors.white,
//                   minimumSize: const Size(100, 50),
//                 ),
//                 child: Text(isPlaying ? 'Stop' : 'Start'),
//               ),
//               ElevatedButton(
//                 onPressed: increaseSpeed,
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.deepOrange[900],
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('Faster'),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildRecallStage() {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Text(
//           'Which word did you see?',
//           style: TextStyle(
//             color: Colors.white,
//             fontSize: 24,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         const SizedBox(height: 20),
//         Wrap(
//           spacing: 10,
//           runSpacing: 10,
//           children: _wordRandom.map((word) =>
//               ElevatedButton(
//                 onPressed: () => _selectWord(word),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.white.withValues(alpha: 0.2),
//                   foregroundColor: _wordChoices.contains(word) ? Colors.blueAccent : Colors.white,
//                   minimumSize: const Size(120, 50),
//                 ),
//                 child: Text(
//                   word,
//                   style: const TextStyle(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               )
//           ).toList(),
//         ),
//         const SizedBox(height: 20),
//         ElevatedButton(
//           onPressed: () => _submit(),
//           style: ElevatedButton.styleFrom(
//             backgroundColor:
//             isPlaying ? Colors.red[900] : Colors.green[900],
//             foregroundColor: Colors.white,
//             minimumSize: const Size(100, 50),
//           ),
//           child: Text('Submit'),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildFinishedStage() {
//     return Container(
//       child: Text(
//         '$_score',
//         style: const TextStyle(
//           fontSize: 18,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }
//
//   void _selectWord(String selectedWord) {
//     setState(() {
//       if (_wordChoices.contains(selectedWord))
//         _wordChoices.removeAt(_wordChoices.indexWhere((word) => word == selectedWord));
//       else _wordChoices.add(selectedWord);
//     });
//   }
//
//   void _submit() {
//     setState(() {
//       widget.words.forEach((word){
//         if (_wordChoices.contains(word)) {
//           _score = _score + 1;
//         }
//       });
//       _currentStage = GameStage.finished;
//     });
//   }
// }
//
//
// // Background pattern painter
// class ArrowPatternPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint =
//     Paint()
//       ..color = Colors.white.withValues(alpha: .15)
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = 2;
//
//     // Draw angled lines like in the image
//     const double spacing = 40;
//     const double arrowWidth = 30;
//
//     for (double x = -size.width; x < size.width * 2; x += spacing) {
//       final path = Path();
//
//       for (double y = -size.height; y < size.height * 2; y += spacing) {
//         // Draw arrow/chevron shapes
//         path.moveTo(x, y);
//         path.lineTo(x + arrowWidth, y + arrowWidth);
//         path.lineTo(x, y + arrowWidth * 2);
//       }
//
//       canvas.drawPath(path, paint);
//     }
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
// }
