import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';

class AuthRepositoryImpl implements AuthRepository {
  final firebase_auth.FirebaseAuth _firebaseAuth;

  AuthRepositoryImpl(this._firebaseAuth);

  @override
  firebase_auth.User? get currentUser => _firebaseAuth.currentUser;

  @override
  Future<void> signOut() => _firebaseAuth.signOut();

  @override
  Future<firebase_auth.UserCredential> signInWithCredential(
    firebase_auth.AuthCredential credential,
  ) {
    return _firebaseAuth.signInWithCredential(credential);
  }

  @override
  Future<String?> getIdToken(bool forceRefresh) async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      return await user.getIdToken(forceRefresh);
    }
    return null;
  }
}
