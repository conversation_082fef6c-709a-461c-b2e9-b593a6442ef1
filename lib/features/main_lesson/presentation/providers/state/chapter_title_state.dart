import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';

part 'chapter_title_state.freezed.dart';

@freezed
abstract class ChapterTitleState with _$ChapterTitleState {
  factory ChapterTitleState({
    @Default([]) List<ChapterIndexData> paths,
    ChapterData? chapter,
  }) = _ChapterTitleState;

  // Allow custom getters / setters
  const ChapterTitleState._();
}
