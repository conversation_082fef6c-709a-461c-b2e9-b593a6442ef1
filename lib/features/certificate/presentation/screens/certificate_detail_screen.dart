import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/theme/app_colors.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';
import 'package:selfeng/shared/widgets/v_dialog_alert.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import '../providers/certificate_navigation_provider.dart';
import '../../app/services/certificate_download_service.dart';
import '../../app/services/certificate_share_service.dart';

class CertificateDetailScreen extends ConsumerStatefulWidget {
  final String level;

  const CertificateDetailScreen({super.key, required this.level});

  @override
  ConsumerState<CertificateDetailScreen> createState() =>
      _CertificateDetailScreenState();
}

class _CertificateDetailScreenState
    extends ConsumerState<CertificateDetailScreen> {
  final CertificateDownloadService _downloadService =
      CertificateDownloadService();
  final CertificateShareService _shareService = CertificateShareService();

  bool _isDownloadingPage1 = false;
  bool _isDownloadingPage2 = false;
  bool _isSharingPage1 = false;
  bool _isSharingPage2 = false;
  bool _isRetakingCertificate = false;
  Certificate? _selectedCertificate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final certificateData = ref.read(certificateNavigationControllerProvider);
      if (certificateData != null && certificateData.certificates.isNotEmpty) {
        setState(() {
          // Find the selected certificate by ID, or use the first one as fallback
          if (certificateData.selectedCertificateId != null) {
            _selectedCertificate = certificateData.certificates.firstWhere(
              (cert) => cert.id == certificateData.selectedCertificateId,
              orElse: () => certificateData.certificates.first,
            );
          } else {
            _selectedCertificate = certificateData.certificates.first;
          }
        });
      }
    });
  }

  // Color scheme for different levels (matching existing pattern)
  Color _colorForLevel(String name) {
    final n = name.toLowerCase();
    if (n.contains('a1') || n.contains('beginner')) {
      return const Color(0xFF4F46E5); // Indigo 600
    } else if (n.contains('a2')) {
      return const Color(0xFF10B981); // Emerald 500
    } else if (n.contains('b1')) {
      return const Color(0xFF8B5CF6); // Violet 500
    } else if (n.contains('b2')) {
      return const Color(0xFF06B6D4); // Cyan 500
    } else if (n.contains('c1')) {
      return const Color(0xFFF59E0B); // Amber 500
    } else if (n.contains('c2') || n.contains('advanced')) {
      return const Color(0xFFEF4444); // Red 500
    }
    return const Color(0xFF2563EB); // Default: Blue 600
  }

  void _showSnackBar(
    String message, {
    bool isError = false,
    Color? backgroundColor,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor:
            backgroundColor ?? (isError ? AppColors.error : Colors.green[600]),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Generates a filename using the format: {certificate_id}_{page_number}.png
  /// All downloaded certificates are converted to PNG format by the download service
  String _generateFileName(String certificateId, int pageNumber, String url) {
    // All certificates are converted to PNG format during download
    return '${certificateId}_$pageNumber.png';
  }

  Future<void> _downloadCertificate(
    String url,
    String fileName,
    int page,
  ) async {
    setState(() {
      if (page == 1) {
        _isDownloadingPage1 = true;
      } else {
        _isDownloadingPage2 = true;
      }
    });

    try {
      final result = await _downloadService.downloadCertificate(
        url: url,
        fileName: fileName,
      );

      if (mounted) {
        String message;
        if (result['success']) {
          // Use localized success message based on platform
          message =
              Platform.isAndroid
                  ? context.loc.certificate_saved_to_downloads
                  : context.loc.certificate_saved_to_files_app;
        } else {
          // Use the original error message from service
          message = result['message'] as String;
        }
        _showSnackBar(message, isError: !result['success']);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(context.loc.download_failed(e.toString()), isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          if (page == 1) {
            _isDownloadingPage1 = false;
          } else {
            _isDownloadingPage2 = false;
          }
        });
      }
    }
  }

  Future<void> _shareCertificate(String url, String fileName, int page) async {
    final shareText = context.loc.check_out_my_certificate(widget.level);

    setState(() {
      if (page == 1) {
        _isSharingPage1 = true;
      } else {
        _isSharingPage2 = true;
      }
    });

    try {
      final result = await _shareService.shareCertificate(
        url: url,
        fileName: fileName,
        text: shareText,
      );

      if (mounted && !result['success']) {
        _showSnackBar(result['message'] as String, isError: true);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(context.loc.share_failed(e.toString()), isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          if (page == 1) {
            _isSharingPage1 = false;
          } else {
            _isSharingPage2 = false;
          }
        });
      }
    }
  }

  Future<void> _shareAllPages() async {
    if (_selectedCertificate == null) return;

    setState(() {
      _isSharingPage1 = true;
      if (_selectedCertificate!.certificateUrlPage2 != null) {
        _isSharingPage2 = true;
      }
    });

    try {
      final urls = <String>[_selectedCertificate!.certificateUrl];
      final fileNames = <String>[
        _generateFileName(
          _selectedCertificate!.id,
          1,
          _selectedCertificate!.certificateUrl,
        ),
      ];

      if (_selectedCertificate!.certificateUrlPage2 != null) {
        urls.add(_selectedCertificate!.certificateUrlPage2!);
        fileNames.add(
          _generateFileName(
            _selectedCertificate!.id,
            2,
            _selectedCertificate!.certificateUrlPage2!,
          ),
        );
      }

      final shareText = context.loc.check_out_my_certificates(widget.level);
      final result = await _shareService.shareMultipleCertificates(
        urls: urls,
        fileNames: fileNames,
        text: shareText,
      );

      if (mounted && !result['success']) {
        _showSnackBar(result['message'] as String, isError: true);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(context.loc.share_failed(e.toString()), isError: true);
      }
    } finally {
      setState(() {
        _isSharingPage1 = false;
        if (_selectedCertificate!.certificateUrlPage2 != null) {
          _isSharingPage2 = false;
        }
      });
    }
  }

  /// Shows a confirmation dialog for retaking the certificate
  void _showRetakeCertificateDialog() {
    // Make the message easier to scan by adding a line break between sentences
    final warningMessage = context.loc.retake_certificate_warning_message
        .replaceAll('. ', '.\n');

    VDialogAlert(
      // Remove the word "warning" from the title, use icon and colors to convey urgency
      title: context.loc.retake_certificate,
      barrierDismissible: false,
      icon: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.red.shade100,
        ),
        padding: const EdgeInsets.all(10),
        child: Icon(
          Icons.warning_amber_rounded,
          color: Colors.red.shade700,
          size: 36,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Highlighted warning box
          Container(
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.06),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withValues(alpha: 0.20)),
            ),
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.info_outline, color: Colors.red.shade700, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    warningMessage,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.black87,
                      height: 1.35,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Actions
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Continue / Retake (destructive, prominent)
              VButtonGradient(
                title: context.loc.continue1,
                backgroundColor: Colors.red.shade700,
                fontStyle: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Colors.white),
                leading: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                  size: 20,
                ),
                onTap: () {
                  context.pop(); // Close dialog
                  _retakeCertificate();
                },
              ),
              const SizedBox(height: 12),
              // Cancel (neutral, outlined)
              VButtonGradient(
                title: context.loc.cancel,
                fontStyle: Theme.of(context).textTheme.bodyLarge,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  color: Colors.white,
                  border: Border.all(color: const Color(0xff802115), width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: .05),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                onTap: () => context.pop(),
              ),
            ],
          ),
        ],
      ),
    ).showMyDialog(context);
  }

  /// Calls the Firebase resetLevel function to reset the user's progress
  Future<void> _retakeCertificate() async {
    if (_selectedCertificate == null) return;

    setState(() {
      _isRetakingCertificate = true;
    });

    try {
      final certificateData = ref.read(certificateNavigationControllerProvider);
      if (certificateData == null) {
        throw Exception('Certificate data not available');
      }

      final levelId = certificateData.level.id;

      // Call the Firebase resetLevel function
      final firestoreService = ref.read(firestoreServiceRepositoryProvider);
      await firestoreService.firebaseFunctions.httpsCallable('resetLevel').call(
        {'levelId': levelId},
      );

      if (mounted) {
        _showSnackBar(context.loc.retake_certificate_success);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(context.loc.retake_certificate_error, isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRetakingCertificate = false;
        });
      }
    }
  }

  Widget _buildCertificateImage(String imageUrl, String pageLabel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Page label with icon
        Container(
          margin: const EdgeInsets.only(left: 8.0, bottom: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.description, size: 16, color: AppColors.primary),
              const SizedBox(width: 6),
              Text(
                pageLabel,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        // Certificate image
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 8.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.0),
            child: AspectRatio(
              aspectRatio:
                  4 /
                  3, // Landscape certificate ratio (more typical for certificates)
              child: SvgPicture.network(
                imageUrl,
                fit:
                    BoxFit
                        .contain, // Changed from cover to contain to show full certificate
                placeholderBuilder:
                    (context) => Container(
                      color: Colors.grey[50],
                      child: const Center(child: LoadingCircle()),
                    ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isLoading,
    Color? backgroundColor,
  }) {
    return Expanded(
      child: Container(
        height: 56,
        margin: const EdgeInsets.symmetric(horizontal: 4.0),
        child: ElevatedButton.icon(
          onPressed: isLoading ? null : onPressed,
          icon:
              isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : Icon(icon, size: 20),
          label: Text(title),
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppColors.primary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final certificateData = ref.watch(certificateNavigationControllerProvider);

    // If no data is available, show error or navigate back
    if (certificateData == null) {
      return Scaffold(
        appBar: AppBar(title: Text(context.loc.certificate_detail)),
        body: Center(child: Text(context.loc.no_certificate_data_available)),
      );
    }

    final level = certificateData.level;
    final certificates = certificateData.certificates;

    if (_selectedCertificate == null && certificates.isNotEmpty) {
      // Find the selected certificate by ID, or use the first one as fallback
      if (certificateData.selectedCertificateId != null) {
        _selectedCertificate = certificates.firstWhere(
          (cert) => cert.id == certificateData.selectedCertificateId,
          orElse: () => certificates.first,
        );
      } else {
        _selectedCertificate = certificates.first;
      }
    }

    if (_selectedCertificate == null) {
      return Scaffold(
        appBar: AppBar(title: Text(level.name)),
        body: Center(child: Text(context.loc.no_certificate_selected)),
      );
    }

    final formattedDate = DateFormat(
      'd MMMM yyyy',
    ).format(_selectedCertificate!.dateIssued);
    final levelColor = _colorForLevel(level.name);
    final hasSecondPage = _selectedCertificate!.certificateUrlPage2 != null;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          context.loc.level_certificate(level.name),
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Certificate Info Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: levelColor,
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: levelColor.withValues(alpha: 0.3),
                    blurRadius: 18,
                    spreadRadius: 1,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedCertificate!.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedCertificate!.description,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(Icons.schedule, color: Colors.white, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        context.loc.issued_on(formattedDate),
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Certificate Images
            Text(
              context.loc.certificate_pages,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // Page 1
            _buildCertificateImage(
              _selectedCertificate!.certificateUrl,
              context.loc.page_1,
            ),

            const SizedBox(height: 12),

            // Action buttons for Page 1
            Row(
              children: [
                _buildActionButton(
                  title: context.loc.download,
                  icon: Icons.download,
                  onPressed:
                      () => _downloadCertificate(
                        _selectedCertificate!.certificateUrl,
                        _generateFileName(
                          _selectedCertificate!.id,
                          1,
                          _selectedCertificate!.certificateUrl,
                        ),
                        1,
                      ),
                  isLoading: _isDownloadingPage1,
                ),
                _buildActionButton(
                  title: context.loc.share,
                  icon: Icons.share,
                  onPressed:
                      () => _shareCertificate(
                        _selectedCertificate!.certificateUrl,
                        _generateFileName(
                          _selectedCertificate!.id,
                          1,
                          _selectedCertificate!.certificateUrl,
                        ),
                        1,
                      ),
                  isLoading: _isSharingPage1,
                  backgroundColor: Colors.blue[600],
                ),
              ],
            ),

            // Page 2 (if exists)
            if (hasSecondPage) ...[
              const SizedBox(height: 24),
              _buildCertificateImage(
                _selectedCertificate!.certificateUrlPage2!,
                context.loc.page_2,
              ),
              const SizedBox(height: 12),

              // Action buttons for Page 2
              Row(
                children: [
                  _buildActionButton(
                    title: context.loc.download,
                    icon: Icons.download,
                    onPressed:
                        () => _downloadCertificate(
                          _selectedCertificate!.certificateUrlPage2!,
                          _generateFileName(
                            _selectedCertificate!.id,
                            2,
                            _selectedCertificate!.certificateUrlPage2!,
                          ),
                          2,
                        ),
                    isLoading: _isDownloadingPage2,
                  ),
                  _buildActionButton(
                    title: context.loc.share,
                    icon: Icons.share,
                    onPressed:
                        () => _shareCertificate(
                          _selectedCertificate!.certificateUrlPage2!,
                          _generateFileName(
                            _selectedCertificate!.id,
                            2,
                            _selectedCertificate!.certificateUrlPage2!,
                          ),
                          2,
                        ),
                    isLoading: _isSharingPage2,
                    backgroundColor: Colors.blue[600],
                  ),
                ],
              ),
            ],

            const SizedBox(height: 32),

            // Share All Button (if multiple pages)
            if (hasSecondPage)
              SizedBox(
                width: double.infinity,
                height: 56,
                child: VButtonGradient(
                  title: context.loc.share_all_pages,
                  onTap: _shareAllPages,
                ),
              ),

            const SizedBox(height: 24),

            // Retake Certificate Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed:
                    _isRetakingCertificate
                        ? null
                        : _showRetakeCertificateDialog,
                icon:
                    _isRetakingCertificate
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.refresh, size: 20),
                label: Text(context.loc.retake_certificate),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                  elevation: 2,
                ),
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
