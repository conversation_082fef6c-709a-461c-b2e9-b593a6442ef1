// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class StickyHeader extends StatelessWidget {
  const StickyHeader({super.key, required this.title, this.subtitle});
  final String title;
  final String? subtitle;
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topLeft,
      child: MediaQuery.removePadding(
        context: context,
        removeBottom: true,
        child: ListView(
          primary: false,
          shrinkWrap: true,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () => context.pop(),
                    child: SizedBox(
                      height: 40,
                      width: 40,
                      child: const Icon(
                        Icons.chevron_left_rounded,
                        size: 36,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Text(title, style: Theme.of(context).textTheme.titleLarge),
                  <PERSON>(
                    children: [
                      IconButton(
                        icon: const Icon(
                          Icons.info_outline,
                          size: 32,
                          color: Color(0xffD9D9D9),
                        ),
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.bookmark_border_outlined,
                          size: 32,
                          color: Color(0xff540005),
                        ),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            if (subtitle != null)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 22,
                  vertical: 10,
                ),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xffFA3236),
                      Color(0xffD9353C),
                      Color(0xff8C1412),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Text(
                  subtitle ?? '',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.white),
                  // overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  softWrap: true,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
