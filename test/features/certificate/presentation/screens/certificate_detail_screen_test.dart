import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CertificateDetailScreen', () {
    group('_generateFileName', () {
      late _CertificateDetailScreenTestHelper helper;

      setUp(() {
        helper = _CertificateDetailScreenTestHelper();
      });

      test(
        'should generate filename with certificate ID and page number - always PNG',
        () {
          // Arrange
          const certificateId = 'cert-123';
          const pageNumber = 1;
          const url = 'https://example.com/certificate.png';

          // Act
          final result = helper.generateFileName(
            certificateId,
            pageNumber,
            url,
          );

          // Assert
          expect(result, equals('cert-123_1.png'));
        },
      );

      test(
        'should generate PNG filename regardless of original URL extension (SVG)',
        () {
          // Arrange
          const certificateId = 'cert-456';
          const pageNumber = 2;
          const url = 'https://example.com/certificate.svg';

          // Act
          final result = helper.generateFileName(
            certificateId,
            pageNumber,
            url,
          );

          // Assert
          expect(result, equals('cert-456_2.png'));
        },
      );

      test(
        'should generate PNG filename regardless of original URL extension (PDF)',
        () {
          // Arrange
          const certificateId = 'cert-789';
          const pageNumber = 1;
          const url = 'https://example.com/certificate.pdf';

          // Act
          final result = helper.generateFileName(
            certificateId,
            pageNumber,
            url,
          );

          // Assert
          expect(result, equals('cert-789_1.png'));
        },
      );

      test(
        'should generate PNG filename regardless of original URL extension (JPEG)',
        () {
          // Arrange
          const certificateId = 'cert-abc';
          const pageNumber = 2;
          const url = 'https://example.com/certificate.jpeg';

          // Act
          final result = helper.generateFileName(
            certificateId,
            pageNumber,
            url,
          );

          // Assert
          expect(result, equals('cert-abc_2.png'));
        },
      );

      test('should generate PNG filename for unknown file types', () {
        // Arrange
        const certificateId = 'cert-xyz';
        const pageNumber = 1;
        const url = 'https://example.com/certificate.unknown';

        // Act
        final result = helper.generateFileName(certificateId, pageNumber, url);

        // Assert
        expect(result, equals('cert-xyz_1.png'));
      });

      test('should generate PNG filename for URLs without extension', () {
        // Arrange
        const certificateId = 'cert-noext';
        const pageNumber = 1;
        const url = 'https://example.com/certificate';

        // Act
        final result = helper.generateFileName(certificateId, pageNumber, url);

        // Assert
        expect(result, equals('cert-noext_1.png'));
      });

      test('should generate PNG filename for malformed URLs', () {
        // Arrange
        const certificateId = 'cert-malformed';
        const pageNumber = 1;
        const url = 'not-a-valid-url';

        // Act
        final result = helper.generateFileName(certificateId, pageNumber, url);

        // Assert
        expect(result, equals('cert-malformed_1.png'));
      });
    });
  });
}

/// Test helper class to access private methods of CertificateDetailScreen
class _CertificateDetailScreenTestHelper {
  /// Generates a filename using the same logic as the private _generateFileName method
  /// All downloaded certificates are converted to PNG format by the download service
  String generateFileName(String certificateId, int pageNumber, String url) {
    // All certificates are converted to PNG format during download
    return '${certificateId}_$pageNumber.png';
  }
}
