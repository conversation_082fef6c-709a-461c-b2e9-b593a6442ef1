import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:selfeng/shared/data/remote/remote.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/response.dart' as response;
import 'package:selfeng/shared/exceptions/http_exception.dart';

mixin ExceptionHandlerMixin on NetworkService {
  Future<Either<AppException, response.Response>>
  handleException<T extends Object>(
    Future<Response<dynamic>> Function() handler, {
    String endpoint = '',
  }) async {
    try {
      final res = await handler();

      return Right(
        response.Response(
          statusCode: res.statusCode ?? 200,
          data: res.data,
          statusMessage: res.statusMessage,
        ),
      );
      // ParseResponse resParse = ParseResponse.fromMap(res.data);
      // if(resParse.success) {
      //   return Right(
      //     response.Response(
      //       statusCode: res.statusCode ?? 200,
      //       data: resParse.data,
      //       // message: resParse.message,
      //       // refreshToken: resParse.refreshToken,
      //       statusMessage: res.statusMessage,
      //     ),
      //   );
      // } else {
      //   return Left(
      //     AppException(
      //       message: resParse.message ?? '',
      //       statusCode: 9,
      //       identifier: '${resParse.data.toString()}\nResponse false',
      //     ),
      //   );
      // }
    } catch (e) {
      String message = '';
      String identifier = '';
      int statusCode = 0;
      log(e.runtimeType.toString());
      switch (e) {
        case SocketException():
          message = 'Unable to connect to the server.';
          statusCode = 0;
          identifier = 'Socket Exception ${e.message}\n at  $endpoint';
          break;

        case DioException():
          message = e.response?.data?['message'] ?? 'Internal Error occurred';
          statusCode = 1;
          identifier = 'DioException ${e.message} \nat  $endpoint';
          break;

        default:
          message = 'Unknown error occurred';
          statusCode = 2;
          identifier = 'Unknown error ${e.toString()}\n at $endpoint';
      }
      return Left(
        AppException(
          message: message,
          statusCode: statusCode,
          identifier: identifier,
        ),
      );
    }
  }
}
