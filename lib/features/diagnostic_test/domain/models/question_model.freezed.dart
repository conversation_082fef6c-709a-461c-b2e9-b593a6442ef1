// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'question_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuestionModel {

@JsonKey(name: 'question_id') String? get questionId; int get order; String get question;@J<PERSON>Key(name: 'image_url') String get image;@JsonKey(name: 'is_active') dynamic get isActive; List<Choice> get choices;@JsonKey(includeFromJson: false) QuestionConcreteState get state;@JsonKey(includeFromJson: false) bool get isCorrect;@JsonKey(includeFromJson: false) Choice? get answer;
/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QuestionModelCopyWith<QuestionModel> get copyWith => _$QuestionModelCopyWithImpl<QuestionModel>(this as QuestionModel, _$identity);

  /// Serializes this QuestionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is QuestionModel&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.order, order) || other.order == order)&&(identical(other.question, question) || other.question == question)&&(identical(other.image, image) || other.image == image)&&const DeepCollectionEquality().equals(other.isActive, isActive)&&const DeepCollectionEquality().equals(other.choices, choices)&&(identical(other.state, state) || other.state == state)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&(identical(other.answer, answer) || other.answer == answer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,questionId,order,question,image,const DeepCollectionEquality().hash(isActive),const DeepCollectionEquality().hash(choices),state,isCorrect,answer);

@override
String toString() {
  return 'QuestionModel(questionId: $questionId, order: $order, question: $question, image: $image, isActive: $isActive, choices: $choices, state: $state, isCorrect: $isCorrect, answer: $answer)';
}


}

/// @nodoc
abstract mixin class $QuestionModelCopyWith<$Res>  {
  factory $QuestionModelCopyWith(QuestionModel value, $Res Function(QuestionModel) _then) = _$QuestionModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'question_id') String? questionId, int order, String question,@JsonKey(name: 'image_url') String image,@JsonKey(name: 'is_active') dynamic isActive, List<Choice> choices,@JsonKey(includeFromJson: false) QuestionConcreteState state,@JsonKey(includeFromJson: false) bool isCorrect,@JsonKey(includeFromJson: false) Choice? answer
});


$ChoiceCopyWith<$Res>? get answer;

}
/// @nodoc
class _$QuestionModelCopyWithImpl<$Res>
    implements $QuestionModelCopyWith<$Res> {
  _$QuestionModelCopyWithImpl(this._self, this._then);

  final QuestionModel _self;
  final $Res Function(QuestionModel) _then;

/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? questionId = freezed,Object? order = null,Object? question = null,Object? image = null,Object? isActive = freezed,Object? choices = null,Object? state = null,Object? isCorrect = null,Object? answer = freezed,}) {
  return _then(_self.copyWith(
questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,isActive: freezed == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as dynamic,choices: null == choices ? _self.choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choice>,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as QuestionConcreteState,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as Choice?,
  ));
}
/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChoiceCopyWith<$Res>? get answer {
    if (_self.answer == null) {
    return null;
  }

  return $ChoiceCopyWith<$Res>(_self.answer!, (value) {
    return _then(_self.copyWith(answer: value));
  });
}
}


/// Adds pattern-matching-related methods to [QuestionModel].
extension QuestionModelPatterns on QuestionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _QuestionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _QuestionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _QuestionModel value)  $default,){
final _that = this;
switch (_that) {
case _QuestionModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _QuestionModel value)?  $default,){
final _that = this;
switch (_that) {
case _QuestionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'question_id')  String? questionId,  int order,  String question, @JsonKey(name: 'image_url')  String image, @JsonKey(name: 'is_active')  dynamic isActive,  List<Choice> choices, @JsonKey(includeFromJson: false)  QuestionConcreteState state, @JsonKey(includeFromJson: false)  bool isCorrect, @JsonKey(includeFromJson: false)  Choice? answer)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _QuestionModel() when $default != null:
return $default(_that.questionId,_that.order,_that.question,_that.image,_that.isActive,_that.choices,_that.state,_that.isCorrect,_that.answer);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'question_id')  String? questionId,  int order,  String question, @JsonKey(name: 'image_url')  String image, @JsonKey(name: 'is_active')  dynamic isActive,  List<Choice> choices, @JsonKey(includeFromJson: false)  QuestionConcreteState state, @JsonKey(includeFromJson: false)  bool isCorrect, @JsonKey(includeFromJson: false)  Choice? answer)  $default,) {final _that = this;
switch (_that) {
case _QuestionModel():
return $default(_that.questionId,_that.order,_that.question,_that.image,_that.isActive,_that.choices,_that.state,_that.isCorrect,_that.answer);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'question_id')  String? questionId,  int order,  String question, @JsonKey(name: 'image_url')  String image, @JsonKey(name: 'is_active')  dynamic isActive,  List<Choice> choices, @JsonKey(includeFromJson: false)  QuestionConcreteState state, @JsonKey(includeFromJson: false)  bool isCorrect, @JsonKey(includeFromJson: false)  Choice? answer)?  $default,) {final _that = this;
switch (_that) {
case _QuestionModel() when $default != null:
return $default(_that.questionId,_that.order,_that.question,_that.image,_that.isActive,_that.choices,_that.state,_that.isCorrect,_that.answer);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _QuestionModel implements QuestionModel {
   _QuestionModel({@JsonKey(name: 'question_id') this.questionId, this.order = 0, this.question = '', @JsonKey(name: 'image_url') this.image = '', @JsonKey(name: 'is_active') this.isActive = true, final  List<Choice> choices = const [], @JsonKey(includeFromJson: false) this.state = QuestionConcreteState.initial, @JsonKey(includeFromJson: false) this.isCorrect = false, @JsonKey(includeFromJson: false) this.answer}): _choices = choices;
  factory _QuestionModel.fromJson(Map<String, dynamic> json) => _$QuestionModelFromJson(json);

@override@JsonKey(name: 'question_id') final  String? questionId;
@override@JsonKey() final  int order;
@override@JsonKey() final  String question;
@override@JsonKey(name: 'image_url') final  String image;
@override@JsonKey(name: 'is_active') final  dynamic isActive;
 final  List<Choice> _choices;
@override@JsonKey() List<Choice> get choices {
  if (_choices is EqualUnmodifiableListView) return _choices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_choices);
}

@override@JsonKey(includeFromJson: false) final  QuestionConcreteState state;
@override@JsonKey(includeFromJson: false) final  bool isCorrect;
@override@JsonKey(includeFromJson: false) final  Choice? answer;

/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QuestionModelCopyWith<_QuestionModel> get copyWith => __$QuestionModelCopyWithImpl<_QuestionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$QuestionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _QuestionModel&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.order, order) || other.order == order)&&(identical(other.question, question) || other.question == question)&&(identical(other.image, image) || other.image == image)&&const DeepCollectionEquality().equals(other.isActive, isActive)&&const DeepCollectionEquality().equals(other._choices, _choices)&&(identical(other.state, state) || other.state == state)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&(identical(other.answer, answer) || other.answer == answer));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,questionId,order,question,image,const DeepCollectionEquality().hash(isActive),const DeepCollectionEquality().hash(_choices),state,isCorrect,answer);

@override
String toString() {
  return 'QuestionModel(questionId: $questionId, order: $order, question: $question, image: $image, isActive: $isActive, choices: $choices, state: $state, isCorrect: $isCorrect, answer: $answer)';
}


}

/// @nodoc
abstract mixin class _$QuestionModelCopyWith<$Res> implements $QuestionModelCopyWith<$Res> {
  factory _$QuestionModelCopyWith(_QuestionModel value, $Res Function(_QuestionModel) _then) = __$QuestionModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'question_id') String? questionId, int order, String question,@JsonKey(name: 'image_url') String image,@JsonKey(name: 'is_active') dynamic isActive, List<Choice> choices,@JsonKey(includeFromJson: false) QuestionConcreteState state,@JsonKey(includeFromJson: false) bool isCorrect,@JsonKey(includeFromJson: false) Choice? answer
});


@override $ChoiceCopyWith<$Res>? get answer;

}
/// @nodoc
class __$QuestionModelCopyWithImpl<$Res>
    implements _$QuestionModelCopyWith<$Res> {
  __$QuestionModelCopyWithImpl(this._self, this._then);

  final _QuestionModel _self;
  final $Res Function(_QuestionModel) _then;

/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? questionId = freezed,Object? order = null,Object? question = null,Object? image = null,Object? isActive = freezed,Object? choices = null,Object? state = null,Object? isCorrect = null,Object? answer = freezed,}) {
  return _then(_QuestionModel(
questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,isActive: freezed == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as dynamic,choices: null == choices ? _self._choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choice>,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as QuestionConcreteState,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as Choice?,
  ));
}

/// Create a copy of QuestionModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChoiceCopyWith<$Res>? get answer {
    if (_self.answer == null) {
    return null;
  }

  return $ChoiceCopyWith<$Res>(_self.answer!, (value) {
    return _then(_self.copyWith(answer: value));
  });
}
}


/// @nodoc
mixin _$Choice {

 String get text; String get value;@JsonKey(name: 'is_correct') bool get isCorrect;
/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChoiceCopyWith<Choice> get copyWith => _$ChoiceCopyWithImpl<Choice>(this as Choice, _$identity);

  /// Serializes this Choice to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Choice&&(identical(other.text, text) || other.text == text)&&(identical(other.value, value) || other.value == value)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text,value,isCorrect);

@override
String toString() {
  return 'Choice(text: $text, value: $value, isCorrect: $isCorrect)';
}


}

/// @nodoc
abstract mixin class $ChoiceCopyWith<$Res>  {
  factory $ChoiceCopyWith(Choice value, $Res Function(Choice) _then) = _$ChoiceCopyWithImpl;
@useResult
$Res call({
 String text, String value,@JsonKey(name: 'is_correct') bool isCorrect
});




}
/// @nodoc
class _$ChoiceCopyWithImpl<$Res>
    implements $ChoiceCopyWith<$Res> {
  _$ChoiceCopyWithImpl(this._self, this._then);

  final Choice _self;
  final $Res Function(Choice) _then;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? text = null,Object? value = null,Object? isCorrect = null,}) {
  return _then(_self.copyWith(
text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [Choice].
extension ChoicePatterns on Choice {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Choice value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Choice value)  $default,){
final _that = this;
switch (_that) {
case _Choice():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Choice value)?  $default,){
final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String text,  String value, @JsonKey(name: 'is_correct')  bool isCorrect)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that.text,_that.value,_that.isCorrect);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String text,  String value, @JsonKey(name: 'is_correct')  bool isCorrect)  $default,) {final _that = this;
switch (_that) {
case _Choice():
return $default(_that.text,_that.value,_that.isCorrect);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String text,  String value, @JsonKey(name: 'is_correct')  bool isCorrect)?  $default,) {final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that.text,_that.value,_that.isCorrect);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Choice implements Choice {
   _Choice({this.text = '', this.value = '', @JsonKey(name: 'is_correct') this.isCorrect = false});
  factory _Choice.fromJson(Map<String, dynamic> json) => _$ChoiceFromJson(json);

@override@JsonKey() final  String text;
@override@JsonKey() final  String value;
@override@JsonKey(name: 'is_correct') final  bool isCorrect;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChoiceCopyWith<_Choice> get copyWith => __$ChoiceCopyWithImpl<_Choice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChoiceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Choice&&(identical(other.text, text) || other.text == text)&&(identical(other.value, value) || other.value == value)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text,value,isCorrect);

@override
String toString() {
  return 'Choice(text: $text, value: $value, isCorrect: $isCorrect)';
}


}

/// @nodoc
abstract mixin class _$ChoiceCopyWith<$Res> implements $ChoiceCopyWith<$Res> {
  factory _$ChoiceCopyWith(_Choice value, $Res Function(_Choice) _then) = __$ChoiceCopyWithImpl;
@override @useResult
$Res call({
 String text, String value,@JsonKey(name: 'is_correct') bool isCorrect
});




}
/// @nodoc
class __$ChoiceCopyWithImpl<$Res>
    implements _$ChoiceCopyWith<$Res> {
  __$ChoiceCopyWithImpl(this._self, this._then);

  final _Choice _self;
  final $Res Function(_Choice) _then;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? text = null,Object? value = null,Object? isCorrect = null,}) {
  return _then(_Choice(
text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$QuestionResultModel {

 int get point; int get maxValue; int get minValue; Map<String, dynamic>? get level; Map<String, dynamic>? get description;
/// Create a copy of QuestionResultModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<QuestionResultModel> get copyWith => _$QuestionResultModelCopyWithImpl<QuestionResultModel>(this as QuestionResultModel, _$identity);

  /// Serializes this QuestionResultModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is QuestionResultModel&&(identical(other.point, point) || other.point == point)&&(identical(other.maxValue, maxValue) || other.maxValue == maxValue)&&(identical(other.minValue, minValue) || other.minValue == minValue)&&const DeepCollectionEquality().equals(other.level, level)&&const DeepCollectionEquality().equals(other.description, description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,point,maxValue,minValue,const DeepCollectionEquality().hash(level),const DeepCollectionEquality().hash(description));

@override
String toString() {
  return 'QuestionResultModel(point: $point, maxValue: $maxValue, minValue: $minValue, level: $level, description: $description)';
}


}

/// @nodoc
abstract mixin class $QuestionResultModelCopyWith<$Res>  {
  factory $QuestionResultModelCopyWith(QuestionResultModel value, $Res Function(QuestionResultModel) _then) = _$QuestionResultModelCopyWithImpl;
@useResult
$Res call({
 int point, int maxValue, int minValue, Map<String, dynamic>? level, Map<String, dynamic>? description
});




}
/// @nodoc
class _$QuestionResultModelCopyWithImpl<$Res>
    implements $QuestionResultModelCopyWith<$Res> {
  _$QuestionResultModelCopyWithImpl(this._self, this._then);

  final QuestionResultModel _self;
  final $Res Function(QuestionResultModel) _then;

/// Create a copy of QuestionResultModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? point = null,Object? maxValue = null,Object? minValue = null,Object? level = freezed,Object? description = freezed,}) {
  return _then(_self.copyWith(
point: null == point ? _self.point : point // ignore: cast_nullable_to_non_nullable
as int,maxValue: null == maxValue ? _self.maxValue : maxValue // ignore: cast_nullable_to_non_nullable
as int,minValue: null == minValue ? _self.minValue : minValue // ignore: cast_nullable_to_non_nullable
as int,level: freezed == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [QuestionResultModel].
extension QuestionResultModelPatterns on QuestionResultModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _QuestionResultModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _QuestionResultModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _QuestionResultModel value)  $default,){
final _that = this;
switch (_that) {
case _QuestionResultModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _QuestionResultModel value)?  $default,){
final _that = this;
switch (_that) {
case _QuestionResultModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int point,  int maxValue,  int minValue,  Map<String, dynamic>? level,  Map<String, dynamic>? description)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _QuestionResultModel() when $default != null:
return $default(_that.point,_that.maxValue,_that.minValue,_that.level,_that.description);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int point,  int maxValue,  int minValue,  Map<String, dynamic>? level,  Map<String, dynamic>? description)  $default,) {final _that = this;
switch (_that) {
case _QuestionResultModel():
return $default(_that.point,_that.maxValue,_that.minValue,_that.level,_that.description);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int point,  int maxValue,  int minValue,  Map<String, dynamic>? level,  Map<String, dynamic>? description)?  $default,) {final _that = this;
switch (_that) {
case _QuestionResultModel() when $default != null:
return $default(_that.point,_that.maxValue,_that.minValue,_that.level,_that.description);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _QuestionResultModel implements QuestionResultModel {
   _QuestionResultModel({this.point = 0, this.maxValue = 0, this.minValue = 0, final  Map<String, dynamic>? level, final  Map<String, dynamic>? description}): _level = level,_description = description;
  factory _QuestionResultModel.fromJson(Map<String, dynamic> json) => _$QuestionResultModelFromJson(json);

@override@JsonKey() final  int point;
@override@JsonKey() final  int maxValue;
@override@JsonKey() final  int minValue;
 final  Map<String, dynamic>? _level;
@override Map<String, dynamic>? get level {
  final value = _level;
  if (value == null) return null;
  if (_level is EqualUnmodifiableMapView) return _level;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _description;
@override Map<String, dynamic>? get description {
  final value = _description;
  if (value == null) return null;
  if (_description is EqualUnmodifiableMapView) return _description;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of QuestionResultModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QuestionResultModelCopyWith<_QuestionResultModel> get copyWith => __$QuestionResultModelCopyWithImpl<_QuestionResultModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$QuestionResultModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _QuestionResultModel&&(identical(other.point, point) || other.point == point)&&(identical(other.maxValue, maxValue) || other.maxValue == maxValue)&&(identical(other.minValue, minValue) || other.minValue == minValue)&&const DeepCollectionEquality().equals(other._level, _level)&&const DeepCollectionEquality().equals(other._description, _description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,point,maxValue,minValue,const DeepCollectionEquality().hash(_level),const DeepCollectionEquality().hash(_description));

@override
String toString() {
  return 'QuestionResultModel(point: $point, maxValue: $maxValue, minValue: $minValue, level: $level, description: $description)';
}


}

/// @nodoc
abstract mixin class _$QuestionResultModelCopyWith<$Res> implements $QuestionResultModelCopyWith<$Res> {
  factory _$QuestionResultModelCopyWith(_QuestionResultModel value, $Res Function(_QuestionResultModel) _then) = __$QuestionResultModelCopyWithImpl;
@override @useResult
$Res call({
 int point, int maxValue, int minValue, Map<String, dynamic>? level, Map<String, dynamic>? description
});




}
/// @nodoc
class __$QuestionResultModelCopyWithImpl<$Res>
    implements _$QuestionResultModelCopyWith<$Res> {
  __$QuestionResultModelCopyWithImpl(this._self, this._then);

  final _QuestionResultModel _self;
  final $Res Function(_QuestionResultModel) _then;

/// Create a copy of QuestionResultModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? point = null,Object? maxValue = null,Object? minValue = null,Object? level = freezed,Object? description = freezed,}) {
  return _then(_QuestionResultModel(
point: null == point ? _self.point : point // ignore: cast_nullable_to_non_nullable
as int,maxValue: null == maxValue ? _self.maxValue : maxValue // ignore: cast_nullable_to_non_nullable
as int,minValue: null == minValue ? _self.minValue : minValue // ignore: cast_nullable_to_non_nullable
as int,level: freezed == level ? _self._level : level // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,description: freezed == description ? _self._description : description // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
