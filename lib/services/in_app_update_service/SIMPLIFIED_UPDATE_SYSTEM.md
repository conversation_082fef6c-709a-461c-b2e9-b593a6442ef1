# Simplified Update System - Critical vs Normal

## 🎯 **New Simplified System**

The update system has been simplified to just **2 types** of updates:

| Update Type | Condition | User Experience | When to Use |
|-------------|-----------|-----------------|-------------|
| **Critical** | Version difference > 5 | **Forced immediate update** - App blocks until updated | Major releases, security fixes, breaking changes |
| **Normal** | Version difference ≤ 5 | **Flexible update** - Downloads in background | Bug fixes, minor features, regular updates |

## 📊 **How It Works**

### Version Code Difference Calculation
```dart
final currentVersionCode = 1001;     // Current app version
final availableVersionCode = 1008;   // New version available
final difference = 1008 - 1001 = 7;  // Difference is 7

// Since 7 > 5, this is a CRITICAL update (forced)
```

### Examples
- **Current: 1001, Available: 1006** → Difference = 5 → **Normal Update** (flexible)
- **Current: 1001, Available: 1007** → Difference = 6 → **Critical Update** (forced)
- **Current: 1001, Available: 1002** → Difference = 1 → **Normal Update** (flexible)
- **Current: 1001, Available: 1101** → Difference = 100 → **Critical Update** (forced)

## 🔧 **Implementation Location**

**File:** `/lib/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart`

**Key Methods:**
```dart
/// Check if this is a critical update (version difference > 5)
bool get isCriticalUpdate {
  final currentVersionCode = _getCurrentVersionCode();
  final availableVersionCode = _updateInfo!.availableVersionCode;
  final versionDifference = availableVersionCode - currentVersionCode;
  return versionDifference > 5;
}

/// Check if this is a normal update (version difference ≤ 5)
bool get isNormalUpdate => !isCriticalUpdate;
```

## 🚀 **How to Control Update Type**

### Method 1: Version Code Strategy (Recommended)

Plan your version codes to control update behavior:

**For Normal Updates (Flexible):**
- Increment by 1-5: `1001 → 1002, 1003, 1004, 1005, 1006`
- Use for: Bug fixes, minor features, UI improvements

**For Critical Updates (Forced):**
- Jump by 6+: `1001 → 1007, 1010, 1020, 1100`
- Use for: Security fixes, major features, breaking changes

### Method 2: Versioning Convention

Establish a clear versioning strategy:

```
Version Pattern: MAJOR.MINOR.PATCH
Version Code Pattern: MMMMNNPP

Examples:
v1.0.1 → Version Code: 10001 (Normal increment)
v1.0.2 → Version Code: 10002 (Normal increment) 
v1.0.3 → Version Code: 10003 (Normal increment)
v1.1.0 → Version Code: 10100 (Critical - major feature)
v2.0.0 → Version Code: 20000 (Critical - major release)
```

### Method 3: Custom Threshold

You can modify the threshold by changing the comparison in the code:

```dart
// Current: > 5 means critical
return versionDifference > 5;

// Change to > 10 for higher threshold
return versionDifference > 10;

// Change to > 3 for lower threshold  
return versionDifference > 3;
```

## 📱 **User Experience**

### Critical Updates (Forced)
- **Dialog appears immediately** when update is detected
- **"Update Required" title** with red styling
- **No "Later" button** - user must update
- **App blocks** until update is complete
- **Uses immediate update flow**

### Normal Updates (Flexible)
- **Banner appears in dashboard** (non-intrusive)
- **"Update Available" title** with blue styling
- **"Later" button available** - user can defer
- **Downloads in background** while user continues using app
- **Uses flexible update flow**

## 🧪 **Testing the System**

### Test Critical Updates
1. **Current version code**: 1001
2. **Upload new version** with code: 1008 (difference = 7)
3. **Expected behavior**: Forced immediate update dialog

### Test Normal Updates  
1. **Current version code**: 1001
2. **Upload new version** with code: 1004 (difference = 3)
3. **Expected behavior**: Flexible update banner

### Debug Information
The system logs the version difference:
```
📊 Version difference: 7 (current: 1001, available: 1008)
```

## 🔧 **Customization Options**

### Change the Threshold
Edit the comparison in `isCriticalUpdate`:
```dart
// More aggressive (lower threshold)
return versionDifference > 3;

// More lenient (higher threshold)
return versionDifference > 10;

// Custom logic
return versionDifference > 5 || isSecurityUpdate();
```

### Add Additional Conditions
```dart
bool get isCriticalUpdate {
  final versionDifference = availableVersionCode - currentVersionCode;
  
  // Version difference rule
  if (versionDifference > 5) return true;
  
  // Additional rules
  if (isSecurityUpdate()) return true;
  if (isWeekend() && versionDifference > 2) return true;
  
  return false;
}
```

### Remote Configuration
```dart
bool get isCriticalUpdate {
  // Get threshold from remote config
  final threshold = FirebaseRemoteConfig.instance.getInt('critical_update_threshold');
  final versionDifference = availableVersionCode - currentVersionCode;
  
  return versionDifference > (threshold > 0 ? threshold : 5);
}
```

## ✅ **Benefits of Simplified System**

1. **Easy to Understand**: Only 2 types instead of 6 priority levels
2. **Predictable**: Based on version code difference (objective)
3. **Flexible**: Easy to adjust threshold as needed
4. **Automatic**: No manual priority setting required
5. **Consistent**: Same logic applies to all updates

## 📋 **Migration from Old System**

The old priority system (0-5) is still supported for backward compatibility:
- **Critical updates** return priority `5`
- **Normal updates** return priority `2`
- **`isHighPriorityUpdate`** maps to `isCriticalUpdate`

## 🎯 **Quick Reference**

**To make an update CRITICAL (forced):**
- Increase version code by **6 or more**

**To make an update NORMAL (flexible):**
- Increase version code by **5 or less**

**To change the threshold:**
- Edit the `> 5` comparison in `isCriticalUpdate` method

This simplified system gives you precise control over update behavior while being much easier to understand and manage! 🚀