// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'core_routes.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
  $dashboardRoute,
  $questionnaireRoute,
  $questionnaireFinishRoute,
  $diagnosticTestInstructionRoute,
  $diagnosticTestRoute,
  $diagnosticTestResultRoute,
  $diagnosticTestCongratulationRoute,
  $diagnosticTestOnboardRoute,
  $languageRoute,
  $selectedLanguageRoute,
  $profileSettingRoute,
  $certificateListRoute,
  $certificateRoute,
  $certificateDetailRoute,
  $notificationSettingRoute,
  $splashRoute,
  $onboardingRoute,
  $chapterTitleRoute,
  $pronunciationChallengeRoute,
  $pronunciationChallengeOnboardingRoute,
  $pronunciationChallengeInstructionRoute,
  $pronunciationChallengeContentResultRoute,
  $pronunciationChallengeResultRoute,
  $videoConversationRoute,
  $videoConversationOnboardingRoute,
  $videoConversationInstructionRoute,
  $videoConversationResultRoute,
  $listeningMasteryRoute,
  $listeningMasteryOnboardingRoute,
  $listeningMasteryInstructionRoute,
  $listeningMasteryResultRoute,
  $speakingArenaRoute,
  $speakingArenaStageRoute,
  $speakingArenaOnboardingRoute,
  $speakingArenaInstructionRoute,
  $speakingArenaContentResultRoute,
  $speakingArenaResultRoute,
  $certificateNotificationRoute,
  $libraryRoute,
  $libraryChapterRoute,
  $libraryChapterContentRoute,
  $observeRecalRoute,
  $topicGameRoute,
  $searchRoute,
];

RouteBase get $dashboardRoute => GoRouteData.$route(
  path: '/',
  name: 'dashboardscreenroute',

  factory: _$DashboardRoute._fromState,
);

mixin _$DashboardRoute on GoRouteData {
  static DashboardRoute _fromState(GoRouterState state) =>
      const DashboardRoute();

  @override
  String get location => GoRouteData.$location('/');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $questionnaireRoute => GoRouteData.$route(
  path: '/questionnaire',
  name: 'questionnairescreenroute',

  factory: _$QuestionnaireRoute._fromState,
);

mixin _$QuestionnaireRoute on GoRouteData {
  static QuestionnaireRoute _fromState(GoRouterState state) =>
      const QuestionnaireRoute();

  @override
  String get location => GoRouteData.$location('/questionnaire');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $questionnaireFinishRoute => GoRouteData.$route(
  path: '/questionnaire-finish',
  name: 'questionnairefinishscreenroute',

  factory: _$QuestionnaireFinishRoute._fromState,
);

mixin _$QuestionnaireFinishRoute on GoRouteData {
  static QuestionnaireFinishRoute _fromState(GoRouterState state) =>
      const QuestionnaireFinishRoute();

  @override
  String get location => GoRouteData.$location('/questionnaire-finish');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $diagnosticTestInstructionRoute => GoRouteData.$route(
  path: '/diagnostic-test/instruction',
  name: 'diagnostictestinstscreenroute',

  factory: _$DiagnosticTestInstructionRoute._fromState,
);

mixin _$DiagnosticTestInstructionRoute on GoRouteData {
  static DiagnosticTestInstructionRoute _fromState(GoRouterState state) =>
      const DiagnosticTestInstructionRoute();

  @override
  String get location => GoRouteData.$location('/diagnostic-test/instruction');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $diagnosticTestRoute => GoRouteData.$route(
  path: '/diagnostic-test',
  name: 'diagnostictestscreenroute',

  factory: _$DiagnosticTestRoute._fromState,
);

mixin _$DiagnosticTestRoute on GoRouteData {
  static DiagnosticTestRoute _fromState(GoRouterState state) =>
      const DiagnosticTestRoute();

  @override
  String get location => GoRouteData.$location('/diagnostic-test');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $diagnosticTestResultRoute => GoRouteData.$route(
  path: '/diagnostic-test-result',
  name: 'diagnostictestresultscreenroute',

  factory: _$DiagnosticTestResultRoute._fromState,
);

mixin _$DiagnosticTestResultRoute on GoRouteData {
  static DiagnosticTestResultRoute _fromState(GoRouterState state) =>
      const DiagnosticTestResultRoute();

  @override
  String get location => GoRouteData.$location('/diagnostic-test-result');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $diagnosticTestCongratulationRoute => GoRouteData.$route(
  path: '/diagnostic-test-congratulation',
  name: 'diagnostictestcongratulationscreenroute',

  factory: _$DiagnosticTestCongratulationRoute._fromState,
);

mixin _$DiagnosticTestCongratulationRoute on GoRouteData {
  static DiagnosticTestCongratulationRoute _fromState(GoRouterState state) =>
      const DiagnosticTestCongratulationRoute();

  @override
  String get location =>
      GoRouteData.$location('/diagnostic-test-congratulation');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $diagnosticTestOnboardRoute => GoRouteData.$route(
  path: '/diagnostic-test-onboarding',
  name: 'diagnostictestonboardscreenroute',

  factory: _$DiagnosticTestOnboardRoute._fromState,
);

mixin _$DiagnosticTestOnboardRoute on GoRouteData {
  static DiagnosticTestOnboardRoute _fromState(GoRouterState state) =>
      const DiagnosticTestOnboardRoute();

  @override
  String get location => GoRouteData.$location('/diagnostic-test-onboarding');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $languageRoute => GoRouteData.$route(
  path: '/language/:origin',
  name: 'languagescreenroute',

  factory: _$LanguageRoute._fromState,
);

mixin _$LanguageRoute on GoRouteData {
  static LanguageRoute _fromState(GoRouterState state) =>
      LanguageRoute(origin: state.pathParameters['origin']!);

  LanguageRoute get _self => this as LanguageRoute;

  @override
  String get location =>
      GoRouteData.$location('/language/${Uri.encodeComponent(_self.origin)}');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $selectedLanguageRoute => GoRouteData.$route(
  path: '/language-selected',
  name: 'selectedlanguagescreenroute',

  factory: _$SelectedLanguageRoute._fromState,
);

mixin _$SelectedLanguageRoute on GoRouteData {
  static SelectedLanguageRoute _fromState(GoRouterState state) =>
      const SelectedLanguageRoute();

  @override
  String get location => GoRouteData.$location('/language-selected');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $profileSettingRoute => GoRouteData.$route(
  path: '/profile-settings',
  name: 'profilesettingscreenroute',

  factory: _$ProfileSettingRoute._fromState,
);

mixin _$ProfileSettingRoute on GoRouteData {
  static ProfileSettingRoute _fromState(GoRouterState state) =>
      const ProfileSettingRoute();

  @override
  String get location => GoRouteData.$location('/profile-settings');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $certificateListRoute => GoRouteData.$route(
  path: '/certificate-list',
  name: 'certificatelistscreenroute',

  factory: _$CertificateListRoute._fromState,
);

mixin _$CertificateListRoute on GoRouteData {
  static CertificateListRoute _fromState(GoRouterState state) =>
      const CertificateListRoute();

  @override
  String get location => GoRouteData.$location('/certificate-list');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $certificateRoute => GoRouteData.$route(
  path: '/certificate/:level',
  name: 'certificatescreenroute',

  factory: _$CertificateRoute._fromState,
);

mixin _$CertificateRoute on GoRouteData {
  static CertificateRoute _fromState(GoRouterState state) =>
      CertificateRoute(state.pathParameters['level']!);

  CertificateRoute get _self => this as CertificateRoute;

  @override
  String get location =>
      GoRouteData.$location('/certificate/${Uri.encodeComponent(_self.level)}');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $certificateDetailRoute => GoRouteData.$route(
  path: '/certificate-detail/:level',
  name: 'certificatedetailscreenroute',

  factory: _$CertificateDetailRoute._fromState,
);

mixin _$CertificateDetailRoute on GoRouteData {
  static CertificateDetailRoute _fromState(GoRouterState state) =>
      CertificateDetailRoute(state.pathParameters['level']!);

  CertificateDetailRoute get _self => this as CertificateDetailRoute;

  @override
  String get location => GoRouteData.$location(
    '/certificate-detail/${Uri.encodeComponent(_self.level)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $notificationSettingRoute => GoRouteData.$route(
  path: '/notification-settings',
  name: 'notificationsettingscreenroute',

  factory: _$NotificationSettingRoute._fromState,
);

mixin _$NotificationSettingRoute on GoRouteData {
  static NotificationSettingRoute _fromState(GoRouterState state) =>
      const NotificationSettingRoute();

  @override
  String get location => GoRouteData.$location('/notification-settings');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $splashRoute => GoRouteData.$route(
  path: '/splash',
  name: 'splashscreenroute',

  factory: _$SplashRoute._fromState,
);

mixin _$SplashRoute on GoRouteData {
  static SplashRoute _fromState(GoRouterState state) => const SplashRoute();

  @override
  String get location => GoRouteData.$location('/splash');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $onboardingRoute => GoRouteData.$route(
  path: '/onboarding',
  name: 'onboardingscreenroute',

  factory: _$OnboardingRoute._fromState,
);

mixin _$OnboardingRoute on GoRouteData {
  static OnboardingRoute _fromState(GoRouterState state) =>
      const OnboardingRoute();

  @override
  String get location => GoRouteData.$location('/onboarding');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $chapterTitleRoute => GoRouteData.$route(
  path: '/main-lesson/chapter-title/:level/:chapter',
  name: 'chaptertitlescreenroute',

  factory: _$ChapterTitleRoute._fromState,
);

mixin _$ChapterTitleRoute on GoRouteData {
  static ChapterTitleRoute _fromState(GoRouterState state) => ChapterTitleRoute(
    state.pathParameters['level']!,
    state.pathParameters['chapter']!,
  );

  ChapterTitleRoute get _self => this as ChapterTitleRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/chapter-title/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $pronunciationChallengeRoute => GoRouteData.$route(
  path: '/main-lesson/pronunciation/:level/:chapter/:path',
  name: 'pronunciationchallengescreenroute',

  factory: _$PronunciationChallengeRoute._fromState,
);

mixin _$PronunciationChallengeRoute on GoRouteData {
  static PronunciationChallengeRoute _fromState(GoRouterState state) =>
      PronunciationChallengeRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  PronunciationChallengeRoute get _self => this as PronunciationChallengeRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/pronunciation/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $pronunciationChallengeOnboardingRoute => GoRouteData.$route(
  path: '/main-lesson/pronunciation-onboarding/:level/:chapter',
  name: 'pronunciationchallengeonboardingscreenroute',

  factory: _$PronunciationChallengeOnboardingRoute._fromState,
);

mixin _$PronunciationChallengeOnboardingRoute on GoRouteData {
  static PronunciationChallengeOnboardingRoute _fromState(
    GoRouterState state,
  ) => PronunciationChallengeOnboardingRoute(
    state.pathParameters['level']!,
    state.pathParameters['chapter']!,
  );

  PronunciationChallengeOnboardingRoute get _self =>
      this as PronunciationChallengeOnboardingRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/pronunciation-onboarding/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $pronunciationChallengeInstructionRoute => GoRouteData.$route(
  path: '/main-lesson/pronunciation-instruction/:level/:chapter',
  name: 'pronunciationchallengeinstructionscreenroute',

  factory: _$PronunciationChallengeInstructionRoute._fromState,
);

mixin _$PronunciationChallengeInstructionRoute on GoRouteData {
  static PronunciationChallengeInstructionRoute _fromState(
    GoRouterState state,
  ) => PronunciationChallengeInstructionRoute(
    state.pathParameters['level']!,
    state.pathParameters['chapter']!,
  );

  PronunciationChallengeInstructionRoute get _self =>
      this as PronunciationChallengeInstructionRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/pronunciation-instruction/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $pronunciationChallengeContentResultRoute => GoRouteData.$route(
  path: '/main-lesson/pronunciation-content-result/:level/:chapter/:path',
  name: 'pronunciationchallengecontentresultscreenroute',

  factory: _$PronunciationChallengeContentResultRoute._fromState,
);

mixin _$PronunciationChallengeContentResultRoute on GoRouteData {
  static PronunciationChallengeContentResultRoute _fromState(
    GoRouterState state,
  ) => PronunciationChallengeContentResultRoute(
    state.pathParameters['level']!,
    state.pathParameters['chapter']!,
    state.pathParameters['path']!,
  );

  PronunciationChallengeContentResultRoute get _self =>
      this as PronunciationChallengeContentResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/pronunciation-content-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $pronunciationChallengeResultRoute => GoRouteData.$route(
  path: '/main-lesson/pronunciation-result/:level/:chapter/:path',
  name: 'pronunciationchallengeresultscreenroute',

  factory: _$PronunciationChallengeResultRoute._fromState,
);

mixin _$PronunciationChallengeResultRoute on GoRouteData {
  static PronunciationChallengeResultRoute _fromState(GoRouterState state) =>
      PronunciationChallengeResultRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  PronunciationChallengeResultRoute get _self =>
      this as PronunciationChallengeResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/pronunciation-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $videoConversationRoute => GoRouteData.$route(
  path: '/main-lesson/conversation-video/:level/:chapter/:path',
  name: 'conversationvideoscreenroute',

  factory: _$VideoConversationRoute._fromState,
);

mixin _$VideoConversationRoute on GoRouteData {
  static VideoConversationRoute _fromState(GoRouterState state) =>
      VideoConversationRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  VideoConversationRoute get _self => this as VideoConversationRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/conversation-video/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $videoConversationOnboardingRoute => GoRouteData.$route(
  path: '/main-lesson/conversation-video-onboarding/:level/:chapter',
  name: 'conversationvideoonboardingscreenroute',

  factory: _$VideoConversationOnboardingRoute._fromState,
);

mixin _$VideoConversationOnboardingRoute on GoRouteData {
  static VideoConversationOnboardingRoute _fromState(GoRouterState state) =>
      VideoConversationOnboardingRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  VideoConversationOnboardingRoute get _self =>
      this as VideoConversationOnboardingRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/conversation-video-onboarding/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $videoConversationInstructionRoute => GoRouteData.$route(
  path: '/main-lesson/conversation-video-instruction/:level/:chapter',
  name: 'conversationvideoinstructionscreenroute',

  factory: _$VideoConversationInstructionRoute._fromState,
);

mixin _$VideoConversationInstructionRoute on GoRouteData {
  static VideoConversationInstructionRoute _fromState(GoRouterState state) =>
      VideoConversationInstructionRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  VideoConversationInstructionRoute get _self =>
      this as VideoConversationInstructionRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/conversation-video-instruction/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $videoConversationResultRoute => GoRouteData.$route(
  path: '/main-lesson/conversation-video-result/:level/:chapter',
  name: 'conversationvideoresultscreenroute',

  factory: _$VideoConversationResultRoute._fromState,
);

mixin _$VideoConversationResultRoute on GoRouteData {
  static VideoConversationResultRoute _fromState(GoRouterState state) =>
      VideoConversationResultRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  VideoConversationResultRoute get _self =>
      this as VideoConversationResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/conversation-video-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $listeningMasteryRoute => GoRouteData.$route(
  path: '/main-lesson/listening-mastery/:level/:chapter/:path',
  name: 'listeningmasteryscreenroute',

  factory: _$ListeningMasteryRoute._fromState,
);

mixin _$ListeningMasteryRoute on GoRouteData {
  static ListeningMasteryRoute _fromState(GoRouterState state) =>
      ListeningMasteryRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  ListeningMasteryRoute get _self => this as ListeningMasteryRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/listening-mastery/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $listeningMasteryOnboardingRoute => GoRouteData.$route(
  path: '/main-lesson/listening-mastery-onboarding/:level/:chapter',
  name: 'listeningmasteryonboardingscreenroute',

  factory: _$ListeningMasteryOnboardingRoute._fromState,
);

mixin _$ListeningMasteryOnboardingRoute on GoRouteData {
  static ListeningMasteryOnboardingRoute _fromState(GoRouterState state) =>
      ListeningMasteryOnboardingRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  ListeningMasteryOnboardingRoute get _self =>
      this as ListeningMasteryOnboardingRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/listening-mastery-onboarding/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $listeningMasteryInstructionRoute => GoRouteData.$route(
  path: '/main-lesson/listening-mastery-instruction/:level/:chapter',
  name: 'listeningmasteryinstructionscreenroute',

  factory: _$ListeningMasteryInstructionRoute._fromState,
);

mixin _$ListeningMasteryInstructionRoute on GoRouteData {
  static ListeningMasteryInstructionRoute _fromState(GoRouterState state) =>
      ListeningMasteryInstructionRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  ListeningMasteryInstructionRoute get _self =>
      this as ListeningMasteryInstructionRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/listening-mastery-instruction/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $listeningMasteryResultRoute => GoRouteData.$route(
  path: '/main-lesson/listening-result/:level/:chapter/:path',
  name: 'listeningmasteryresultscreenroute',

  factory: _$ListeningMasteryResultRoute._fromState,
);

mixin _$ListeningMasteryResultRoute on GoRouteData {
  static ListeningMasteryResultRoute _fromState(GoRouterState state) =>
      ListeningMasteryResultRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  ListeningMasteryResultRoute get _self => this as ListeningMasteryResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/listening-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaRoute => GoRouteData.$route(
  path: '/main-lesson/speaking-arena/:level/:chapter/:path',
  name: 'speakingarenascreenroute',

  factory: _$SpeakingArenaRoute._fromState,
);

mixin _$SpeakingArenaRoute on GoRouteData {
  static SpeakingArenaRoute _fromState(GoRouterState state) =>
      SpeakingArenaRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
      );

  SpeakingArenaRoute get _self => this as SpeakingArenaRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaStageRoute => GoRouteData.$route(
  path: '/main-lesson/speaking-arena-stage/:level/:chapter/:path/:stage',
  name: 'speakingarenastagescreenroute',

  factory: _$SpeakingArenaStageRoute._fromState,
);

mixin _$SpeakingArenaStageRoute on GoRouteData {
  static SpeakingArenaStageRoute _fromState(GoRouterState state) =>
      SpeakingArenaStageRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
        state.pathParameters['stage']!,
      );

  SpeakingArenaStageRoute get _self => this as SpeakingArenaStageRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena-stage/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}/${Uri.encodeComponent(_self.stage)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaOnboardingRoute => GoRouteData.$route(
  path: '/main-lesson/speaking-arena-onboarding/:level/:chapter',
  name: 'speakingarenaonboardingscreenroute',

  factory: _$SpeakingArenaOnboardingRoute._fromState,
);

mixin _$SpeakingArenaOnboardingRoute on GoRouteData {
  static SpeakingArenaOnboardingRoute _fromState(GoRouterState state) =>
      SpeakingArenaOnboardingRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  SpeakingArenaOnboardingRoute get _self =>
      this as SpeakingArenaOnboardingRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena-onboarding/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaInstructionRoute => GoRouteData.$route(
  path: '/main-lesson/speaking-arena-instruction/:level/:chapter',
  name: 'speakingarenainstructionscreenroute',

  factory: _$SpeakingArenaInstructionRoute._fromState,
);

mixin _$SpeakingArenaInstructionRoute on GoRouteData {
  static SpeakingArenaInstructionRoute _fromState(GoRouterState state) =>
      SpeakingArenaInstructionRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  SpeakingArenaInstructionRoute get _self =>
      this as SpeakingArenaInstructionRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena-instruction/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaContentResultRoute => GoRouteData.$route(
  path:
      '/main-lesson/speaking-arena-content-result/:level/:chapter/:path/:stage',
  name: 'speakingarenacontentresultscreenroute',

  factory: _$SpeakingArenaContentResultRoute._fromState,
);

mixin _$SpeakingArenaContentResultRoute on GoRouteData {
  static SpeakingArenaContentResultRoute _fromState(GoRouterState state) =>
      SpeakingArenaContentResultRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
        state.pathParameters['stage']!,
      );

  SpeakingArenaContentResultRoute get _self =>
      this as SpeakingArenaContentResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena-content-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}/${Uri.encodeComponent(_self.stage)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $speakingArenaResultRoute => GoRouteData.$route(
  path: '/main-lesson/speaking-arena-result/:level/:chapter/:path/:stage',
  name: 'speakingarenaresultscreenroute',

  factory: _$SpeakingArenaResultRoute._fromState,
);

mixin _$SpeakingArenaResultRoute on GoRouteData {
  static SpeakingArenaResultRoute _fromState(GoRouterState state) =>
      SpeakingArenaResultRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
        state.pathParameters['path']!,
        state.pathParameters['stage']!,
      );

  SpeakingArenaResultRoute get _self => this as SpeakingArenaResultRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/speaking-arena-result/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}/${Uri.encodeComponent(_self.path)}/${Uri.encodeComponent(_self.stage)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $certificateNotificationRoute => GoRouteData.$route(
  path: '/main-lesson/certificate-notification/:level',
  name: 'certificatenotificationscreenroute',

  factory: _$CertificateNotificationRoute._fromState,
);

mixin _$CertificateNotificationRoute on GoRouteData {
  static CertificateNotificationRoute _fromState(GoRouterState state) =>
      CertificateNotificationRoute(state.pathParameters['level']!);

  CertificateNotificationRoute get _self =>
      this as CertificateNotificationRoute;

  @override
  String get location => GoRouteData.$location(
    '/main-lesson/certificate-notification/${Uri.encodeComponent(_self.level)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $libraryRoute => GoRouteData.$route(
  path: '/library',
  name: 'libraryscreenroute',

  factory: _$LibraryRoute._fromState,
);

mixin _$LibraryRoute on GoRouteData {
  static LibraryRoute _fromState(GoRouterState state) => const LibraryRoute();

  @override
  String get location => GoRouteData.$location('/library');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $libraryChapterRoute => GoRouteData.$route(
  path: '/library-chapter/:level',
  name: 'librarychapterscreenroute',

  factory: _$LibraryChapterRoute._fromState,
);

mixin _$LibraryChapterRoute on GoRouteData {
  static LibraryChapterRoute _fromState(GoRouterState state) =>
      LibraryChapterRoute(state.pathParameters['level']!);

  LibraryChapterRoute get _self => this as LibraryChapterRoute;

  @override
  String get location => GoRouteData.$location(
    '/library-chapter/${Uri.encodeComponent(_self.level)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $libraryChapterContentRoute => GoRouteData.$route(
  path: '/library-chapter-content/:level/:chapter',
  name: 'librarychaptercontentscreenroute',

  factory: _$LibraryChapterContentRoute._fromState,
);

mixin _$LibraryChapterContentRoute on GoRouteData {
  static LibraryChapterContentRoute _fromState(GoRouterState state) =>
      LibraryChapterContentRoute(
        state.pathParameters['level']!,
        state.pathParameters['chapter']!,
      );

  LibraryChapterContentRoute get _self => this as LibraryChapterContentRoute;

  @override
  String get location => GoRouteData.$location(
    '/library-chapter-content/${Uri.encodeComponent(_self.level)}/${Uri.encodeComponent(_self.chapter)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $observeRecalRoute => GoRouteData.$route(
  path: '/memory-flash/:topic',
  name: 'memoryflashscreenroute',

  factory: _$ObserveRecalRoute._fromState,
);

mixin _$ObserveRecalRoute on GoRouteData {
  static ObserveRecalRoute _fromState(GoRouterState state) =>
      ObserveRecalRoute(state.pathParameters['topic']!);

  ObserveRecalRoute get _self => this as ObserveRecalRoute;

  @override
  String get location => GoRouteData.$location(
    '/memory-flash/${Uri.encodeComponent(_self.topic)}',
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $topicGameRoute => GoRouteData.$route(
  path: '/topic-game/:game',
  name: 'topicgamescreenroute',

  factory: _$TopicGameRoute._fromState,
);

mixin _$TopicGameRoute on GoRouteData {
  static TopicGameRoute _fromState(GoRouterState state) =>
      TopicGameRoute(state.pathParameters['game']!);

  TopicGameRoute get _self => this as TopicGameRoute;

  @override
  String get location =>
      GoRouteData.$location('/topic-game/${Uri.encodeComponent(_self.game)}');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $searchRoute => GoRouteData.$route(
  path: '/search',
  name: 'searchscreenroute',

  factory: _$SearchRoute._fromState,
);

mixin _$SearchRoute on GoRouteData {
  static SearchRoute _fromState(GoRouterState state) => const SearchRoute();

  @override
  String get location => GoRouteData.$location('/search');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
