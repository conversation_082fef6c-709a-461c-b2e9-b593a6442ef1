import '../../domain/entities/certificate_level.dart';

class CertificateLevelModel extends CertificateLevel {
  CertificateLevelModel({
    required super.id,
    required super.name,
    required super.description,
  });

  factory CertificateLevelModel.fromJson(Map<String, dynamic> json) {
    return CertificateLevelModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
    );
  }
}
