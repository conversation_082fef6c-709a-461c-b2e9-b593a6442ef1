import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:screenshot/screenshot.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/listening_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/screenshot_share_button.dart';

import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class ListeningMasteryResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const ListeningMasteryResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ListeningMasteryResultScreen> createState() =>
      _ListeningMasteryResultScreenState();
}

class _ListeningMasteryResultScreenState
    extends ConsumerState<ListeningMasteryResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<ListeningState> viewState;
  late ListeningController viewModel;
  late final AudioPlayer _bgmPlayer = AudioPlayer();
  // Create a screenshot controller
  final ScreenshotController _screenshotController = ScreenshotController();

  late AnimationController _scoreAnimationController;
  late Animation<int> _scoreAnimation;
  bool _animationInitialized = false;

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    if (_animationInitialized) {
      _scoreAnimationController.dispose();
    }
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = ListeningControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final totalCorrect = viewModel.calculateTotalCorrectPart();
    final totalWrong =
        viewState.value!.currentListenings.questions.length - totalCorrect;
    final score =
        (totalCorrect /
                viewState.value!.currentListenings.questions.length *
                100)
            .round();

    if (!_animationInitialized && viewState.hasValue) {
      _scoreAnimationController = AnimationController(
        duration: const Duration(milliseconds: 1800),
        vsync: this,
      );
      _scoreAnimation = IntTween(begin: 0, end: score).animate(
        CurvedAnimation(
          parent: _scoreAnimationController,
          curve: Curves.easeOutCubic,
        ),
      );
      _scoreAnimationController.forward();
      _animationInitialized = true;
    }

    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            if (_animationInitialized)
              Column(
                children: [
                  SizedBox(height: 98),
                  Text(
                    '${context.loc.chapter} ${widget.chapter}',
                    style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      color: Color(0xff680007),
                    ),
                  ),
                  SizedBox(height: 32),
                  AnimatedBuilder(
                    animation: _scoreAnimation,
                    builder: (context, child) {
                      return Text(
                        '${_scoreAnimation.value}',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineLarge!.copyWith(
                          fontSize: 70,
                          color:
                              _scoreAnimation.value > 85
                                  ? Color(0xff36AA34)
                                  : _scoreAnimation.value > 60
                                  ? Color(0xffF5BE48)
                                  : Color(0xff93000F),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: 8),
                  AnimatedBuilder(
                    animation: _scoreAnimation,
                    builder: (context, child) {
                      final animatedDesc =
                          _scoreAnimation.value > 85
                              ? context.loc.lm_100_desc
                              : _scoreAnimation.value > 60
                              ? context.loc.lm_50_desc
                              : context.loc.lm_0_desc;
                      return Text(
                        animatedDesc,
                        style: Theme.of(context).textTheme.headlineSmall,
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                  SizedBox(height: 24),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: _CorrectWrongItem(
                            title: context.loc.correct_emot,
                            count: totalCorrect,
                            color: const Color(0xff36AA34),
                          ),
                        ),
                        Expanded(
                          child: _CorrectWrongItem(
                            title: context.loc.incorrect_emot,
                            count: totalWrong,
                            color: const Color(0xff93000F),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            else
              const Center(child: CircularProgressIndicator()),
            // const SizedBox(height: 66), // Removed as padding is handled inside SingleChildScrollView
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VButtonGradient(
                      title: context.loc.next,
                      onTap: () => _handleNextButton(),
                      isBorder: false,
                    ),
                    SizedBox(height: 16),
                    ScreenshotShareButton(
                      title: 'Chapter ${widget.chapter} Listening Mastery',
                      screenshotController: _screenshotController,
                    ),
                  ],
                ),
              ),
            ),
            if (viewState.hasValue && viewState.value!.nextSection)
              _buildNextSectionDialog(),
          ],
        ),
      ),
    );
  }

  Widget _buildNextSectionDialog() {
    return VDialogAlert(
      title: context.loc.nextSection,
      child: Column(
        children: [
          VButtonGradient(
            title: context.loc.yes,
            fontStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            onTap: () async {
              // First, reset the nextSection state to hide the dialog
              viewModel.resetNextSectionState();
              // Wait a frame to ensure the dialog is removed from the widget tree
              await Future.delayed(const Duration(milliseconds: 100));
              // Then proceed with navigation
              if (mounted) {
                viewModel.markSectionAsCompleted();
                // Navigate to speaking arena only when we've completed all listening content
                customNav(
                  context,
                  RouterName.speakingArenaOnboarding,
                  isReplace: true,
                  params: {'level': widget.level, 'chapter': widget.chapter},
                );
              }
            },
          ),
          const SizedBox(height: 24),
          VButtonGradient(
            title: context.loc.no,
            fontStyle: Theme.of(context).textTheme.bodyLarge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.transparent,
              border: Border.all(color: const Color(0xff802115), width: 0.6),
            ),
            onTap: () => context.pop(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleNextButton() async {
    await viewModel.nextPart(context);
    if (viewState.value!.nextSection) {
      viewModel.markSectionAsCompleted();
    } else {}
  }
}

class _CorrectWrongItem extends StatefulWidget {
  const _CorrectWrongItem({
    required this.title,
    required this.count,
    required this.color,
  });

  final String title;
  final int count;
  final Color color;

  @override
  State<_CorrectWrongItem> createState() => _CorrectWrongItemState();
}

class _CorrectWrongItemState extends State<_CorrectWrongItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(
        milliseconds: ((1800 * widget.count / 5).round()).clamp(500, 1800),
      ),
      vsync: this,
    );
    _animation = IntTween(
      begin: 0,
      end: widget.count,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .4),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(width: 0.6, color: widget.color),
      ),
      child: Column(
        children: [
          Text(
            widget.title,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: const Color(0xffB4A9A7),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Text(
                '${_animation.value}',
                style: Theme.of(
                  context,
                ).textTheme.headlineLarge!.copyWith(color: widget.color),
                textAlign: TextAlign.center,
              );
            },
          ),
        ],
      ),
    );
  }
}
