import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/games/presentation/providers/game_completion_controller.dart';

void main() {
  group('GameCompletionController', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('initial state should be false', () {
      final controller = container.read(gameCompletionControllerProvider);
      expect(controller, false);
    });

    test('markGameCompleted should set state to true', () {
      final notifier = container.read(
        gameCompletionControllerProvider.notifier,
      );

      notifier.markGameCompleted();

      final state = container.read(gameCompletionControllerProvider);
      expect(state, true);
    });

    test(
      'checkAndResetCompletion should return true and reset state when completed',
      () {
        final notifier = container.read(
          gameCompletionControllerProvider.notifier,
        );

        // Mark as completed
        notifier.markGameCompleted();
        expect(container.read(gameCompletionControllerProvider), true);

        // Check and reset
        final wasCompleted = notifier.checkAndResetCompletion();

        expect(wasCompleted, true);
        expect(container.read(gameCompletionControllerProvider), false);
      },
    );

    test('checkAndResetCompletion should return false when not completed', () {
      final notifier = container.read(
        gameCompletionControllerProvider.notifier,
      );

      final wasCompleted = notifier.checkAndResetCompletion();

      expect(wasCompleted, false);
      expect(container.read(gameCompletionControllerProvider), false);
    });

    test('reset should set state to false', () {
      final notifier = container.read(
        gameCompletionControllerProvider.notifier,
      );

      // Mark as completed first
      notifier.markGameCompleted();
      expect(container.read(gameCompletionControllerProvider), true);

      // Reset
      notifier.reset();
      expect(container.read(gameCompletionControllerProvider), false);
    });

    test('listener pattern should work correctly', () {
      final notifier = container.read(
        gameCompletionControllerProvider.notifier,
      );
      bool listenerCalled = false;

      // Listen to changes
      container.listen(gameCompletionControllerProvider, (previous, next) {
        if (next == true) {
          listenerCalled = true;
        }
      });

      // Mark as completed - should trigger listener
      notifier.markGameCompleted();

      expect(listenerCalled, true);
      expect(container.read(gameCompletionControllerProvider), true);
    });
  });
}
