# Update Priority System Guide

## Overview

The in-app update service uses a **custom priority system** (0-5) to determine how updates should be handled. Since Google Play Console doesn't provide priority levels through the API, we implement our own logic based on version information.

## Priority Levels

| Priority | Level | Update Type | Description | User Experience |
|----------|-------|-------------|-------------|-----------------|
| **5** | Critical | Immediate | Emergency fixes, critical security patches | **Forced update** - App blocks until updated |
| **4** | High | Immediate | Security updates, urgent bug fixes | **Forced update** - App blocks until updated |
| **3** | Medium | Flexible | Important features, major bug fixes | **Flexible update** - Downloads in background |
| **2** | Low | Flexible | Regular updates, minor improvements | **Flexible update** - Downloads in background |
| **1** | Very Low | Flexible | Beta features, experimental changes | **Flexible update** - Downloads in background |
| **0** | Normal | Flexible | Default priority for regular releases | **Flexible update** - Downloads in background |

## How Priority is Determined

The priority is calculated in `_calculateUpdatePriority()` method using two approaches:

### Method 1: Version Name Keywords

The system checks for specific keywords in the version name/description:

```dart
// Priority 5 (Critical) - Forces immediate update
if (versionName.contains('critical') || versionName.contains('emergency')) {
  return 5;
}

// Priority 4 (High) - Forces immediate update  
if (versionName.contains('security') || versionName.contains('urgent')) {
  return 4;
}

// Priority 3 (Medium) - Flexible update
if (versionName.contains('hotfix') || versionName.contains('important')) {
  return 3;
}

// Priority 1 (Very Low) - Flexible update
if (versionName.contains('beta') || versionName.contains('alpha')) {
  return 1;
}
```

### Method 2: Version Code Differences

Large version code jumps indicate major updates:

```dart
final versionDifference = availableVersionCode - currentVersionCode;

if (versionDifference >= 100) {
  return 3; // Major update - Medium priority
} else if (versionDifference >= 10) {
  return 2; // Minor update - Low priority
}
```

## How to Set Update Priority

### Option 1: Version Naming Convention (Recommended)

When releasing updates in Google Play Console, include priority keywords in your version name or release notes:

**Examples:**
- `v1.2.3-critical` → Priority 5 (Critical)
- `v1.2.4-security` → Priority 4 (High)
- `v1.2.5-hotfix` → Priority 3 (Medium)
- `v1.2.6-beta` → Priority 1 (Very Low)
- `v1.2.7` → Priority 2 (Default)

### Option 2: Version Code Strategy

Use version code patterns to indicate priority:

**Examples:**
- Version codes ending in 99: Critical (e.g., 10099, 10199)
- Version codes ending in 90-98: High priority
- Regular incremental codes: Normal priority

### Option 3: Custom Logic (Advanced)

Modify the `_calculateUpdatePriority()` method to implement your own logic:

```dart
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  // Your custom logic here
  
  // Example: Time-based priority
  final releaseDate = DateTime.now(); // Get from your backend
  final daysSinceRelease = DateTime.now().difference(releaseDate).inDays;
  
  if (daysSinceRelease < 1) {
    return 4; // New releases are high priority for first day
  }
  
  // Example: Feature-based priority
  if (updateInfo.availableVersionCode.toString().contains('2024')) {
    return 3; // Year-based versioning
  }
  
  return 2; // Default
}
```

## Testing Priority Levels

### 1. Local Testing

Use the example screen to test different priority scenarios:

```dart
// In your test environment, you can override the priority
class TestInAppUpdateService extends InAppUpdateServiceRepository {
  @override
  int get updatePriority => 5; // Force critical priority for testing
}
```

### 2. Play Store Testing

1. **Upload to Internal Testing** with different version names
2. **Test Priority Keywords**:
   - Upload `v1.0.1-critical` → Should show immediate update dialog
   - Upload `v1.0.2-beta` → Should show flexible update banner
3. **Monitor Logs** to see calculated priority levels

### 3. Version Code Testing

1. **Large Version Jump**: Upload version with +100 version code difference
2. **Small Version Jump**: Upload version with +1 version code difference
3. **Check Priority Calculation** in debug logs

## Customizing Priority Logic

### Edit the Priority Calculation

Modify `/lib/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart`:

```dart
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  // Add your custom logic here
  
  // Example: App-specific keywords
  final versionName = updateInfo.availableVersionCode.toString();
  
  if (versionName.contains('yourapp-critical')) {
    return 5;
  }
  
  // Example: Backend-driven priority
  // You could fetch priority from your API based on version
  final priority = await _fetchPriorityFromBackend(updateInfo.availableVersionCode);
  return priority;
  
  // Example: Date-based priority
  final now = DateTime.now();
  if (now.weekday == DateTime.friday) {
    return 1; // Lower priority on Fridays
  }
  
  return 2; // Default
}
```

### Add Remote Configuration

For dynamic priority control, integrate with Firebase Remote Config:

```dart
// Add to pubspec.yaml
firebase_remote_config: ^4.3.8

// In your priority calculation
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  final remoteConfig = FirebaseRemoteConfig.instance;
  final priorityOverride = remoteConfig.getInt('update_priority_override');
  
  if (priorityOverride > 0) {
    return priorityOverride;
  }
  
  // Fall back to version-based logic
  return _calculateVersionBasedPriority(updateInfo);
}
```

## Best Practices

### 1. **Use Critical Priority Sparingly**
- Only for security vulnerabilities or app-breaking bugs
- Users cannot skip these updates

### 2. **Test Before Release**
- Always test priority logic with internal testing
- Verify user experience for each priority level

### 3. **Clear Version Naming**
- Use consistent keywords across your team
- Document your versioning strategy

### 4. **Monitor Update Adoption**
- Track update rates in Google Play Console
- Adjust priority strategy based on user behavior

### 5. **Gradual Rollout**
- Use Play Console's staged rollout for high-priority updates
- Monitor for issues before full deployment

## Debugging Priority Issues

### Check Debug Logs

Look for these log messages:

```
📋 Update priority: 3
📱 Current version code: 1001
🔍 Available version code: 1002
📊 Version difference: 1
```

### Common Issues

1. **Priority Always 0**: Check if keywords are in version name
2. **Wrong Priority**: Verify keyword matching logic
3. **Version Code Issues**: Ensure `package_info_plus` is working

### Debug Commands

```dart
// Add temporary debug logging
debugPrint('🔍 Version name: ${updateInfo.availableVersionCode}');
debugPrint('📊 Calculated priority: ${_calculateUpdatePriority(updateInfo)}');
debugPrint('🎯 Should force update: ${isHighPriorityUpdate}');
```

## Example Implementation

Here's a complete example of how to implement a custom priority system:

```dart
// Custom priority rules for your app
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  final versionCode = updateInfo.availableVersionCode;
  final currentVersion = _getCurrentVersionCode();
  
  // Rule 1: Emergency releases (version codes ending in 999)
  if (versionCode.toString().endsWith('999')) {
    return 5; // Critical
  }
  
  // Rule 2: Security releases (version codes ending in 900-998)
  final lastTwoDigits = versionCode % 100;
  if (lastTwoDigits >= 90 && lastTwoDigits <= 98) {
    return 4; // High
  }
  
  // Rule 3: Major version jumps
  final majorVersionCurrent = currentVersion ~/ 1000;
  final majorVersionAvailable = versionCode ~/ 1000;
  if (majorVersionAvailable > majorVersionCurrent) {
    return 3; // Medium
  }
  
  // Rule 4: Beta releases (odd version codes)
  if (versionCode % 2 == 1) {
    return 1; // Very Low
  }
  
  // Default: Regular updates
  return 2; // Low
}
```

This system gives you full control over update priorities while maintaining a good user experience! 🚀