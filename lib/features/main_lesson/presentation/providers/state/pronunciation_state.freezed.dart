// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pronunciation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PronunciationState {

 List<ContentIndexData> get paths; ContentIndexData? get currentPath; PronunciationSubPart? get data; int get currentPage; AudioPath? get audioPath; PronunciationResultFormatted? get response; int get selectedIndex; bool get nextSection; bool get isLoading; bool get isNewSubpart; bool get isIntro; bool get isRecording; PronunciationAgregateScore? get agregateScore;
/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationStateCopyWith<PronunciationState> get copyWith => _$PronunciationStateCopyWithImpl<PronunciationState>(this as PronunciationState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationState&&const DeepCollectionEquality().equals(other.paths, paths)&&(identical(other.currentPath, currentPath) || other.currentPath == currentPath)&&(identical(other.data, data) || other.data == data)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&(identical(other.response, response) || other.response == response)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isNewSubpart, isNewSubpart) || other.isNewSubpart == isNewSubpart)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro)&&(identical(other.isRecording, isRecording) || other.isRecording == isRecording)&&(identical(other.agregateScore, agregateScore) || other.agregateScore == agregateScore));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(paths),currentPath,data,currentPage,audioPath,response,selectedIndex,nextSection,isLoading,isNewSubpart,isIntro,isRecording,agregateScore);

@override
String toString() {
  return 'PronunciationState(paths: $paths, currentPath: $currentPath, data: $data, currentPage: $currentPage, audioPath: $audioPath, response: $response, selectedIndex: $selectedIndex, nextSection: $nextSection, isLoading: $isLoading, isNewSubpart: $isNewSubpart, isIntro: $isIntro, isRecording: $isRecording, agregateScore: $agregateScore)';
}


}

/// @nodoc
abstract mixin class $PronunciationStateCopyWith<$Res>  {
  factory $PronunciationStateCopyWith(PronunciationState value, $Res Function(PronunciationState) _then) = _$PronunciationStateCopyWithImpl;
@useResult
$Res call({
 List<ContentIndexData> paths, ContentIndexData? currentPath, PronunciationSubPart? data, int currentPage, AudioPath? audioPath, PronunciationResultFormatted? response, int selectedIndex, bool nextSection, bool isLoading, bool isNewSubpart, bool isIntro, bool isRecording, PronunciationAgregateScore? agregateScore
});


$ContentIndexDataCopyWith<$Res>? get currentPath;$PronunciationSubPartCopyWith<$Res>? get data;$AudioPathCopyWith<$Res>? get audioPath;$PronunciationResultFormattedCopyWith<$Res>? get response;$PronunciationAgregateScoreCopyWith<$Res>? get agregateScore;

}
/// @nodoc
class _$PronunciationStateCopyWithImpl<$Res>
    implements $PronunciationStateCopyWith<$Res> {
  _$PronunciationStateCopyWithImpl(this._self, this._then);

  final PronunciationState _self;
  final $Res Function(PronunciationState) _then;

/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paths = null,Object? currentPath = freezed,Object? data = freezed,Object? currentPage = null,Object? audioPath = freezed,Object? response = freezed,Object? selectedIndex = null,Object? nextSection = null,Object? isLoading = null,Object? isNewSubpart = null,Object? isIntro = null,Object? isRecording = null,Object? agregateScore = freezed,}) {
  return _then(_self.copyWith(
paths: null == paths ? _self.paths : paths // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>,currentPath: freezed == currentPath ? _self.currentPath : currentPath // ignore: cast_nullable_to_non_nullable
as ContentIndexData?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as PronunciationSubPart?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as PronunciationResultFormatted?,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isNewSubpart: null == isNewSubpart ? _self.isNewSubpart : isNewSubpart // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,isRecording: null == isRecording ? _self.isRecording : isRecording // ignore: cast_nullable_to_non_nullable
as bool,agregateScore: freezed == agregateScore ? _self.agregateScore : agregateScore // ignore: cast_nullable_to_non_nullable
as PronunciationAgregateScore?,
  ));
}
/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentIndexDataCopyWith<$Res>? get currentPath {
    if (_self.currentPath == null) {
    return null;
  }

  return $ContentIndexDataCopyWith<$Res>(_self.currentPath!, (value) {
    return _then(_self.copyWith(currentPath: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationSubPartCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $PronunciationSubPartCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationResultFormattedCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $PronunciationResultFormattedCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAgregateScoreCopyWith<$Res>? get agregateScore {
    if (_self.agregateScore == null) {
    return null;
  }

  return $PronunciationAgregateScoreCopyWith<$Res>(_self.agregateScore!, (value) {
    return _then(_self.copyWith(agregateScore: value));
  });
}
}


/// Adds pattern-matching-related methods to [PronunciationState].
extension PronunciationStatePatterns on PronunciationState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationState value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationState value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ContentIndexData> paths,  ContentIndexData? currentPath,  PronunciationSubPart? data,  int currentPage,  AudioPath? audioPath,  PronunciationResultFormatted? response,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isNewSubpart,  bool isIntro,  bool isRecording,  PronunciationAgregateScore? agregateScore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationState() when $default != null:
return $default(_that.paths,_that.currentPath,_that.data,_that.currentPage,_that.audioPath,_that.response,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isNewSubpart,_that.isIntro,_that.isRecording,_that.agregateScore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ContentIndexData> paths,  ContentIndexData? currentPath,  PronunciationSubPart? data,  int currentPage,  AudioPath? audioPath,  PronunciationResultFormatted? response,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isNewSubpart,  bool isIntro,  bool isRecording,  PronunciationAgregateScore? agregateScore)  $default,) {final _that = this;
switch (_that) {
case _PronunciationState():
return $default(_that.paths,_that.currentPath,_that.data,_that.currentPage,_that.audioPath,_that.response,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isNewSubpart,_that.isIntro,_that.isRecording,_that.agregateScore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ContentIndexData> paths,  ContentIndexData? currentPath,  PronunciationSubPart? data,  int currentPage,  AudioPath? audioPath,  PronunciationResultFormatted? response,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isNewSubpart,  bool isIntro,  bool isRecording,  PronunciationAgregateScore? agregateScore)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationState() when $default != null:
return $default(_that.paths,_that.currentPath,_that.data,_that.currentPage,_that.audioPath,_that.response,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isNewSubpart,_that.isIntro,_that.isRecording,_that.agregateScore);case _:
  return null;

}
}

}

/// @nodoc


class _PronunciationState extends PronunciationState {
   _PronunciationState({final  List<ContentIndexData> paths = const [], this.currentPath, this.data, this.currentPage = 0, this.audioPath, this.response, this.selectedIndex = 0, this.nextSection = false, this.isLoading = false, this.isNewSubpart = true, this.isIntro = false, this.isRecording = false, this.agregateScore}): _paths = paths,super._();
  

 final  List<ContentIndexData> _paths;
@override@JsonKey() List<ContentIndexData> get paths {
  if (_paths is EqualUnmodifiableListView) return _paths;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_paths);
}

@override final  ContentIndexData? currentPath;
@override final  PronunciationSubPart? data;
@override@JsonKey() final  int currentPage;
@override final  AudioPath? audioPath;
@override final  PronunciationResultFormatted? response;
@override@JsonKey() final  int selectedIndex;
@override@JsonKey() final  bool nextSection;
@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isNewSubpart;
@override@JsonKey() final  bool isIntro;
@override@JsonKey() final  bool isRecording;
@override final  PronunciationAgregateScore? agregateScore;

/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationStateCopyWith<_PronunciationState> get copyWith => __$PronunciationStateCopyWithImpl<_PronunciationState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationState&&const DeepCollectionEquality().equals(other._paths, _paths)&&(identical(other.currentPath, currentPath) || other.currentPath == currentPath)&&(identical(other.data, data) || other.data == data)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&(identical(other.response, response) || other.response == response)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isNewSubpart, isNewSubpart) || other.isNewSubpart == isNewSubpart)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro)&&(identical(other.isRecording, isRecording) || other.isRecording == isRecording)&&(identical(other.agregateScore, agregateScore) || other.agregateScore == agregateScore));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_paths),currentPath,data,currentPage,audioPath,response,selectedIndex,nextSection,isLoading,isNewSubpart,isIntro,isRecording,agregateScore);

@override
String toString() {
  return 'PronunciationState(paths: $paths, currentPath: $currentPath, data: $data, currentPage: $currentPage, audioPath: $audioPath, response: $response, selectedIndex: $selectedIndex, nextSection: $nextSection, isLoading: $isLoading, isNewSubpart: $isNewSubpart, isIntro: $isIntro, isRecording: $isRecording, agregateScore: $agregateScore)';
}


}

/// @nodoc
abstract mixin class _$PronunciationStateCopyWith<$Res> implements $PronunciationStateCopyWith<$Res> {
  factory _$PronunciationStateCopyWith(_PronunciationState value, $Res Function(_PronunciationState) _then) = __$PronunciationStateCopyWithImpl;
@override @useResult
$Res call({
 List<ContentIndexData> paths, ContentIndexData? currentPath, PronunciationSubPart? data, int currentPage, AudioPath? audioPath, PronunciationResultFormatted? response, int selectedIndex, bool nextSection, bool isLoading, bool isNewSubpart, bool isIntro, bool isRecording, PronunciationAgregateScore? agregateScore
});


@override $ContentIndexDataCopyWith<$Res>? get currentPath;@override $PronunciationSubPartCopyWith<$Res>? get data;@override $AudioPathCopyWith<$Res>? get audioPath;@override $PronunciationResultFormattedCopyWith<$Res>? get response;@override $PronunciationAgregateScoreCopyWith<$Res>? get agregateScore;

}
/// @nodoc
class __$PronunciationStateCopyWithImpl<$Res>
    implements _$PronunciationStateCopyWith<$Res> {
  __$PronunciationStateCopyWithImpl(this._self, this._then);

  final _PronunciationState _self;
  final $Res Function(_PronunciationState) _then;

/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paths = null,Object? currentPath = freezed,Object? data = freezed,Object? currentPage = null,Object? audioPath = freezed,Object? response = freezed,Object? selectedIndex = null,Object? nextSection = null,Object? isLoading = null,Object? isNewSubpart = null,Object? isIntro = null,Object? isRecording = null,Object? agregateScore = freezed,}) {
  return _then(_PronunciationState(
paths: null == paths ? _self._paths : paths // ignore: cast_nullable_to_non_nullable
as List<ContentIndexData>,currentPath: freezed == currentPath ? _self.currentPath : currentPath // ignore: cast_nullable_to_non_nullable
as ContentIndexData?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as PronunciationSubPart?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as PronunciationResultFormatted?,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isNewSubpart: null == isNewSubpart ? _self.isNewSubpart : isNewSubpart // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,isRecording: null == isRecording ? _self.isRecording : isRecording // ignore: cast_nullable_to_non_nullable
as bool,agregateScore: freezed == agregateScore ? _self.agregateScore : agregateScore // ignore: cast_nullable_to_non_nullable
as PronunciationAgregateScore?,
  ));
}

/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContentIndexDataCopyWith<$Res>? get currentPath {
    if (_self.currentPath == null) {
    return null;
  }

  return $ContentIndexDataCopyWith<$Res>(_self.currentPath!, (value) {
    return _then(_self.copyWith(currentPath: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationSubPartCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $PronunciationSubPartCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationResultFormattedCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $PronunciationResultFormattedCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}/// Create a copy of PronunciationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationAgregateScoreCopyWith<$Res>? get agregateScore {
    if (_self.agregateScore == null) {
    return null;
  }

  return $PronunciationAgregateScoreCopyWith<$Res>(_self.agregateScore!, (value) {
    return _then(_self.copyWith(agregateScore: value));
  });
}
}

// dart format on
