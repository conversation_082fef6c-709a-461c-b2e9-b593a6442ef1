# SelfEng Testing Infrastructure

This directory contains the comprehensive testing infrastructure for the SelfEng Flutter application.

## ✅ **CURRENT STATUS: CENTRALIZED MOCK SYSTEM IMPLEMENTED**

The testing infrastructure has been successfully implemented with a **centralized mock system** that provides consistent, reusable mock objects across all tests. The system includes comprehensive mock factories, setup utilities, and centralized mock classes for all major services.

### **Key Achievements:**
- ✅ **Centralized Mock System**: Unified mock creation through `MockFactories` class
- ✅ **Service Mocks**: Dedicated `service_mocks.dart` for all service layer mocks
- ✅ **Repository Mocks**: Centralized `repository_mocks.dart` for data layer mocks
- ✅ **Firebase Mocks**: Comprehensive `firebase_mocks.dart` with setup utilities
- ✅ **Mock Scenarios**: Pre-configured test scenarios for common use cases
- ✅ **Consistent Testing Patterns**: Standardized approaches across all test files

## 📁 Directory Structure

```
test/
├── README.md                           # This documentation
├── infrastructure_test.dart            # Basic infrastructure verification
├── test_config.dart                    # Test configuration settings
├── widget_test.dart                    # Basic widget test setup
├── helpers/                            # Testing utilities and helpers
│   ├── test_helpers.dart              # General testing utilities
│   ├── test_data_builders.dart        # Builder pattern for test data
│   ├── mock_factories.dart            # Centralized mock object factories
│   ├── riverpod_test_utils.dart       # Riverpod-specific testing utilities
│   └── firebase_test_helpers.dart     # Firebase-specific test helpers
├── mocks/                             # Centralized mock implementations
│   ├── firebase_mocks.dart            # Comprehensive Firebase service mocks
│   ├── service_mocks.dart             # Service layer mock classes
│   ├── repository_mocks.dart          # Repository layer mock classes
│   ├── auth_mocks.dart                # Authentication service mocks
│   ├── http_mocks.dart                # HTTP client mocks
│   └── http_mocks_test.dart           # HTTP mock test utilities
├── examples/                          # Example test implementations
│   └── comprehensive_test_example.dart # Complete testing example
├── features/                          # Feature-specific tests
│   ├── authentication/               # Authentication feature tests
│   ├── main_lesson/                  # Main lesson feature tests
│   ├── dashboard/                    # Dashboard feature tests
│   ├── certificate/                  # Certificate feature tests
│   ├── diagnostic_test/              # Diagnostic test feature tests
│   ├── games/                        # Games feature tests
│   └── ...                           # Other feature tests
├── services/                          # Service layer tests
│   ├── fcm_service/                  # FCM service tests
│   ├── firestore_service_service/    # Firestore service tests
│   ├── notification_service/         # Notification service tests
│   ├── user_data_service/            # User data service tests
│   ├── setting_cache_service/        # Setting cache service tests
│   ├── user_cache_service/           # User cache service tests
│   ├── timer_cache_service/          # Timer cache service tests
│   └── in_app_update_service/        # In-app update service tests
└── shared/                           # Shared domain tests
    └── domain/
        └── models/                   # Domain model tests
```

## Platform Scope

This application is designed exclusively for mobile platforms (iOS and Android).
Therefore, all tests are written with the mobile environment in mind. Web and desktop platforms are not supported or tested.

## ✅ Working Infrastructure

### **Successfully Implemented:**
- ✅ **Centralized Mock System**: Unified mock creation and management
- ✅ **Service Layer Mocks** (`service_mocks.dart`): Platform, storage, and device info mocks
- ✅ **Repository Layer Mocks** (`repository_mocks.dart`): Data access layer mock implementations
- ✅ **Firebase Mock Ecosystem** (`firebase_mocks.dart`): Comprehensive Firebase service mocks with setup utilities
- ✅ **Mock Factories** (`mock_factories.dart`): Centralized mock object creation and configuration
- ✅ **Mock Scenarios**: Pre-configured test scenarios for common use cases
- ✅ **Firebase Auth Mock Setup**: Complete authentication flow mocking
- ✅ **Firestore Mock Setup**: Data persistence layer mocking (with sealed class workarounds)
- ✅ **Firebase Storage Mock Setup**: File upload/download mocking
- ✅ **Firebase Messaging Mock Setup**: Push notification mocking
- ✅ **Basic test infrastructure** (`infrastructure_test.dart`)
- ✅ **UserData model testing** with proper JSON serialization
- ✅ **Either pattern testing** with fold operations
- ✅ **Test data builders** with builder pattern
- ✅ **Riverpod testing utilities** for provider testing
- ✅ **Async testing patterns** with conditions
- ✅ **Authentication controller** with dependency injection
- ✅ **Repository abstraction** for Firebase and Google Sign-In services

### **Known Limitations:**
- ⚠️ Firebase sealed classes (CollectionReference, DocumentReference, etc.) cannot be mocked directly
- ⚠️ Some repository interfaces are commented out due to missing files
- ⚠️ Google Sign In mocking has API compatibility issues

## Update: Firestore sealed class mocks removed

As of cloud_firestore v6+, Firestore core types are sealed. We have commented out and removed mocks that implemented these types in `test/mocks/firebase_mocks.dart`:
- CollectionReference, DocumentReference, DocumentSnapshot, Query, QuerySnapshot, QueryDocumentSnapshot, WriteBatch, Transaction.

This prevents errors like: “The class 'Query' shouldn't be extended, mixed in, or implemented because it's sealed.”

### Guidelines for tests that previously mocked Firestore types
1. **Wrap Firestore behind your own interface** and mock that interface in tests.
2. **Mock higher-level repositories/services** instead of Firestore SDK classes.
3. **For integration-like behavior**, prefer the Firebase Emulator Suite or a fake library (e.g., `fake_cloud_firestore`) in dedicated tests.
4. **For streams**, expose `Stream<T>` from your abstraction and use a `StreamController<T>` in tests.

#### Example: Firestore wrapper + unit test (no sealed types mocked)
```dart
// lib/services/firestore_client.dart
abstract class FirestoreClient {
  Future<Map<String, dynamic>?> getUser(String userId);
  Stream<Map<String, dynamic>?> watchUser(String userId);
}

class FirestoreClientImpl implements FirestoreClient {
  FirestoreClientImpl(this._db);
  final FirebaseFirestore _db;

  @override
  Future<Map<String, dynamic>?> getUser(String userId) async {
    final snap = await _db.collection('users').doc(userId).get();
    return snap.data();
  }

  @override
  Stream<Map<String, dynamic>?> watchUser(String userId) {
    return _db
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((s) => s.data());
  }
}

// test example
class MockFirestoreClient extends Mock implements FirestoreClient {}

void main() {
  late MockFirestoreClient mockClient;

  setUp(() {
    mockClient = MockFirestoreClient();
  });

  test('loads user data', () async {
    when(() => mockClient.getUser('u1'))
        .thenAnswer((_) async => {'email': '<EMAIL>'});

    final result = await sut.loadUser('u1', client: mockClient);

    expect(result.email, '<EMAIL>');
    verify(() => mockClient.getUser('u1')).called(1);
  });

  test('streams user data', () async {
    final controller = StreamController<Map<String, dynamic>?>();
    when(() => mockClient.watchUser('u1'))
        .thenAnswer((_) => controller.stream);

    final events = <Map<String, dynamic>?>[];
    final sub = sut.watchUser('u1', client: mockClient).listen(events.add);
    controller.add({'email': '<EMAIL>'});
    await Future<void>.delayed(const Duration(milliseconds: 10));

    expect(events.first?['email'], '<EMAIL>');
    await sub.cancel();
    await controller.close();
  });
}
```

Tip: If your code already uses repository abstractions (recommended), prefer mocking those repositories instead of Firestore directly.

## Test Structure

### Unit Tests
- **Location**: `test/[layer]/[feature]/`
- **Purpose**: Test individual functions, methods, and classes in isolation
- **Naming**: `[class_name]_test.dart`

### Widget Tests
- **Location**: `test/widgets/`
- **Purpose**: Test UI components and user interactions
- **Naming**: `[widget_name]_test.dart`

### Integration Tests
- **Location**: `integration_test/`
- **Purpose**: Test complete user flows and feature interactions
- **Naming**: `[flow_name]_test.dart`

## Testing Utilities

### Centralized Mock System

The testing infrastructure uses a **centralized mock system** that provides consistent, reusable mock objects across all tests. This system is organized into several key components:

#### 1. Service Mocks (`test/mocks/service_mocks.dart`)

Provides mock implementations for service layer components:

```dart
// Platform Service Mock
final mockPlatformService = MockPlatformService();

// Storage Service Mocks
final mockSharedPrefs = MockSharedPrefsService();
final mockStorage = MockStorageService();

// Device Info Mocks
final mockDeviceInfo = MockDeviceInfoPlugin();
final mockAndroidInfo = MockAndroidDeviceInfo();
final mockIosInfo = MockIosDeviceInfo();
```

#### 2. Repository Mocks (`test/mocks/repository_mocks.dart`)

Centralized repository layer mock implementations:

```dart
// Repository Mocks
final mockUserRepository = MockUserRepository();
final mockFCMRepository = MockFCMServiceRepository();
final mockFirestoreRepository = MockFirestoreServiceRepository();
final mockUserDataRepository = MockUserDataServiceRepository();
final mockNotificationRepository = MockNotificationServiceRepository();

// Data Source Mocks
final mockUserDataSource = MockUserDataSource();
final mockSettingDataSource = MockSettingDataSource();
```

#### 3. Firebase Mocks (`test/mocks/firebase_mocks.dart`)

Comprehensive Firebase service mocks with setup utilities:

```dart
// Firebase Service Mocks
final mockFirebaseAuth = MockFirebaseAuth();
final mockFirebaseFirestore = MockFirebaseFirestore();
final mockFirebaseStorage = MockFirebaseStorage();
final mockFirebaseMessaging = MockFirebaseMessaging();
final mockUser = MockUser();

// Setup authenticated user scenario
FirebaseAuthMockSetup.setupAuthenticatedUser(
  mockFirebaseAuth,
  mockUser,
  uid: 'test_user_id',
  email: '<EMAIL>',
);
```

#### 4. Mock Factories (`test/helpers/mock_factories.dart`)

Centralized mock creation and configuration:

```dart
// Create Firebase service mocks
final mockAuth = MockFactories.createMockFirebaseAuth();
final mockFirestore = MockFactories.createMockFirebaseFirestore();
final mockUser = MockFactories.createMockUser();

// Setup common mock behaviors
MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);
MockFactories.setupSharedPreferencesMocks(mockSharedPreferences);

// Create complete test scenarios
MockScenarios.setupAuthenticatedUserScenario(
  mockAuth: mockAuth,
  mockUser: mockUser,
  userId: 'test_user_id',
  email: '<EMAIL>',
);
```

### Test Helpers (`test/helpers/test_helpers.dart`)

Provides common utilities for all tests:

```dart
// Create test widget with providers
TestHelpers.createTestWidget(
  child: MyWidget(),
  overrides: [mockProvider.overrideWithValue(mockValue)],
);

// Pump and settle with timeout
await TestHelpers.pumpAndSettle(tester);

// Wait for conditions
await TestHelpers.waitFor(() => condition);
```

### Test Data Builders (`test/helpers/test_data_builders.dart`)

Use builder pattern for creating test data:

```dart
final userData = TestDataBuilders.userDataBuilder()
    .withEmail('<EMAIL>')
    .withCompletedTest()
    .withAllLastCourses()
    .build();
```

### Riverpod Test Utils (`test/helpers/riverpod_test_utils.dart`)

Specialized utilities for testing Riverpod providers:

```dart
// Test provider initial state
RiverpodTestUtils.testProviderInitialState(
  myProvider,
  expectedInitialState,
);

// Test async notifier state changes
await RiverpodTestUtils.testAsyncNotifierStateChange(
  myAsyncProvider,
  (notifier) => notifier.performAction(),
  AsyncData(expectedResult),
);
```

## Centralized Mock System Guidelines

### **Overview**

The centralized mock system provides a standardized approach to creating and configuring mock objects across all tests. This ensures consistency, reduces duplication, and makes tests more maintainable.

### **Core Principles**

1. **Single Source of Truth**: All mock objects are created through centralized factories
2. **Consistent Configuration**: Mock behaviors are set up using standardized setup methods
3. **Reusable Scenarios**: Common test scenarios are pre-configured for easy reuse
4. **Clear Separation**: Mocks are organized by layer (services, repositories, Firebase)

### **Using Service Mocks**

```dart
import 'package:selfeng/test/mocks/service_mocks.dart';

void main() {
  late MockPlatformService mockPlatformService;
  late MockSharedPrefsService mockSharedPrefsService;

  setUp(() {
    mockPlatformService = MockPlatformService();
    mockSharedPrefsService = MockSharedPrefsService();
  });

  test('should get device info successfully', () async {
    // Arrange
    when(() => mockPlatformService.getDeviceInfo())
        .thenAnswer((_) async => MockAndroidDeviceInfo());

    // Act
    final result = await platformService.getDeviceInfo();

    // Assert
    expect(result, isA<AndroidDeviceInfo>());
    verify(() => mockPlatformService.getDeviceInfo()).called(1);
  });
}
```

### **Using Repository Mocks**

```dart
import 'package:selfeng/test/mocks/repository_mocks.dart';

void main() {
  late MockUserRepository mockUserRepository;
  late MockFirestoreServiceRepository mockFirestoreRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    mockFirestoreRepository = MockFirestoreServiceRepository();
  });

  test('should save user data successfully', () async {
    // Arrange
    final testUserData = TestDataBuilders.userDataBuilder().build();
    when(() => mockUserRepository.saveUserData(testUserData))
        .thenAnswer((_) async => const Right(unit));

    // Act
    final result = await userRepository.saveUserData(testUserData);

    // Assert
    expect(result.isRight(), isTrue);
    verify(() => mockUserRepository.saveUserData(testUserData)).called(1);
  });
}
```

### **Using Firebase Mocks**

```dart
import 'package:selfeng/test/mocks/firebase_mocks.dart';
import 'package:selfeng/test/helpers/mock_factories.dart';

void main() {
  late MockFirebaseAuth mockFirebaseAuth;
  late MockUser mockUser;

  setUp(() {
    mockFirebaseAuth = MockFactories.createMockFirebaseAuth();
    mockUser = MockFactories.createMockUser();

    // Setup authenticated user scenario
    MockFactories.setupFirebaseAuthMocks(mockFirebaseAuth, mockUser);
  });

  test('should authenticate user successfully', () async {
    // Arrange
    FirebaseAuthMockSetup.setupSignInSuccess(
      mockFirebaseAuth,
      MockUserCredential(),
      mockUser,
    );

    // Act
    final result = await authService.signInWithEmailAndPassword(
      email: '<EMAIL>',
      password: 'password',
    );

    // Assert
    expect(result.isRight(), isTrue);
    verify(() => mockFirebaseAuth.signInWithEmailAndPassword(
      email: '<EMAIL>',
      password: 'password',
    )).called(1);
  });
}
```

### **Using Mock Scenarios**

```dart
import 'package:selfeng/test/helpers/mock_factories.dart';

void main() {
  late MockFirebaseAuth mockAuth;
  late MockUser mockUser;
  late MockDio mockDio;

  setUp(() {
    mockAuth = MockFactories.createMockFirebaseAuth();
    mockUser = MockFactories.createMockUser();
    mockDio = MockFactories.createMockDio();

    // Setup complete authenticated user scenario
    MockScenarios.setupAuthenticatedUserScenario(
      mockAuth: mockAuth,
      mockUser: mockUser,
      userId: 'test_user_id',
      email: '<EMAIL>',
    );

    // Setup successful API response scenario
    MockScenarios.setupSuccessfulApiScenario(
      mockDio,
      {'id': '123', 'name': 'Test User'},
    );
  });

  test('should load user profile successfully', () async {
    // All mocks are pre-configured - just test the business logic
    final profile = await userProfileService.loadProfile();

    expect(profile.isRight(), isTrue);
    expect(profile.getOrElse(() => null)?.email, '<EMAIL>');
  });
}
```

### **Best Practices for Centralized Mocks**

#### **1. Import Organization**
```dart
// Good: Import from centralized locations
import 'package:selfeng/test/mocks/service_mocks.dart';
import 'package:selfeng/test/mocks/repository_mocks.dart';
import 'package:selfeng/test/mocks/firebase_mocks.dart';
import 'package:selfeng/test/helpers/mock_factories.dart';

// Avoid: Direct mocktail imports for custom mocks
// import 'package:mocktail/mocktail.dart';
```

#### **2. Setup Pattern**
```dart
void main() {
  late MockFirebaseAuth mockAuth;
  late MockUser mockUser;

  setUp(() {
    // Create mocks through factories
    mockAuth = MockFactories.createMockFirebaseAuth();
    mockUser = MockFactories.createMockUser();

    // Use setup utilities for common configurations
    MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);
  });

  // Tests...
}
```

#### **3. Verification**
```dart
test('should call repository method', () async {
  // Arrange
  when(() => mockRepository.getData())
      .thenAnswer((_) async => const Right('data'));

  // Act
  await service.loadData();

  // Assert - verify interactions
  verify(() => mockRepository.getData()).called(1);
});
```

#### **4. Error Scenarios**
```dart
test('should handle network errors', () async {
  // Arrange
  when(() => mockRepository.getData())
      .thenAnswer((_) async => const Left(AppException.network()));

  // Act
  final result = await service.loadData();

  // Assert
  expect(result.isLeft(), isTrue);
  expect(result.fold((error) => error, (_) => null),
         isA<NetworkException>());
});
```

## Testing Patterns

### 1. Testing Freezed Models

```dart
group('UserData Model Tests', () {
  test('should serialize and deserialize correctly', () {
    final original = TestDataBuilders.userDataBuilder().build();
    final json = original.toJson();
    final deserialized = UserData.fromJson(json);
    
    expect(deserialized, equals(original));
  });
  
  test('should be equal when properties are the same', () {
    final userData1 = TestDataBuilders.userDataBuilder().build();
    final userData2 = TestDataBuilders.userDataBuilder().build();
    
    expect(userData1, equals(userData2));
    expect(userData1.hashCode, equals(userData2.hashCode));
  });
});
```

### 2. Testing Either Pattern

```dart
test('should handle success case with Either', () {
  final result = Either<AppException, String>.right('success');
  
  final output = result.fold(
    (error) => 'Error: ${error.message}',
    (success) => 'Success: $success',
  );
  
  expect(output, equals('Success: success'));
});
```

### 3. Testing Riverpod Controllers with Dependency Injection

```dart
group('AuthController Tests', () {
  late ProviderContainer container;
  late MockUserRepository mockUserRepository;
  late MockAuthRepository mockAuthRepository;
  late MockGoogleSignInRepository mockGoogleSignInRepository;
  
  setUp(() {
    mockUserRepository = MockUserRepository();
    mockAuthRepository = MockAuthRepository();
    mockGoogleSignInRepository = MockGoogleSignInRepository();
    
    container = ProviderContainer(
      overrides: [
        userLocalRepositoryProvider.overrideWithValue(mockUserRepository),
      ],
    );
  });
  
  tearDown(() {
    container.dispose();
  });
  
  test('should create AuthController with test constructor', () {
    final controller = AuthController.test(
      userRepository: mockUserRepository,
      authRepository: mockAuthRepository,
      googleSignInRepository: mockGoogleSignInRepository,
    );
    
    expect(controller, isA<AuthController>());
 });
  
  test('should logout successfully', () async {
    final controller = AuthController.test(
      userRepository: mockUserRepository,
      authRepository: mockAuthRepository,
      googleSignInRepository: mockGoogleSignInRepository,
    );
    
    await controller.logout();
    
    verify(() => mockAuthRepository.signOut()).called(1);
    verify(() => mockGoogleSignInRepository.disconnect()).called(1);
  });
});
```

### 4. Testing Repository Abstractions

```dart
test('should create repository implementations', () {
  final mockFirebaseAuth = MockFirebaseAuth();
  final mockGoogleSignIn = MockGoogleSignIn();
  
  final authRepository = AuthRepositoryImpl(mockFirebaseAuth);
  final googleSignInRepository = GoogleSignInRepositoryImpl(mockGoogleSignIn);
  
  expect(authRepository, isA<AuthRepository>());
  expect(googleSignInRepository, isA<GoogleSignInRepository>());
});
```

### 5. Testing Configuration

```dart
test('should initialize with default config', () {
  final config = AuthConfig();
  expect(config.googleSignInTimeout, const Duration(seconds: 30));
  expect(config.googleSignInRetryDelay, const Duration(milliseconds: 500));
  expect(config.initializationDelay, const Duration(seconds: 3));
  expect(config.rethrowGoogleSignInErrors, isFalse);
  expect(config.rethrowInitializationErrors, isFalse);
  expect(config.rethrowNotificationErrors, isFalse);
});

test('should allow custom config', () {
  final config = AuthConfig(
    googleSignInTimeout: const Duration(seconds: 10),
    googleSignInRetryDelay: const Duration(milliseconds: 100),
    initializationDelay: const Duration(seconds: 1),
    rethrowGoogleSignInErrors: true,
    rethrowInitializationErrors: true,
    rethrowNotificationErrors: true,
  );
  
  expect(config.googleSignInTimeout, const Duration(seconds: 10));
  expect(config.googleSignInRetryDelay, const Duration(milliseconds: 100));
  expect(config.initializationDelay, const Duration(seconds: 1));
  expect(config.rethrowGoogleSignInErrors, isTrue);
  expect(config.rethrowInitializationErrors, isTrue);
  expect(config.rethrowNotificationErrors, isTrue);
});
```

### 6. Testing Widgets with Riverpod

```dart
testWidgets('should display user data when loaded', (tester) async {
  final mockUserData = TestDataBuilders.userDataBuilder().build();
  
  await RiverpodTestUtils.pumpWidgetWithProviders(
    tester,
    child: UserProfileWidget(),
    overrides: [
      userDataProvider.overrideWithValue(mockUserData),
    ],
  );
  
  expect(find.text(mockUserData.email), findsOneWidget);
});
```

### 7. Testing Firebase Services

```dart
group('FirestoreService Tests', () {
  late MockFirebaseFirestore mockFirestore;
  late FirestoreService service;
  
  setUp(() {
    mockFirestore = MockFactories.createMockFirebaseFirestore();
    service = FirestoreService(mockFirestore);
  });
  
  test('should save user data successfully', () async {
    final mockCollection = MockCollectionReference<Map<String, dynamic>>();
    final mockDocument = MockDocumentReference<Map<String, dynamic>>();
    
    FirestoreMockSetup.setupCollection(mockFirestore, mockCollection, 'users');
    FirestoreMockSetup.setupDocument(mockCollection, mockDocument, 'user123');
    FirestoreMockSetup.setupDocumentSet(mockDocument);
    
    final userData = TestDataBuilders.userDataBuilder().build();
    final result = await service.saveUserData('user123', userData);
    
    expect(result.isRight(), isTrue);
    verify(() => mockDocument.set(userData.toJson())).called(1);
  });
});
```

## Running Tests

### Run All Tests
```bash
flutter test
```

### Run Specific Test File
```bash
flutter test test/shared/domain/models/user_data_test.dart
```

### Run Tests with Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### Run Widget Tests Only
```bash
flutter test test/widgets/
```

### Run Integration Tests
```bash
flutter test integration_test/
```

## Coverage Requirements

- **Minimum Overall Coverage**: 80%
- **Critical Components**: 90%
  - Authentication logic
  - Data models
  - Core business logic
 - Error handling
- **UI Components**: 70%
- **Integration Tests**: Cover main user flows

### Checking Coverage
```bash
# Generate coverage report
flutter test --coverage

# View coverage in browser
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## Best Practices

### 1. **Centralized Mock System Usage**
- **Always use centralized mocks**: Import from `test/mocks/` and `test/helpers/mock_factories.dart`
- **Leverage mock scenarios**: Use `MockScenarios` for common test setups
- **Follow setup patterns**: Use standardized setup methods for consistent behavior
- **Import organization**: Keep mock imports organized and avoid direct mocktail imports for custom mocks

### 2. Test Organization
- Group related tests using `group()`
- Use descriptive test names that explain the scenario
- Follow AAA pattern: Arrange, Act, Assert

### 3. Mock Usage
- **Use centralized mock factories**: `MockFactories.createMockFirebaseAuth()` instead of direct instantiation
- **Apply standard setups**: Use `MockFactories.setupFirebaseAuthMocks()` for common configurations
- **Verify mock interactions**: Always verify important method calls
- **Reset mocks between tests**: Use `setUp()` and `tearDown()` appropriately

### 4. Test Data
- Use test data builders for consistent test data
- Create realistic test scenarios
- Test edge cases and error conditions

### 5. Async Testing
- Always await async operations in tests
- Use `pumpAndSettle()` for widget tests with animations
- Test both success and error scenarios for async operations

### 6. Provider Testing
- Test provider initial states
- Test state transitions
- Test error handling
- Test provider dependencies

### 7. Widget Testing
- Test widget rendering
- Test user interactions
- Test different screen sizes when relevant
- Test accessibility features

### 8. **Mock-Specific Best Practices**

#### **Firebase Service Testing**
```dart
// ✅ Good: Use centralized Firebase mocks
final mockAuth = MockFactories.createMockFirebaseAuth();
final mockUser = MockFactories.createMockUser();
MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);

// ❌ Avoid: Direct mocktail usage for Firebase services
// final mockAuth = MockFirebaseAuth(); // Inconsistent setup
```

#### **Repository Layer Testing**
```dart
// ✅ Good: Use repository mocks from centralized location
final mockRepository = MockUserRepository();

// ✅ Good: Use data builders for test data
final testData = TestDataBuilders.userDataBuilder().build();

// ❌ Avoid: Creating mock data manually
// final testData = UserData(id: '1', email: '<EMAIL>', ...);
```

#### **Service Layer Testing**
```dart
// ✅ Good: Use service mocks with proper setup
final mockService = MockPlatformService();
when(() => mockService.getDeviceInfo())
    .thenAnswer((_) async => MockAndroidDeviceInfo());

// ❌ Avoid: Incomplete mock setup
// final mockService = MockPlatformService();
// No setup - will throw UnimplementedError
```

### 9. Performance
- Keep tests fast and focused
- Use `setUp()` and `tearDown()` for common initialization
- Dispose resources properly
- Reuse mock scenarios across similar tests

## Complete Testing Example with Centralized Mocks

Here's a comprehensive example demonstrating how to use the centralized mock system in a real test scenario:

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/test/mocks/service_mocks.dart';
import 'package:selfeng/test/mocks/repository_mocks.dart';
import 'package:selfeng/test/mocks/firebase_mocks.dart';
import 'package:selfeng/test/helpers/mock_factories.dart';
import 'package:selfeng/test/helpers/test_data_builders.dart';
import 'package:selfeng/features/profile/profile_service.dart';

void main() {
  group('ProfileService Tests', () {
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockUserRepository mockUserRepository;
    late MockPlatformService mockPlatformService;
    late ProfileService profileService;

    setUp(() {
      // Create all mocks through centralized factories
      mockFirebaseAuth = MockFactories.createMockFirebaseAuth();
      mockUser = MockFactories.createMockUser();
      mockUserRepository = MockUserRepository();
      mockPlatformService = MockPlatformService();

      // Setup authenticated user scenario
      MockFactories.setupFirebaseAuthMocks(mockFirebaseAuth, mockUser);

      // Setup user repository behavior
      final testUserData = TestDataBuilders.userDataBuilder()
          .withEmail('<EMAIL>')
          .withDisplayName('John Doe')
          .build();

      when(() => mockUserRepository.getUserData('user123'))
          .thenAnswer((_) async => Right(testUserData));

      // Setup platform service
      when(() => mockPlatformService.getDeviceInfo())
          .thenAnswer((_) async => MockAndroidDeviceInfo());

      // Create service with mocked dependencies
      profileService = ProfileService(
        firebaseAuth: mockFirebaseAuth,
        userRepository: mockUserRepository,
        platformService: mockPlatformService,
      );
    });

    test('should load user profile successfully', () async {
      // Act
      final result = await profileService.loadUserProfile('user123');

      // Assert
      expect(result.isRight(), isTrue);
      final profile = result.getOrElse(() => null)!;
      expect(profile.email, '<EMAIL>');
      expect(profile.displayName, 'John Doe');

      // Verify interactions
      verify(() => mockUserRepository.getUserData('user123')).called(1);
      verify(() => mockFirebaseAuth.currentUser).called(1);
    });

    test('should handle repository errors gracefully', () async {
      // Arrange - override setup for error scenario
      when(() => mockUserRepository.getUserData('user123'))
          .thenAnswer((_) async => const Left(AppException.network()));

      // Act
      final result = await profileService.loadUserProfile('user123');

      // Assert
      expect(result.isLeft(), isTrue);
      expect(result.fold((error) => error, (_) => null),
             isA<NetworkException>());
    });

    test('should get device info for profile', () async {
      // Act
      final deviceInfo = await profileService.getDeviceInfo();

      // Assert
      expect(deviceInfo, isA<AndroidDeviceInfo>());
      verify(() => mockPlatformService.getDeviceInfo()).called(1);
    });
  });
}
```

## Common Testing Scenarios

### Testing API Calls
```dart
test('should handle API success response', () async {
  final mockDio = HttpMocks.createMockDio();
  final adapter = HttpMocks.createDioAdapter(mockDio);

  HttpMocks.setupGetSuccess(
    adapter,
    '/api/user',
    {'id': '123', 'name': 'Test User'},
  );

  final service = ApiService(mockDio);
  final result = await service.getUser('123');

  expect(result.isRight(), isTrue);
});
```

### Testing Error Handling
```dart
test('should handle network errors gracefully', () async {
  when(() => mockRepository.getData())
      .thenThrow(AppException(
        message: 'Network error',
        statusCode: 0,
        identifier: 'network_error',
      ));

  final controller = container.read(dataControllerProvider.notifier);
  await controller.loadData();

  final state = container.read(dataControllerProvider);
  expect(state, isA<AsyncError>());
});
```

### Testing User Interactions
```dart
testWidgets('should navigate when button is tapped', (tester) async {
  await tester.pumpWidget(TestHelpers.createTestWidget(
    child: MyScreen(),
  ));

  await tester.tap(find.byKey(Key('navigate_button')));
  await tester.pumpAndSettle();

  expect(find.byType(NextScreen), findsOneWidget);
});
```

## Continuous Integration

Tests are automatically run on:
- Pull requests
- Commits to main branch
- Nightly builds

CI configuration includes:
- Running all test suites
- Generating coverage reports
- Failing builds on coverage drops
- Performance regression testing

For more specific testing examples, refer to the existing test files in each module.
