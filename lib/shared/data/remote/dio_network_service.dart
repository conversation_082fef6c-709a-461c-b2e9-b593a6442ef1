import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:selfeng/main/app_env.dart';
import 'package:selfeng/shared/data/remote/network_service.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/response.dart' as response;
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/mixins/exception_handler_mixin.dart';

class DioNetworkService extends NetworkService with ExceptionHandlerMixin {
  final Dio dio;
  DioNetworkService(this.dio) {
    // this throws error while running test
    if (!kTestMode) {
      dio.options = dioBaseOptions;
      if (kDebugMode) {
        dio.interceptors.add(
          LogInterceptor(requestBody: true, responseBody: true),
        );
      }
    }
  }

  BaseOptions get dioBaseOptions =>
      BaseOptions(baseUrl: baseUrl, headers: headers);
  @override
  // String get baseUrl => AppConfigs.baseUrl;
  String get baseUrl => EnvInfo.appUrl;

  @override
  Map<String, Object> get headers => {
    'accept': 'application/json',
    'content-type': 'application/json',
  };

  @override
  Map<String, dynamic>? updateHeader(Map<String, dynamic> data) {
    final header = {...data, ...headers};
    if (!kTestMode) {
      dio.options.headers = header;
    }
    return header;
  }

  @override
  Future<Either<AppException, response.Response>> post(
    String endpoint, {
    Map<String, dynamic>? data,
  }) {
    final res = handleException(
      () => dio.post(endpoint, data: data),
      endpoint: endpoint,
    );
    res.then(
      (value) => value.fold((p0) => null, (val) {
        if (val.refreshToken != null) {
          Map<String, dynamic> headerTemp = dio.options.headers;
          headerTemp.addAll({"refresh_token": val.refreshToken});
          updateHeader(headerTemp);
        }
      }),
    );
    return res;
  }

  @override
  Future<Either<AppException, response.Response>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) {
    final res = handleException(
      () => dio.get(endpoint, queryParameters: queryParameters),
      endpoint: endpoint,
    );
    res.then(
      (value) => value.fold((p0) => null, (val) {
        if (val.refreshToken != null) {
          Map<String, dynamic> headerTemp = dio.options.headers;
          headerTemp.addAll({"refresh_token": val.refreshToken});
          updateHeader(headerTemp);
        }
      }),
    );
    return res;
  }
}
