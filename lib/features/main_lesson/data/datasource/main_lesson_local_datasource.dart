import 'package:selfeng/shared/data/local/storage_service.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class MainLessonLocalDatasource {
  Future<Either<AppException, bool>> saveIntro({required String lessonName});
  Future<Either<AppException, bool>> isIntro({required String lessonName});
}

class MainLessonLocalDataSourceImpl implements MainLessonLocalDatasource {
  final StorageService localService;

  MainLessonLocalDataSourceImpl(this.localService);

  @override
  Future<Either<AppException, bool>> saveIntro({
    required String lessonName,
  }) async {
    try {
      final existing = await _getIntroLessons();
      if (existing.contains(lessonName)) return const Right(true);
      return await localService
          .set('is_intro_lesson', [...existing, lessonName].join(','))
          .then((_) => const Right(true));
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.loginUser',
        ),
      );
    }
  }

  @override
  Future<Either<AppException, bool>> isIntro({
    required String lessonName,
  }) async {
    try {
      final existing = await _getIntroLessons();
      return Right(existing.contains(lessonName));
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.loginUser',
        ),
      );
    }
  }

  Future<List<String>> _getIntroLessons() async {
    try {
      final data = await localService.get('is_intro_lesson');
      return data?.toString().split(',').where((s) => s.isNotEmpty).toList() ??
          [];
    } catch (_) {
      return [];
    }
  }
}
