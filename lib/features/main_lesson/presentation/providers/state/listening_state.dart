import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

part 'listening_state.freezed.dart';

@freezed
abstract class ListeningState with _$ListeningState {
  factory ListeningState({
    @Default([]) List<ListeningPart> listenings,
    @Default(0) int currentPage,
    @Default(false) bool expandedResult,
    AudioPath? audioPath,
    Map<String, dynamic>? response,
    @Default(null)
    int? expandedQuestionIndex, // Changed from selectedIndex, default to null
    @Default(false) bool nextSection,
    @Default(true) bool isNewPart,
    @Default(false) bool isIntro,
  }) = _ListeningState;

  // Allow custom getters / setters
  const ListeningState._();

  ListeningPart get currentListenings => listenings[currentPage];
  bool get isCurrentQuestionsAnswered => listenings[currentPage].questions
      .every((question) => question.isCorrect != null);
  // get currentOrder => listenings[currentPage].questions[selectedIndex].order;
  // String get currentQuestion =>
  //     listenings[currentPage].questions[selectedIndex].question ?? '';
  // List<Choice> get currentChoices =>
  //     listenings[currentPage].questions[selectedIndex].choices;
  // get currentAnswer => listenings[currentPage].questions[selectedIndex].answer;
  // get currentIsCorrect =>
  //     listenings[currentPage].questions[selectedIndex].isCorrect;
}
