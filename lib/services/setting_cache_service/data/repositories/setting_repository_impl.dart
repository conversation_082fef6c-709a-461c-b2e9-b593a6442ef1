import 'dart:ui';

import 'package:selfeng/services/setting_cache_service/data/datasource/setting_local_datasource.dart';
import 'package:selfeng/services/setting_cache_service/domain/repositories/setting_cache_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class SettingCacheRepositoryImpl extends SettingCacheRepository {
  SettingCacheRepositoryImpl(this.dataSource);

  final SettingDataSource dataSource;

  @override
  Future<bool> deleteLocale() {
    return dataSource.deleteLocale();
  }

  @override
  Future<Either<AppException, Locale>> fetchLocale() {
    return dataSource.fetchLocale();
  }

  @override
  Future<bool> saveLocale({required Locale locale}) {
    return dataSource.saveLocale(locale: locale);
  }

  @override
  Future<bool> hasLocale() {
    return dataSource.hasLocale();
  }

  @override
  Future<Either<AppException, bool>> getAudioEnabled() {
    return dataSource.getAudioEnabled();
  }

  @override
  Future<bool> saveAudioEnabled({required bool enabled}) {
    return dataSource.saveAudioEnabled(enabled: enabled);
  }
}
