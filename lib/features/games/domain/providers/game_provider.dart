import 'package:selfeng/features/games/domain/repositories/game_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final gameRepositoryProvider = Provider<GameRepository>((ref) {
  final FirestoreServiceRepository firestore = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return GameRepositoryImpl(firestore);
});
