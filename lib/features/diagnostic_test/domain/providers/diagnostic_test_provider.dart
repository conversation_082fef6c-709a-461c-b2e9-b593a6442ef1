import 'package:selfeng/features/diagnostic_test/domain/repositories/diagnostic_test_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final diagnosticTestRepositoryProvider = Provider<DiagnosticTestRepository>((
  ref,
) {
  // FirebaseFirestore firestore = FirebaseFirestore.instance;

  final FirestoreServiceRepository firestore = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return DiagnosticTestRepositoryImpl(firestore);
});
