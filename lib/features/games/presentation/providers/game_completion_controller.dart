import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'game_completion_controller.g.dart';

/// A provider to handle game completion state across different game screens
/// This replaces the need to return values through navigation
@Riverpod(keepAlive: true)
class GameCompletionController extends _$GameCompletionController {
  @override
  bool build() {
    return false; // Initially no game is completed
  }

  /// Called by game screens when a game is completed successfully
  /// and the user should advance to the next topic
  void markGameCompleted() {
    state = true;
  }

  /// Called by the topic game screen to check if game was completed
  /// and reset the flag
  bool checkAndResetCompletion() {
    final wasCompleted = state;
    if (wasCompleted) {
      state = false; // Reset the flag
    }
    return wasCompleted;
  }

  /// Reset the completion state
  void reset() {
    state = false;
  }
}
