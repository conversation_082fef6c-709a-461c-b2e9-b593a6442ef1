allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Set root build directory
rootProject.layout.buildDirectory = file('../build')

subprojects {
    // Set subproject build directory using the root build directory's path
    project.layout.buildDirectory = file("${rootProject.layout.buildDirectory.asFile.get()}/${project.name}")
}

subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.layout.buildDirectory
}