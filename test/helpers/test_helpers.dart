import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Test utilities and helper functions for consistent testing across the project
class TestHelpers {
  TestHelpers._();

  /// Creates a test widget wrapped with MaterialApp and ProviderScope
  static Widget createTestWidget({
    required Widget child,
    List<Override>? overrides,
    Locale? locale,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        locale: locale ?? const Locale('en', 'US'),
        home: Scaffold(body: child),
      ),
    );
  }

  /// Creates a test widget with custom theme
  static Widget createTestWidgetWithTheme({
    required Widget child,
    List<Override>? overrides,
    ThemeData? theme,
    ThemeData? darkTheme,
    ThemeMode? themeMode,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        theme: theme,
        darkTheme: darkTheme,
        themeMode: themeMode ?? ThemeMode.light,
        home: Scaffold(body: child),
      ),
    );
  }

  /// Pumps and settles a widget with default timeout
  static Future<void> pumpAndSettle(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(timeout);
  }

  /// Waits for a specific condition to be true
  static Future<void> waitFor(
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    while (!condition() && stopwatch.elapsed < timeout) {
      await Future.delayed(interval);
    }
    if (!condition()) {
      throw TimeoutException(
        'Condition not met within ${timeout.inMilliseconds}ms',
        timeout,
      );
    }
  }

  /// Finds a widget by its key
  static Finder findByKey(String key) => find.byKey(Key(key));

  /// Finds a widget by its type
  static Finder findByType<T extends Widget>() => find.byType(T);

  /// Finds text widget
  static Finder findText(String text) => find.text(text);

  /// Finds icon widget
  static Finder findIcon(IconData icon) => find.byIcon(icon);

  /// Taps a widget and pumps
  static Future<void> tapAndPump(
    WidgetTester tester,
    Finder finder, {
    Duration? pumpDuration,
  }) async {
    await tester.tap(finder);
    if (pumpDuration != null) {
      await tester.pump(pumpDuration);
    } else {
      await tester.pump();
    }
  }

  /// Enters text in a text field and pumps
  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Scrolls until a widget is visible
  static Future<void> scrollUntilVisible(
    WidgetTester tester,
    Finder finder,
    Finder scrollable, {
    double delta = 100.0,
  }) async {
    await tester.scrollUntilVisible(finder, delta, scrollable: scrollable);
  }

  /// Verifies that a widget exists
  static void verifyWidgetExists(Finder finder) {
    expect(finder, findsOneWidget);
  }

  /// Verifies that a widget does not exist
  static void verifyWidgetNotExists(Finder finder) {
    expect(finder, findsNothing);
  }

  /// Verifies that multiple widgets exist
  static void verifyWidgetsExist(Finder finder, int count) {
    expect(finder, findsNWidgets(count));
  }

  /// Verifies text content
  static void verifyText(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// Verifies that text does not exist
  static void verifyTextNotExists(String text) {
    expect(find.text(text), findsNothing);
  }

  /// Creates a mock ProviderContainer for testing
  static ProviderContainer createMockContainer({List<Override>? overrides}) {
    return ProviderContainer(overrides: overrides ?? []);
  }

  /// Disposes a ProviderContainer safely
  static void disposeContainer(ProviderContainer container) {
    container.dispose();
  }

  /// Creates a test-specific BuildContext
  static BuildContext createTestContext() {
    final binding = TestWidgetsFlutterBinding.ensureInitialized();
    return binding.rootElement!;
  }

  /// Waits for async operations to complete
  static Future<void> waitForAsync([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 100));
  }

  /// Waits for a condition to become true
  static Future<bool> waitForCondition(
    Future<bool> Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final completer = Completer<bool>();
    final timer = Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    Timer.periodic(interval, (periodicTimer) async {
      try {
        final result = await condition();
        if (result && !completer.isCompleted) {
          periodicTimer.cancel();
          timer.cancel();
          completer.complete(true);
        }
      } catch (e) {
        if (!completer.isCompleted) {
          periodicTimer.cancel();
          timer.cancel();
          completer.complete(false);
        }
      }
    });

    return completer.future;
  }

  /// Pumps frames until no more frames are scheduled
  static Future<void> pumpUntilNoFrames(WidgetTester tester) async {
    int frameCount = 0;
    const maxFrames = 100;

    while (tester.binding.hasScheduledFrame && frameCount < maxFrames) {
      await tester.pump();
      frameCount++;
    }

    if (frameCount >= maxFrames) {
      throw Exception('Too many frames pumped, possible infinite animation');
    }
  }

  /// Verifies that an exception is thrown
  static void verifyException<T extends Exception>(void Function() callback) {
    expect(callback, throwsA(isA<T>()));
  }

  /// Verifies async exception
  static Future<void> verifyAsyncException<T extends Exception>(
    Future<void> Function() callback,
  ) async {
    expect(callback, throwsA(isA<T>()));
  }

  /// Creates a test environment with common setup
  static Future<void> setupTestEnvironment() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    // Add any global test setup here
  }

  /// Cleans up test environment
  static Future<void> tearDownTestEnvironment() async {
    // Add any global test cleanup here
  }
}

/// Extension methods for WidgetTester to add convenience methods
extension WidgetTesterExtensions on WidgetTester {
  /// Pumps and waits for a specific duration
  Future<void> pumpAndWait([Duration? duration]) async {
    await pump(duration ?? const Duration(milliseconds: 100));
  }

  /// Pumps multiple frames
  Future<void> pumpFrames(int count) async {
    for (int i = 0; i < count; i++) {
      await pump();
    }
  }

  /// Taps and waits
  Future<void> tapAndWait(Finder finder, [Duration? duration]) async {
    await tap(finder);
    await pumpAndWait(duration);
  }

  /// Long press and wait
  Future<void> longPressAndWait(Finder finder, [Duration? duration]) async {
    await longPress(finder);
    await pumpAndWait(duration);
  }

  /// Drags and waits
  Future<void> dragAndWait(
    Finder finder,
    Offset offset, [
    Duration? duration,
  ]) async {
    await drag(finder, offset);
    await pumpAndWait(duration);
  }
}

/// Extension methods for Finder to add convenience methods
extension FinderExtensions on Finder {
  /// Checks if the finder has exactly one widget
  bool get existsOnce => evaluate().length == 1;

  /// Checks if the finder has no widgets
  bool get notExists => evaluate().isEmpty;

  /// Checks if the finder has multiple widgets
  bool get existsMultiple => evaluate().length > 1;

  /// Gets the count of widgets found
  int get count => evaluate().length;
}
