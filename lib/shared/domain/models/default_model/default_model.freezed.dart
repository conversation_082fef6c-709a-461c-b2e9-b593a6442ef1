// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'default_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DefaultModel {

 String get title; String get subtitle; String get image; String get route; bool get isExpanded; Map<String, String>? get params;@JsonKey(includeFromJson: false, includeToJson: false) dynamic get child; dynamic get onTap;
/// Create a copy of DefaultModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DefaultModelCopyWith<DefaultModel> get copyWith => _$DefaultModelCopyWithImpl<DefaultModel>(this as DefaultModel, _$identity);

  /// Serializes this DefaultModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DefaultModel&&(identical(other.title, title) || other.title == title)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.image, image) || other.image == image)&&(identical(other.route, route) || other.route == route)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded)&&const DeepCollectionEquality().equals(other.params, params)&&const DeepCollectionEquality().equals(other.child, child)&&const DeepCollectionEquality().equals(other.onTap, onTap));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,subtitle,image,route,isExpanded,const DeepCollectionEquality().hash(params),const DeepCollectionEquality().hash(child),const DeepCollectionEquality().hash(onTap));

@override
String toString() {
  return 'DefaultModel(title: $title, subtitle: $subtitle, image: $image, route: $route, isExpanded: $isExpanded, params: $params, child: $child, onTap: $onTap)';
}


}

/// @nodoc
abstract mixin class $DefaultModelCopyWith<$Res>  {
  factory $DefaultModelCopyWith(DefaultModel value, $Res Function(DefaultModel) _then) = _$DefaultModelCopyWithImpl;
@useResult
$Res call({
 String title, String subtitle, String image, String route, bool isExpanded, Map<String, String>? params,@JsonKey(includeFromJson: false, includeToJson: false) dynamic child, dynamic onTap
});




}
/// @nodoc
class _$DefaultModelCopyWithImpl<$Res>
    implements $DefaultModelCopyWith<$Res> {
  _$DefaultModelCopyWithImpl(this._self, this._then);

  final DefaultModel _self;
  final $Res Function(DefaultModel) _then;

/// Create a copy of DefaultModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? subtitle = null,Object? image = null,Object? route = null,Object? isExpanded = null,Object? params = freezed,Object? child = freezed,Object? onTap = freezed,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subtitle: null == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,route: null == route ? _self.route : route // ignore: cast_nullable_to_non_nullable
as String,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,params: freezed == params ? _self.params : params // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,child: freezed == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as dynamic,onTap: freezed == onTap ? _self.onTap : onTap // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}

}


/// Adds pattern-matching-related methods to [DefaultModel].
extension DefaultModelPatterns on DefaultModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DefaultModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DefaultModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DefaultModel value)  $default,){
final _that = this;
switch (_that) {
case _DefaultModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DefaultModel value)?  $default,){
final _that = this;
switch (_that) {
case _DefaultModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String title,  String subtitle,  String image,  String route,  bool isExpanded,  Map<String, String>? params, @JsonKey(includeFromJson: false, includeToJson: false)  dynamic child,  dynamic onTap)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DefaultModel() when $default != null:
return $default(_that.title,_that.subtitle,_that.image,_that.route,_that.isExpanded,_that.params,_that.child,_that.onTap);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String title,  String subtitle,  String image,  String route,  bool isExpanded,  Map<String, String>? params, @JsonKey(includeFromJson: false, includeToJson: false)  dynamic child,  dynamic onTap)  $default,) {final _that = this;
switch (_that) {
case _DefaultModel():
return $default(_that.title,_that.subtitle,_that.image,_that.route,_that.isExpanded,_that.params,_that.child,_that.onTap);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String title,  String subtitle,  String image,  String route,  bool isExpanded,  Map<String, String>? params, @JsonKey(includeFromJson: false, includeToJson: false)  dynamic child,  dynamic onTap)?  $default,) {final _that = this;
switch (_that) {
case _DefaultModel() when $default != null:
return $default(_that.title,_that.subtitle,_that.image,_that.route,_that.isExpanded,_that.params,_that.child,_that.onTap);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DefaultModel implements DefaultModel {
   _DefaultModel({this.title = '', this.subtitle = '', this.image = '', this.route = '', this.isExpanded = false, final  Map<String, String>? params, @JsonKey(includeFromJson: false, includeToJson: false) this.child, this.onTap}): _params = params;
  factory _DefaultModel.fromJson(Map<String, dynamic> json) => _$DefaultModelFromJson(json);

@override@JsonKey() final  String title;
@override@JsonKey() final  String subtitle;
@override@JsonKey() final  String image;
@override@JsonKey() final  String route;
@override@JsonKey() final  bool isExpanded;
 final  Map<String, String>? _params;
@override Map<String, String>? get params {
  final value = _params;
  if (value == null) return null;
  if (_params is EqualUnmodifiableMapView) return _params;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(includeFromJson: false, includeToJson: false) final  dynamic child;
@override final  dynamic onTap;

/// Create a copy of DefaultModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DefaultModelCopyWith<_DefaultModel> get copyWith => __$DefaultModelCopyWithImpl<_DefaultModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DefaultModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DefaultModel&&(identical(other.title, title) || other.title == title)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.image, image) || other.image == image)&&(identical(other.route, route) || other.route == route)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded)&&const DeepCollectionEquality().equals(other._params, _params)&&const DeepCollectionEquality().equals(other.child, child)&&const DeepCollectionEquality().equals(other.onTap, onTap));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,subtitle,image,route,isExpanded,const DeepCollectionEquality().hash(_params),const DeepCollectionEquality().hash(child),const DeepCollectionEquality().hash(onTap));

@override
String toString() {
  return 'DefaultModel(title: $title, subtitle: $subtitle, image: $image, route: $route, isExpanded: $isExpanded, params: $params, child: $child, onTap: $onTap)';
}


}

/// @nodoc
abstract mixin class _$DefaultModelCopyWith<$Res> implements $DefaultModelCopyWith<$Res> {
  factory _$DefaultModelCopyWith(_DefaultModel value, $Res Function(_DefaultModel) _then) = __$DefaultModelCopyWithImpl;
@override @useResult
$Res call({
 String title, String subtitle, String image, String route, bool isExpanded, Map<String, String>? params,@JsonKey(includeFromJson: false, includeToJson: false) dynamic child, dynamic onTap
});




}
/// @nodoc
class __$DefaultModelCopyWithImpl<$Res>
    implements _$DefaultModelCopyWith<$Res> {
  __$DefaultModelCopyWithImpl(this._self, this._then);

  final _DefaultModel _self;
  final $Res Function(_DefaultModel) _then;

/// Create a copy of DefaultModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? subtitle = null,Object? image = null,Object? route = null,Object? isExpanded = null,Object? params = freezed,Object? child = freezed,Object? onTap = freezed,}) {
  return _then(_DefaultModel(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subtitle: null == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,route: null == route ? _self.route : route // ignore: cast_nullable_to_non_nullable
as String,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,params: freezed == params ? _self._params : params // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,child: freezed == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as dynamic,onTap: freezed == onTap ? _self.onTap : onTap // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}


}

// dart format on
