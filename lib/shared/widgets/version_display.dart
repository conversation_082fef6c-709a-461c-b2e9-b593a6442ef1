import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class VersionDisplay extends StatelessWidget {
  final Color? textColor;

  const VersionDisplay({super.key, this.textColor});

  Future<PackageInfo> _getPackageInfo() async {
    return await PackageInfo.fromPlatform();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PackageInfo>(
      future: _getPackageInfo(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return const Text('Error loading version info');
        }

        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        final packageInfo = snapshot.data!;
        return Text(
          'Version ${packageInfo.version} build ${packageInfo.buildNumber}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: textColor ?? Colors.black,
            shadows: [
              Shadow(
                offset: const Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Colors.black.withValues(alpha: .3),
              ),
            ],
          ),
        );
      },
    );
  }
}
