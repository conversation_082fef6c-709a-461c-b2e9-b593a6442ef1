// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pronunciation_result_formatted.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PronunciationResultFormatted {

 String get originalText; String get recordedInput; double get accuracyScore; double get fluencyScore; double get prosodyScore; double get completenessScore; double get pronScore; List<FormattedResult> get formattedResult;
/// Create a copy of PronunciationResultFormatted
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationResultFormattedCopyWith<PronunciationResultFormatted> get copyWith => _$PronunciationResultFormattedCopyWithImpl<PronunciationResultFormatted>(this as PronunciationResultFormatted, _$identity);

  /// Serializes this PronunciationResultFormatted to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationResultFormatted&&(identical(other.originalText, originalText) || other.originalText == originalText)&&(identical(other.recordedInput, recordedInput) || other.recordedInput == recordedInput)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&const DeepCollectionEquality().equals(other.formattedResult, formattedResult));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,originalText,recordedInput,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,const DeepCollectionEquality().hash(formattedResult));

@override
String toString() {
  return 'PronunciationResultFormatted(originalText: $originalText, recordedInput: $recordedInput, accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, formattedResult: $formattedResult)';
}


}

/// @nodoc
abstract mixin class $PronunciationResultFormattedCopyWith<$Res>  {
  factory $PronunciationResultFormattedCopyWith(PronunciationResultFormatted value, $Res Function(PronunciationResultFormatted) _then) = _$PronunciationResultFormattedCopyWithImpl;
@useResult
$Res call({
 String originalText, String recordedInput, double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, List<FormattedResult> formattedResult
});




}
/// @nodoc
class _$PronunciationResultFormattedCopyWithImpl<$Res>
    implements $PronunciationResultFormattedCopyWith<$Res> {
  _$PronunciationResultFormattedCopyWithImpl(this._self, this._then);

  final PronunciationResultFormatted _self;
  final $Res Function(PronunciationResultFormatted) _then;

/// Create a copy of PronunciationResultFormatted
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? originalText = null,Object? recordedInput = null,Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? formattedResult = null,}) {
  return _then(_self.copyWith(
originalText: null == originalText ? _self.originalText : originalText // ignore: cast_nullable_to_non_nullable
as String,recordedInput: null == recordedInput ? _self.recordedInput : recordedInput // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,formattedResult: null == formattedResult ? _self.formattedResult : formattedResult // ignore: cast_nullable_to_non_nullable
as List<FormattedResult>,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationResultFormatted].
extension PronunciationResultFormattedPatterns on PronunciationResultFormatted {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationResultFormatted value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationResultFormatted() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationResultFormatted value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationResultFormatted():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationResultFormatted value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationResultFormatted() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String originalText,  String recordedInput,  double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  List<FormattedResult> formattedResult)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationResultFormatted() when $default != null:
return $default(_that.originalText,_that.recordedInput,_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.formattedResult);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String originalText,  String recordedInput,  double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  List<FormattedResult> formattedResult)  $default,) {final _that = this;
switch (_that) {
case _PronunciationResultFormatted():
return $default(_that.originalText,_that.recordedInput,_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.formattedResult);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String originalText,  String recordedInput,  double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  List<FormattedResult> formattedResult)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationResultFormatted() when $default != null:
return $default(_that.originalText,_that.recordedInput,_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.formattedResult);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationResultFormatted implements PronunciationResultFormatted {
   _PronunciationResultFormatted({required this.originalText, required this.recordedInput, required this.accuracyScore, required this.fluencyScore, required this.prosodyScore, required this.completenessScore, required this.pronScore, required final  List<FormattedResult> formattedResult}): _formattedResult = formattedResult;
  factory _PronunciationResultFormatted.fromJson(Map<String, dynamic> json) => _$PronunciationResultFormattedFromJson(json);

@override final  String originalText;
@override final  String recordedInput;
@override final  double accuracyScore;
@override final  double fluencyScore;
@override final  double prosodyScore;
@override final  double completenessScore;
@override final  double pronScore;
 final  List<FormattedResult> _formattedResult;
@override List<FormattedResult> get formattedResult {
  if (_formattedResult is EqualUnmodifiableListView) return _formattedResult;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_formattedResult);
}


/// Create a copy of PronunciationResultFormatted
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationResultFormattedCopyWith<_PronunciationResultFormatted> get copyWith => __$PronunciationResultFormattedCopyWithImpl<_PronunciationResultFormatted>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationResultFormattedToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationResultFormatted&&(identical(other.originalText, originalText) || other.originalText == originalText)&&(identical(other.recordedInput, recordedInput) || other.recordedInput == recordedInput)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&const DeepCollectionEquality().equals(other._formattedResult, _formattedResult));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,originalText,recordedInput,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,const DeepCollectionEquality().hash(_formattedResult));

@override
String toString() {
  return 'PronunciationResultFormatted(originalText: $originalText, recordedInput: $recordedInput, accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, formattedResult: $formattedResult)';
}


}

/// @nodoc
abstract mixin class _$PronunciationResultFormattedCopyWith<$Res> implements $PronunciationResultFormattedCopyWith<$Res> {
  factory _$PronunciationResultFormattedCopyWith(_PronunciationResultFormatted value, $Res Function(_PronunciationResultFormatted) _then) = __$PronunciationResultFormattedCopyWithImpl;
@override @useResult
$Res call({
 String originalText, String recordedInput, double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, List<FormattedResult> formattedResult
});




}
/// @nodoc
class __$PronunciationResultFormattedCopyWithImpl<$Res>
    implements _$PronunciationResultFormattedCopyWith<$Res> {
  __$PronunciationResultFormattedCopyWithImpl(this._self, this._then);

  final _PronunciationResultFormatted _self;
  final $Res Function(_PronunciationResultFormatted) _then;

/// Create a copy of PronunciationResultFormatted
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? originalText = null,Object? recordedInput = null,Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? formattedResult = null,}) {
  return _then(_PronunciationResultFormatted(
originalText: null == originalText ? _self.originalText : originalText // ignore: cast_nullable_to_non_nullable
as String,recordedInput: null == recordedInput ? _self.recordedInput : recordedInput // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,formattedResult: null == formattedResult ? _self._formattedResult : formattedResult // ignore: cast_nullable_to_non_nullable
as List<FormattedResult>,
  ));
}


}


/// @nodoc
mixin _$FormattedResult {

 String get text; double get accuracyScore; String get errorType;
/// Create a copy of FormattedResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FormattedResultCopyWith<FormattedResult> get copyWith => _$FormattedResultCopyWithImpl<FormattedResult>(this as FormattedResult, _$identity);

  /// Serializes this FormattedResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FormattedResult&&(identical(other.text, text) || other.text == text)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.errorType, errorType) || other.errorType == errorType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text,accuracyScore,errorType);

@override
String toString() {
  return 'FormattedResult(text: $text, accuracyScore: $accuracyScore, errorType: $errorType)';
}


}

/// @nodoc
abstract mixin class $FormattedResultCopyWith<$Res>  {
  factory $FormattedResultCopyWith(FormattedResult value, $Res Function(FormattedResult) _then) = _$FormattedResultCopyWithImpl;
@useResult
$Res call({
 String text, double accuracyScore, String errorType
});




}
/// @nodoc
class _$FormattedResultCopyWithImpl<$Res>
    implements $FormattedResultCopyWith<$Res> {
  _$FormattedResultCopyWithImpl(this._self, this._then);

  final FormattedResult _self;
  final $Res Function(FormattedResult) _then;

/// Create a copy of FormattedResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? text = null,Object? accuracyScore = null,Object? errorType = null,}) {
  return _then(_self.copyWith(
text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [FormattedResult].
extension FormattedResultPatterns on FormattedResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FormattedResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FormattedResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FormattedResult value)  $default,){
final _that = this;
switch (_that) {
case _FormattedResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FormattedResult value)?  $default,){
final _that = this;
switch (_that) {
case _FormattedResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String text,  double accuracyScore,  String errorType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FormattedResult() when $default != null:
return $default(_that.text,_that.accuracyScore,_that.errorType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String text,  double accuracyScore,  String errorType)  $default,) {final _that = this;
switch (_that) {
case _FormattedResult():
return $default(_that.text,_that.accuracyScore,_that.errorType);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String text,  double accuracyScore,  String errorType)?  $default,) {final _that = this;
switch (_that) {
case _FormattedResult() when $default != null:
return $default(_that.text,_that.accuracyScore,_that.errorType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FormattedResult implements FormattedResult {
   _FormattedResult({required this.text, required this.accuracyScore, required this.errorType});
  factory _FormattedResult.fromJson(Map<String, dynamic> json) => _$FormattedResultFromJson(json);

@override final  String text;
@override final  double accuracyScore;
@override final  String errorType;

/// Create a copy of FormattedResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FormattedResultCopyWith<_FormattedResult> get copyWith => __$FormattedResultCopyWithImpl<_FormattedResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FormattedResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FormattedResult&&(identical(other.text, text) || other.text == text)&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.errorType, errorType) || other.errorType == errorType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,text,accuracyScore,errorType);

@override
String toString() {
  return 'FormattedResult(text: $text, accuracyScore: $accuracyScore, errorType: $errorType)';
}


}

/// @nodoc
abstract mixin class _$FormattedResultCopyWith<$Res> implements $FormattedResultCopyWith<$Res> {
  factory _$FormattedResultCopyWith(_FormattedResult value, $Res Function(_FormattedResult) _then) = __$FormattedResultCopyWithImpl;
@override @useResult
$Res call({
 String text, double accuracyScore, String errorType
});




}
/// @nodoc
class __$FormattedResultCopyWithImpl<$Res>
    implements _$FormattedResultCopyWith<$Res> {
  __$FormattedResultCopyWithImpl(this._self, this._then);

  final _FormattedResult _self;
  final $Res Function(_FormattedResult) _then;

/// Create a copy of FormattedResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? text = null,Object? accuracyScore = null,Object? errorType = null,}) {
  return _then(_FormattedResult(
text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ScoreResult {

 int get prosody; int get phoneme; int get completeness;
/// Create a copy of ScoreResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ScoreResultCopyWith<ScoreResult> get copyWith => _$ScoreResultCopyWithImpl<ScoreResult>(this as ScoreResult, _$identity);

  /// Serializes this ScoreResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ScoreResult&&(identical(other.prosody, prosody) || other.prosody == prosody)&&(identical(other.phoneme, phoneme) || other.phoneme == phoneme)&&(identical(other.completeness, completeness) || other.completeness == completeness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosody,phoneme,completeness);

@override
String toString() {
  return 'ScoreResult(prosody: $prosody, phoneme: $phoneme, completeness: $completeness)';
}


}

/// @nodoc
abstract mixin class $ScoreResultCopyWith<$Res>  {
  factory $ScoreResultCopyWith(ScoreResult value, $Res Function(ScoreResult) _then) = _$ScoreResultCopyWithImpl;
@useResult
$Res call({
 int prosody, int phoneme, int completeness
});




}
/// @nodoc
class _$ScoreResultCopyWithImpl<$Res>
    implements $ScoreResultCopyWith<$Res> {
  _$ScoreResultCopyWithImpl(this._self, this._then);

  final ScoreResult _self;
  final $Res Function(ScoreResult) _then;

/// Create a copy of ScoreResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prosody = null,Object? phoneme = null,Object? completeness = null,}) {
  return _then(_self.copyWith(
prosody: null == prosody ? _self.prosody : prosody // ignore: cast_nullable_to_non_nullable
as int,phoneme: null == phoneme ? _self.phoneme : phoneme // ignore: cast_nullable_to_non_nullable
as int,completeness: null == completeness ? _self.completeness : completeness // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ScoreResult].
extension ScoreResultPatterns on ScoreResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ScoreResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ScoreResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ScoreResult value)  $default,){
final _that = this;
switch (_that) {
case _ScoreResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ScoreResult value)?  $default,){
final _that = this;
switch (_that) {
case _ScoreResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int prosody,  int phoneme,  int completeness)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ScoreResult() when $default != null:
return $default(_that.prosody,_that.phoneme,_that.completeness);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int prosody,  int phoneme,  int completeness)  $default,) {final _that = this;
switch (_that) {
case _ScoreResult():
return $default(_that.prosody,_that.phoneme,_that.completeness);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int prosody,  int phoneme,  int completeness)?  $default,) {final _that = this;
switch (_that) {
case _ScoreResult() when $default != null:
return $default(_that.prosody,_that.phoneme,_that.completeness);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ScoreResult implements ScoreResult {
   _ScoreResult({this.prosody = 0, this.phoneme = 0, this.completeness = 0});
  factory _ScoreResult.fromJson(Map<String, dynamic> json) => _$ScoreResultFromJson(json);

@override@JsonKey() final  int prosody;
@override@JsonKey() final  int phoneme;
@override@JsonKey() final  int completeness;

/// Create a copy of ScoreResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ScoreResultCopyWith<_ScoreResult> get copyWith => __$ScoreResultCopyWithImpl<_ScoreResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ScoreResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ScoreResult&&(identical(other.prosody, prosody) || other.prosody == prosody)&&(identical(other.phoneme, phoneme) || other.phoneme == phoneme)&&(identical(other.completeness, completeness) || other.completeness == completeness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,prosody,phoneme,completeness);

@override
String toString() {
  return 'ScoreResult(prosody: $prosody, phoneme: $phoneme, completeness: $completeness)';
}


}

/// @nodoc
abstract mixin class _$ScoreResultCopyWith<$Res> implements $ScoreResultCopyWith<$Res> {
  factory _$ScoreResultCopyWith(_ScoreResult value, $Res Function(_ScoreResult) _then) = __$ScoreResultCopyWithImpl;
@override @useResult
$Res call({
 int prosody, int phoneme, int completeness
});




}
/// @nodoc
class __$ScoreResultCopyWithImpl<$Res>
    implements _$ScoreResultCopyWith<$Res> {
  __$ScoreResultCopyWithImpl(this._self, this._then);

  final _ScoreResult _self;
  final $Res Function(_ScoreResult) _then;

/// Create a copy of ScoreResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prosody = null,Object? phoneme = null,Object? completeness = null,}) {
  return _then(_ScoreResult(
prosody: null == prosody ? _self.prosody : prosody // ignore: cast_nullable_to_non_nullable
as int,phoneme: null == phoneme ? _self.phoneme : phoneme // ignore: cast_nullable_to_non_nullable
as int,completeness: null == completeness ? _self.completeness : completeness // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
