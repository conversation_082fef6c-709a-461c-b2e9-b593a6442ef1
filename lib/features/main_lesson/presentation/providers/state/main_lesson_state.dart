import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

part 'main_lesson_state.freezed.dart';

@freezed
abstract class MainLessonState with _$MainLessonState {
  factory MainLessonState({
    LastCourse? lastPronunciation,
    LastCourse? lastConversation,
    LastCourse? lastListening,
    LastCourse? lastSpeaking,
    @Default(false) bool fromLastCourse,
  }) = _MainLessonState;

  // Allow custom getters / setters
  const MainLessonState._();
}
