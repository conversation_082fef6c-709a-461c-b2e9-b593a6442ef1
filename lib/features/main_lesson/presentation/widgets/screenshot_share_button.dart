import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:share_plus/share_plus.dart';

class ScreenshotShareButton extends StatelessWidget {
  const ScreenshotShareButton({
    super.key,
    required this.title,
    required this.screenshotController,
  });

  final String title;
  final ScreenshotController screenshotController;

  @override
  Widget build(BuildContext context) {
    return VButtonGradient(
      title: context.loc.share,
      onTap: () => _captureAndShareScreenshot(context),
      isBorder: false,
      // backgroundColor: Colors.white,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        color: Colors.transparent,
      ),
      leading: Icon(Icons.share, color: Color(0xff998E8D)),
      fontStyle: Theme.of(
        context,
      ).textTheme.bodyLarge!.copyWith(color: Color(0xff998E8D)),
    );
  }

  /// Captures and shares a screenshot of the current screen
  Future<void> _captureAndShareScreenshot(BuildContext context) async {
    try {
      // Capture the screenshot
      final Uint8List? imageBytes = await screenshotController.capture();
      if (imageBytes == null) {
        // Handle case where screenshot capture failed
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("context.loc.error_occurred")));
        return;
      }

      // Get temporary directory to save the screenshot
      final directory = await getTemporaryDirectory();
      final imagePath =
          '${directory.path}/speaking_result_${DateTime.now().millisecondsSinceEpoch}.png';
      final File imageFile = File(imagePath);
      await imageFile.writeAsBytes(imageBytes);

      // Share the screenshot
      final files = <XFile>[XFile(imagePath)];
      await SharePlus.instance.share(
        ShareParams(files: files, text: title, subject: 'My Certificates'),
      );
    } catch (e) {
      debugPrint('Error sharing screenshot: $e');
      // Handle any errors that occur during the process
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("context.loc.error_occurred")));
    }
  }
}
