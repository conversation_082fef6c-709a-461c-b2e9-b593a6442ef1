import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class SettingRepository {
  Future<Either<AppException, List<String>>> isNewUser();
}

class SettingRepositoryImpl extends SettingRepository {
  // final Firebase Firestore dataSource;
  final FirestoreServiceRepository dataSource;

  SettingRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppException, List<String>>> isNewUser() async {
    try {
      final QuerySnapshot<Map<String, dynamic>> doc =
          await dataSource.dataUser().collection('testResult').get();
      List<String> temp = [];
      for (var value in doc.docs) {
        temp.add(value.id);
      }
      return Right<AppException, List<String>>(temp);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch test result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
