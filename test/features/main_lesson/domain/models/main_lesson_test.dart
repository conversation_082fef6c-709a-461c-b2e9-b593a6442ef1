import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

void main() {
  group('MainLesson', () {
    group('MainLesson Model Tests', () {
      test('Initial state should be QuestionConcreteState.initial', () {
        final mainLesson = MainLesson();
        expect(mainLesson.state, QuestionConcreteState.initial);
      });

      test('Should create MainLesson with custom values', () {
        final mainLesson = MainLesson(
          state: QuestionConcreteState.answered,
          questionId: 'test_id',
          order: 5,
          correctAnswer: 'correct answer',
          question: 'test question',
          answer: 'user answer',
          isCorrect: true,
        );

        expect(mainLesson.state, QuestionConcreteState.answered);
        expect(mainLesson.questionId, 'test_id');
        expect(mainLesson.order, 5);
        expect(mainLesson.correctAnswer, 'correct answer');
        expect(mainLesson.question, 'test question');
        expect(mainLesson.answer, 'user answer');
        expect(mainLesson.isCorrect, true);
      });

      test('Should create MainLesson with default values', () {
        final mainLesson = MainLesson();

        expect(mainLesson.state, QuestionConcreteState.initial);
        expect(mainLesson.questionId, null);
        expect(mainLesson.order, 0);
        expect(mainLesson.correctAnswer, '');
        expect(mainLesson.question, '');
        expect(mainLesson.answer, '');
        expect(mainLesson.isCorrect, false);
      });

      test('fromJson should create MainLesson from JSON', () {
        final json = {
          'question_id': 'test_id',
          'correct_answer': 'correct answer',
          'question': 'test question',
          'answer': 'user answer',
          'is_correct': true,
        };

        final mainLesson = MainLesson.fromJson(json);

        expect(mainLesson.questionId, 'test_id');
        // Note: state, order, correctAnswer, and question are marked with includeToJson: false
        // so they won't be serialized/deserialized
        expect(mainLesson.answer, 'user answer');
        expect(mainLesson.isCorrect, true);
      });

      test('toJson should convert MainLesson to JSON', () {
        final mainLesson = MainLesson(
          questionId: 'test_id',
          answer: 'user answer',
          isCorrect: true,
        );

        final json = mainLesson.toJson();

        expect(json['question_id'], 'test_id');
        expect(json['answer'], 'user answer');
        expect(json['is_correct'], true);
        // Fields with includeToJson: false should not be in the JSON
        expect(json.containsKey('state'), false);
        expect(json.containsKey('order'), false);
        expect(json.containsKey('correct_answer'), false);
        expect(json.containsKey('question'), false);
      });
    });

    group('QuestionConcreteState Enum Tests', () {
      test('Should have correct values', () {
        expect(QuestionConcreteState.values, [
          QuestionConcreteState.initial,
          QuestionConcreteState.answered,
        ]);
        expect(QuestionConcreteState.initial.name, 'initial');
        expect(QuestionConcreteState.answered.name, 'answered');
      });
    });

    group('SpeakingStage Enum Tests', () {
      test('Should have correct values', () {
        expect(SpeakingStage.values, [
          SpeakingStage.stage1,
          SpeakingStage.onboardingStage2,
          SpeakingStage.stage2,
          SpeakingStage.onboardingStage3,
          SpeakingStage.stage3,
        ]);
        expect(SpeakingStage.stage1.name, 'stage1');
        expect(SpeakingStage.onboardingStage2.name, 'onboardingStage2');
        expect(SpeakingStage.stage2.name, 'stage2');
        expect(SpeakingStage.onboardingStage3.name, 'onboardingStage3');
        expect(SpeakingStage.stage3.name, 'stage3');
      });
    });

    group('ScoreCategory Enum Tests', () {
      test('Should have correct values', () {
        expect(ScoreCategory.values, [
          ScoreCategory.accuracy,
          ScoreCategory.fluency,
          ScoreCategory.rhythm,
          ScoreCategory.completeness,
          ScoreCategory.prosody,
        ]);
        expect(ScoreCategory.accuracy.name, 'accuracy');
        expect(ScoreCategory.fluency.name, 'fluency');
        expect(ScoreCategory.rhythm.name, 'rhythm');
        expect(ScoreCategory.completeness.name, 'completeness');
        expect(ScoreCategory.prosody.name, 'prosody');
      });
    });

    group('SpeakingSessionType Enum Tests', () {
      test('Should have correct values', () {
        expect(SpeakingSessionType.values, [
          SpeakingSessionType.question,
          SpeakingSessionType.answer,
        ]);
        expect(SpeakingSessionType.question.name, 'question');
        expect(SpeakingSessionType.answer.name, 'answer');
      });
    });

    group('ChapterIndexData Tests', () {
      test('Should create ChapterIndexData with required values', () {
        final chapterIndexData = ChapterIndexData(
          chapter: 1,
          level: 'beginner',
          path: '/path/to/chapter',
        );

        expect(chapterIndexData.chapter, 1);
        expect(chapterIndexData.level, 'beginner');
        expect(chapterIndexData.path, '/path/to/chapter');
      });

      test('fromJson should create ChapterIndexData from JSON', () {
        final json = {
          'chapter': 2,
          'level': 'intermediate',
          'path': '/path/to/chapter2',
        };

        final chapterIndexData = ChapterIndexData.fromJson(json);

        expect(chapterIndexData.chapter, 2);
        expect(chapterIndexData.level, 'intermediate');
        expect(chapterIndexData.path, '/path/to/chapter2');
      });

      test('toJson should convert ChapterIndexData to JSON', () {
        final chapterIndexData = ChapterIndexData(
          chapter: 3,
          level: 'advanced',
          path: '/path/to/chapter3',
        );

        final json = chapterIndexData.toJson();

        expect(json['chapter'], 3);
        expect(json['level'], 'advanced');
        expect(json['path'], '/path/to/chapter3');
      });
    });

    group('ContentIndexData Tests', () {
      test('Should create ContentIndexData with default values', () {
        final contentIndexData = ContentIndexData();

        expect(contentIndexData.title, '');
        expect(contentIndexData.contentPath, '');
        expect(contentIndexData.contentOrder, 1);
        expect(contentIndexData.partOrder, null);
        expect(contentIndexData.partTitle, null);
        expect(contentIndexData.subpartOrder, null);
        expect(contentIndexData.subpartTitle, null);
        expect(contentIndexData.hasResult, false);
        expect(contentIndexData.isBookmarked, false);
        expect(contentIndexData.firstStage, false);
        expect(contentIndexData.secondStage, false);
        expect(contentIndexData.thirdStage, false);
      });

      test('Should create ContentIndexData with custom values', () {
        final contentIndexData = ContentIndexData(
          title: 'Test Title',
          contentPath: '/path/to/content',
          contentOrder: 5,
          partOrder: 2,
          partTitle: 'Part Title',
          subpartOrder: 3,
          subpartTitle: 'Subpart Title',
          hasResult: true,
          isBookmarked: true,
          firstStage: true,
          secondStage: true,
          thirdStage: true,
        );

        expect(contentIndexData.title, 'Test Title');
        expect(contentIndexData.contentPath, '/path/to/content');
        expect(contentIndexData.contentOrder, 5);
        expect(contentIndexData.partOrder, 2);
        expect(contentIndexData.partTitle, 'Part Title');
        expect(contentIndexData.subpartOrder, 3);
        expect(contentIndexData.subpartTitle, 'Subpart Title');
        expect(contentIndexData.hasResult, true);
        expect(contentIndexData.isBookmarked, true);
        expect(contentIndexData.firstStage, true);
        expect(contentIndexData.secondStage, true);
        expect(contentIndexData.thirdStage, true);
      });

      test('fromJson should create ContentIndexData from JSON', () {
        final json = {
          'content_title': 'Test Title',
          'content_path': '/path/to/content',
          'content_order': 5,
          'part_order': 2,
          'part_title': 'Part Title',
          'subpart_order': 3,
          'subpart_title': 'Subpart Title',
          'hasResult': true,
          'isBookmarked': true,
          'firstStage': true,
          'secondStage': true,
          'thirdStage': true,
        };

        final contentIndexData = ContentIndexData.fromJson(json);

        expect(contentIndexData.title, 'Test Title');
        expect(contentIndexData.contentPath, '/path/to/content');
        expect(contentIndexData.contentOrder, 5);
        expect(contentIndexData.partOrder, 2);
        expect(contentIndexData.partTitle, 'Part Title');
        expect(contentIndexData.subpartOrder, 3);
        expect(contentIndexData.subpartTitle, 'Subpart Title');
        expect(contentIndexData.hasResult, true);
        expect(contentIndexData.isBookmarked, true);
        expect(contentIndexData.firstStage, true);
        expect(contentIndexData.secondStage, true);
        expect(contentIndexData.thirdStage, true);
      });

      test('toJson should convert ContentIndexData to JSON', () {
        final contentIndexData = ContentIndexData(
          title: 'Test Title',
          contentPath: '/path/to/content',
          contentOrder: 5,
          partOrder: 2,
          partTitle: 'Part Title',
          subpartOrder: 3,
          subpartTitle: 'Subpart Title',
          hasResult: true,
          isBookmarked: true,
          firstStage: true,
          secondStage: true,
          thirdStage: true,
        );

        final json = contentIndexData.toJson();

        expect(json['content_title'], 'Test Title');
        expect(json['content_path'], '/path/to/content');
        expect(json['content_order'], 5);
        expect(json['part_order'], 2);
        expect(json['part_title'], 'Part Title');
        expect(json['subpart_order'], 3);
        expect(json['subpart_title'], 'Subpart Title');
        expect(json['hasResult'], true);
        expect(json['isBookmarked'], true);
        expect(json['firstStage'], true);
        expect(json['secondStage'], true);
        expect(json['thirdStage'], true);
      });
    });

    group('PronunciationSubPart Tests', () {
      test('Should create PronunciationSubPart with required values', () {
        final pronunciationSubPart = PronunciationSubPart(
          audio: 'test_audio_url',
          image: 'test_image_url',
          caption: 'test_caption',
          translation: 'test_translation',
          order: 1,
        );

        expect(pronunciationSubPart.audio, 'test_audio_url');
        expect(pronunciationSubPart.image, 'test_image_url');
        expect(pronunciationSubPart.caption, 'test_caption');
        expect(pronunciationSubPart.translation, 'test_translation');
        expect(pronunciationSubPart.description, null);
        expect(pronunciationSubPart.order, 1);
        expect(pronunciationSubPart.isBookmarked, false);
      });

      test('Should create PronunciationSubPart with optional values', () {
        final pronunciationSubPart = PronunciationSubPart(
          audio: 'test_audio_url',
          image: 'test_image_url',
          caption: 'test_caption',
          translation: 'test_translation',
          description: 'test_description',
          order: 2,
          isBookmarked: true,
        );

        expect(pronunciationSubPart.audio, 'test_audio_url');
        expect(pronunciationSubPart.image, 'test_image_url');
        expect(pronunciationSubPart.caption, 'test_caption');
        expect(pronunciationSubPart.translation, 'test_translation');
        expect(pronunciationSubPart.description, 'test_description');
        expect(pronunciationSubPart.order, 2);
        expect(pronunciationSubPart.isBookmarked, true);
      });

      test('fromJson should create PronunciationSubPart from JSON', () {
        final json = {
          'audio_url': 'test_audio_url',
          'image_url': 'test_image_url',
          'caption': 'test_caption',
          'translation': 'test_translation',
          'description': 'test_description',
          'order': 3,
          'isBookmarked': true,
        };

        final pronunciationSubPart = PronunciationSubPart.fromJson(json);

        expect(pronunciationSubPart.audio, 'test_audio_url');
        expect(pronunciationSubPart.image, 'test_image_url');
        expect(pronunciationSubPart.caption, 'test_caption');
        expect(pronunciationSubPart.translation, 'test_translation');
        expect(pronunciationSubPart.description, 'test_description');
        expect(pronunciationSubPart.order, 3);
        expect(pronunciationSubPart.isBookmarked, true);
      });

      test('toJson should convert PronunciationSubPart to JSON', () {
        final pronunciationSubPart = PronunciationSubPart(
          audio: 'test_audio_url',
          image: 'test_image_url',
          caption: 'test_caption',
          translation: 'test_translation',
          description: 'test_description',
          order: 4,
          isBookmarked: true,
        );

        final json = pronunciationSubPart.toJson();

        expect(json['audio_url'], 'test_audio_url');
        expect(json['image_url'], 'test_image_url');
        expect(json['caption'], 'test_caption');
        expect(json['translation'], 'test_translation');
        expect(json['description'], 'test_description');
        expect(json['order'], 4);
        expect(json['isBookmarked'], true);
      });
    });

    group('ConversationPart Tests', () {
      test('Should create ConversationPart with default values', () {
        final conversationPart = ConversationPart();

        expect(conversationPart.order, null);
        expect(conversationPart.title, null);
        expect(conversationPart.video, null);
        expect(conversationPart.videoController, null);
        expect(conversationPart.videoMeta, null);
        expect(conversationPart.image, null);
        expect(conversationPart.isBookmarked, false);
      });

      test('Should create ConversationPart with custom values', () {
        final conversationPart = ConversationPart(
          order: 1,
          title: 'Test Title',
          video: 'test_video_url',
          image: 'test_image_url',
          isBookmarked: true,
        );

        expect(conversationPart.order, 1);
        expect(conversationPart.title, 'Test Title');
        expect(conversationPart.video, 'test_video_url');
        expect(conversationPart.image, 'test_image_url');
        expect(conversationPart.isBookmarked, true);
      });

      test('fromJson should create ConversationPart from JSON', () {
        final json = {
          'order': 2,
          'title': 'Test Title',
          'video_url': 'test_video_url',
          'img_url': 'test_image_url',
          'isBookmarked': true,
        };

        final conversationPart = ConversationPart.fromJson(json);

        expect(conversationPart.order, 2);
        expect(conversationPart.title, 'Test Title');
        expect(conversationPart.video, 'test_video_url');
        expect(conversationPart.image, 'test_image_url');
        expect(conversationPart.isBookmarked, true);
      });

      test('toJson should convert ConversationPart to JSON', () {
        final conversationPart = ConversationPart(
          order: 3,
          title: 'Test Title',
          video: 'test_video_url',
          image: 'test_image_url',
          isBookmarked: true,
        );

        final json = conversationPart.toJson();

        expect(json['order'], 3);
        expect(json['title'], 'Test Title');
        expect(json['video_url'], 'test_video_url');
        expect(json['img_url'], 'test_image_url');
        expect(json['isBookmarked'], true);
      });
    });

    group('VideoMeta Tests', () {
      test('Should create VideoMeta with default values', () {
        final videoMeta = VideoMeta();

        expect(videoMeta.duration, null);
        expect(videoMeta.isPlayed, false);
      });

      test('Should create VideoMeta with custom values', () {
        final duration = const Duration(seconds: 30);
        final videoMeta = VideoMeta(duration: duration, isPlayed: true);

        expect(videoMeta.duration, duration);
        expect(videoMeta.isPlayed, true);
      });

      test('fromJson should create VideoMeta from JSON', () {
        final json = {'duration': '30', 'isPlayed': true};

        final videoMeta = VideoMeta.fromJson(json);

        expect(videoMeta.isPlayed, true);
        expect(videoMeta.duration, isNotNull);
      });

      test('toJson should convert VideoMeta to JSON', () {
        final duration = const Duration(seconds: 30);
        final videoMeta = VideoMeta(duration: duration, isPlayed: true);

        final json = videoMeta.toJson();

        expect(json['isPlayed'], true);
        expect(json['duration'], isNotNull);
      });
    });

    group('AudioPath Tests', () {
      test('Should create AudioPath with default values', () {
        final audioPath = AudioPath();

        expect(audioPath.path, '');
        expect(audioPath.refPath, '');
        expect(audioPath.url, '');
      });

      test('Should create AudioPath with custom values', () {
        final audioPath = AudioPath(
          path: 'test_path',
          refPath: 'test_ref_path',
          url: 'test_url',
        );

        expect(audioPath.path, 'test_path');
        expect(audioPath.refPath, 'test_ref_path');
        expect(audioPath.url, 'test_url');
      });

      test('fromJson should create AudioPath from JSON', () {
        final json = {
          'path': 'test_path',
          'refPath': 'test_ref_path',
          'url': 'test_url',
        };

        final audioPath = AudioPath.fromJson(json);

        expect(audioPath.path, 'test_path');
        expect(audioPath.refPath, 'test_ref_path');
        expect(audioPath.url, 'test_url');
      });

      test('toJson should convert AudioPath to JSON', () {
        final audioPath = AudioPath(
          path: 'test_path',
          refPath: 'test_ref_path',
          url: 'test_url',
        );

        final json = audioPath.toJson();

        expect(json['path'], 'test_path');
        expect(json['refPath'], 'test_ref_path');
        expect(json['url'], 'test_url');
      });
    });

    group('ListeningPart Tests', () {
      test('Should create ListeningPart with default values', () {
        final listeningPart = ListeningPart();

        expect(listeningPart.main, null);
        expect(listeningPart.order, null);
        expect(listeningPart.title, null);
        expect(listeningPart.image, null);
        expect(listeningPart.isBookmarked, false);
        expect(listeningPart.questions, []);
      });

      test('Should create ListeningPart with custom values', () {
        final questions = [Question(order: 1, question: 'Test question')];
        final listeningPart = ListeningPart(
          main: 'test_audio_url',
          order: 1,
          title: 'Test Title',
          image: 'test_image_url',
          isBookmarked: true,
          questions: questions,
        );

        expect(listeningPart.main, 'test_audio_url');
        expect(listeningPart.order, 1);
        expect(listeningPart.title, 'Test Title');
        expect(listeningPart.image, 'test_image_url');
        expect(listeningPart.isBookmarked, true);
        expect(listeningPart.questions, questions);
      });

      test('fromJson should create ListeningPart from JSON', () {
        final json = {
          'audio_url': 'test_audio_url',
          'order': 2,
          'title': 'Test Title',
          'image_url': 'test_image_url',
          'isBookmarked': true,
          'questions': [
            {'order': 1, 'question': 'Test question'},
          ],
        };

        final listeningPart = ListeningPart.fromJson(json);

        expect(listeningPart.main, 'test_audio_url');
        expect(listeningPart.order, 2);
        expect(listeningPart.title, 'Test Title');
        expect(listeningPart.image, 'test_image_url');
        expect(listeningPart.isBookmarked, true);
        expect(listeningPart.questions, isNotNull);
        expect(listeningPart.questions.length, 1);
      });

      test('toJson should convert ListeningPart to JSON', () {
        final questions = [Question(order: 1, question: 'Test question')];
        final listeningPart = ListeningPart(
          main: 'test_audio_url',
          order: 3,
          title: 'Test Title',
          image: 'test_image_url',
          isBookmarked: true,
          questions: questions,
        );

        final json = listeningPart.toJson();

        expect(json['audio_url'], 'test_audio_url');
        expect(json['order'], 3);
        expect(json['title'], 'Test Title');
        expect(json['image_url'], 'test_image_url');
        expect(json['isBookmarked'], true);
        expect(json['questions'], isNotNull);
      });
    });

    group('Question Tests', () {
      test('Should create Question with default values', () {
        final question = Question();

        expect(question.collapse, null);
        expect(question.order, null);
        expect(question.question, null);
        expect(question.answer, null);
        expect(question.isCorrect, null);
        expect(question.choices, []);
      });

      test('Should create Question with custom values', () {
        final choices = [Choice(text: 'Choice 1', isCorrect: true)];
        final question = Question(
          collapse: true,
          order: 1,
          question: 'Test question',
          answer: 'Test answer',
          isCorrect: true,
          choices: choices,
        );

        expect(question.collapse, true);
        expect(question.order, 1);
        expect(question.question, 'Test question');
        expect(question.answer, 'Test answer');
        expect(question.isCorrect, true);
        expect(question.choices, choices);
      });

      test('fromJson should create Question from JSON', () {
        final json = {
          'collapse': true,
          'order': 2,
          'question': 'Test question',
          'answer': 'Test answer',
          'choices': [
            {'text': 'Choice 1', 'is_correct': true},
          ],
        };

        final question = Question.fromJson(json);

        expect(question.collapse, true);
        expect(question.order, 2);
        expect(question.question, 'Test question');
        expect(question.answer, 'Test answer');
        expect(question.isCorrect, null);
        expect(question.choices, isNotNull);
        expect(question.choices.length, 1);
      });

      test('toJson should convert Question to JSON', () {
        final choices = [Choice(text: 'Choice 1', isCorrect: true)];
        final question = Question(
          collapse: true,
          order: 3,
          question: 'Test question',
          answer: 'Test answer',
          isCorrect: null,
          choices: choices,
        );

        final json = question.toJson();

        expect(json['collapse'], true);
        expect(json['order'], 3);
        expect(json['question'], 'Test question');
        expect(json['answer'], 'Test answer');
        expect(json.containsKey('is_correct'), false);
        expect(json['choices'], isNotNull);
      });
    });

    group('Choice Tests', () {
      test('Should create Choice with default values', () {
        final choice = Choice();

        expect(choice.isCorrect, null);
        expect(choice.text, null);
      });

      test('Should create Choice with custom values', () {
        final choice = Choice(isCorrect: true, text: 'Test choice');

        expect(choice.isCorrect, true);
        expect(choice.text, 'Test choice');
      });

      test('fromJson should create Choice from JSON', () {
        final json = {'is_correct': true, 'text': 'Test choice'};

        final choice = Choice.fromJson(json);

        expect(choice.isCorrect, true);
        expect(choice.text, 'Test choice');
      });

      test('toJson should convert Choice to JSON', () {
        final choice = Choice(isCorrect: true, text: 'Test choice');

        final json = choice.toJson();

        expect(json['is_correct'], true);
        expect(json['text'], 'Test choice');
      });
    });

    group('SpeakingPart Tests', () {
      test('Should create SpeakingPart with required values', () {
        final answer = Answer(audio: 'test_audio_url', text: 'Test text');
        final question = Answer(audio: 'test_audio_url', text: 'Test question');
        final speakingPart = SpeakingPart(
          answer: answer,
          question: question,
          image: 'test_image_url',
          imageB: 'test_imageB_url',
          order: 1,
          title: 'Test Title',
        );

        expect(speakingPart.answer, answer);
        expect(speakingPart.question, question);
        expect(speakingPart.image, 'test_image_url');
        expect(speakingPart.imageB, 'test_imageB_url');
        expect(speakingPart.order, 1);
        expect(speakingPart.title, 'Test Title');
        expect(speakingPart.isBookmarked, false);
      });

      test('Should create SpeakingPart with optional values', () {
        final answer = Answer(audio: 'test_audio_url', text: 'Test text');
        final question = Answer(audio: 'test_audio_url', text: 'Test question');
        final speakingPart = SpeakingPart(
          answer: answer,
          question: question,
          image: 'test_image_url',
          imageB: 'test_imageB_url',
          order: 2,
          title: 'Test Title',
          isBookmarked: true,
        );

        expect(speakingPart.answer, answer);
        expect(speakingPart.question, question);
        expect(speakingPart.image, 'test_image_url');
        expect(speakingPart.imageB, 'test_imageB_url');
        expect(speakingPart.order, 2);
        expect(speakingPart.title, 'Test Title');
        expect(speakingPart.isBookmarked, true);
      });

      test('fromJson should create SpeakingPart from JSON', () {
        final json = {
          'answer': {'audio_url': 'test_audio_url', 'text': 'Test text'},
          'question': {'audio_url': 'test_audio_url', 'text': 'Test question'},
          'image_url': 'test_image_url',
          'imageB_url': 'test_imageB_url',
          'order': 3,
          'title': 'Test Title',
          'isBookmarked': true,
        };

        final speakingPart = SpeakingPart.fromJson(json);

        expect(speakingPart.answer, isNotNull);
        expect(speakingPart.question, isNotNull);
        expect(speakingPart.image, 'test_image_url');
        expect(speakingPart.imageB, 'test_imageB_url');
        expect(speakingPart.order, 3);
        expect(speakingPart.title, 'Test Title');
        expect(speakingPart.isBookmarked, true);
      });

      test('toJson should convert SpeakingPart to JSON', () {
        final answer = Answer(audio: 'test_audio_url', text: 'Test text');
        final question = Answer(audio: 'test_audio_url', text: 'Test question');
        final speakingPart = SpeakingPart(
          answer: answer,
          question: question,
          image: 'test_image_url',
          imageB: 'test_imageB_url',
          order: 4,
          title: 'Test Title',
          isBookmarked: true,
        );

        final json = speakingPart.toJson();

        expect(json['answer'], isNotNull);
        expect(json['question'], isNotNull);
        expect(json['image_url'], 'test_image_url');
        expect(json['imageB_url'], 'test_imageB_url');
        expect(json['order'], 4);
        expect(json['title'], 'Test Title');
        expect(json['isBookmarked'], true);
      });
    });

    group('Answer Tests', () {
      test('Should create Answer with required values', () {
        final answer = Answer(audio: 'test_audio_url', text: 'Test text');

        expect(answer.audio, 'test_audio_url');
        expect(answer.text, 'Test text');
        expect(answer.isActive, false);
      });

      test('Should create Answer with optional values', () {
        final answer = Answer(
          audio: 'test_audio_url',
          text: 'Test text',
          isActive: true,
        );

        expect(answer.audio, 'test_audio_url');
        expect(answer.text, 'Test text');
        expect(answer.isActive, true);
      });

      test('fromJson should create Answer from JSON', () {
        final json = {
          'audio_url': 'test_audio_url',
          'text': 'Test text',
          'isActive': true,
        };

        final answer = Answer.fromJson(json);

        expect(answer.audio, 'test_audio_url');
        expect(answer.text, 'Test text');
        expect(answer.isActive, true);
      });

      test('toJson should convert Answer to JSON', () {
        final answer = Answer(
          audio: 'test_audio_url',
          text: 'Test text',
          isActive: true,
        );

        final json = answer.toJson();

        expect(json['audio_url'], 'test_audio_url');
        expect(json['text'], 'Test text');
        expect(json['isActive'], true);
      });
    });
  });
}
