// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listening_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listeningControllerHash() =>
    r'697a1ccb97e3c305b182df9be44862ff37ad96e9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ListeningController
    extends BuildlessAutoDisposeAsyncNotifier<ListeningState> {
  late final String level;
  late final String chapter;
  late final String path;

  FutureOr<ListeningState> build(String level, String chapter, String path);
}

/// See also [ListeningController].
@ProviderFor(ListeningController)
const listeningControllerProvider = ListeningControllerFamily();

/// See also [ListeningController].
class ListeningControllerFamily extends Family<AsyncValue<ListeningState>> {
  /// See also [ListeningController].
  const ListeningControllerFamily();

  /// See also [ListeningController].
  ListeningControllerProvider call(String level, String chapter, String path) {
    return ListeningControllerProvider(level, chapter, path);
  }

  @override
  ListeningControllerProvider getProviderOverride(
    covariant ListeningControllerProvider provider,
  ) {
    return call(provider.level, provider.chapter, provider.path);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listeningControllerProvider';
}

/// See also [ListeningController].
class ListeningControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          ListeningController,
          ListeningState
        > {
  /// See also [ListeningController].
  ListeningControllerProvider(String level, String chapter, String path)
    : this._internal(
        () =>
            ListeningController()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: listeningControllerProvider,
        name: r'listeningControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$listeningControllerHash,
        dependencies: ListeningControllerFamily._dependencies,
        allTransitiveDependencies:
            ListeningControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
        path: path,
      );

  ListeningControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
    required this.path,
  }) : super.internal();

  final String level;
  final String chapter;
  final String path;

  @override
  FutureOr<ListeningState> runNotifierBuild(
    covariant ListeningController notifier,
  ) {
    return notifier.build(level, chapter, path);
  }

  @override
  Override overrideWith(ListeningController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ListeningControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
        path: path,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ListeningController, ListeningState>
  createElement() {
    return _ListeningControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListeningControllerProvider &&
        other.level == level &&
        other.chapter == chapter &&
        other.path == path;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);
    hash = _SystemHash.combine(hash, path.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ListeningControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ListeningState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;

  /// The parameter `path` of this provider.
  String get path;
}

class _ListeningControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          ListeningController,
          ListeningState
        >
    with ListeningControllerRef {
  _ListeningControllerProviderElement(super.provider);

  @override
  String get level => (origin as ListeningControllerProvider).level;
  @override
  String get chapter => (origin as ListeningControllerProvider).chapter;
  @override
  String get path => (origin as ListeningControllerProvider).path;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
