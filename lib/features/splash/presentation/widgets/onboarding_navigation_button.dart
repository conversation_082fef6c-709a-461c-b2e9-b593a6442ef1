import 'package:flutter/material.dart';

class OnboardingNavigationButton extends StatelessWidget {
  const OnboardingNavigationButton({
    super.key,
    required this.onTap,
    required this.icon,
    required this.margin,
  });

  final VoidCallback onTap;
  final IconData icon;
  final EdgeInsets margin;

  static const _navigationButtonDecoration = BoxDecoration(
    shape: BoxShape.circle,
    color: Color(0x80FFB3AC),
  );

  static const _navigationButtonBorder = BorderSide(
    color: Color(0x80FFFFFF),
    width: 1.6,
  );

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: _navigationButtonDecoration.copyWith(
          border: Border.fromBorderSide(_navigationButtonBorder),
        ),
        margin: margin,
        height: 50,
        width: 50,
        child: Icon(icon, size: 46, color: const Color(0x80000000)),
      ),
    );
  }
}
