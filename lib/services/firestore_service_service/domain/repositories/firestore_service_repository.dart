import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

abstract class FirestoreServiceRepository {
  DocumentReference dataUser();
  FirebaseFirestore get fireStore;
  FirebaseStorage get firebaseStorage;
  FirebaseAuth get firebaseAuth;
  FirebaseFunctions get firebaseFunctions;
}

class FirestoreServiceRepositoryImpl extends FirestoreServiceRepository {
  final FirebaseFirestore _dataSource;
  final FirebaseAuth _firebaseAuth;
  final FirebaseStorage _firebaseStorage;
  final FirebaseFunctions _firebaseFunctions;

  FirestoreServiceRepositoryImpl(
    this._dataSource,
    this._firebaseAuth,
    this._firebaseStorage,
    this._firebaseFunctions,
  );

  @override
  DocumentReference dataUser() =>
      _dataSource.collection('user-data').doc(_firebaseAuth.currentUser?.uid);

  @override
  FirebaseFirestore get fireStore => _dataSource;

  @override
  FirebaseStorage get firebaseStorage => _firebaseStorage;

  @override
  FirebaseAuth get firebaseAuth => _firebaseAuth;

  @override
  FirebaseFunctions get firebaseFunctions => _firebaseFunctions;
}
