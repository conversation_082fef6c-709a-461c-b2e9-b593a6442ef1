import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Basic infrastructure test to verify the testing setup works
void main() {
  group('Testing Infrastructure', () {
    test('should be able to run basic tests', () {
      expect(1 + 1, equals(2));
    });

    test('should be able to test Either class', () {
      final left = Either<String, int>.left('error');
      final right = Either<String, int>.right(42);

      expect(left.isLeft(), isTrue);
      expect(left.isRight(), isFalse);
      expect(right.isLeft(), isFalse);
      expect(right.isRight(), isTrue);
    });

    test('should be able to test AppException', () {
      final exception = AppException(
        message: 'Test error',
        statusCode: 400,
        identifier: 'TEST_ERROR',
      );

      expect(exception.message, equals('Test error'));
      expect(exception.statusCode, equals(400));
      expect(exception.identifier, equals('TEST_ERROR'));
    });

    test('should be able to fold Either values', () {
      final left = Either<String, int>.left('error');
      final right = Either<String, int>.right(42);

      final leftResult = left.fold(
        (error) => 'Error: $error',
        (value) => 'Value: $value',
      );

      final rightResult = right.fold(
        (error) => 'Error: $error',
        (value) => 'Value: $value',
      );

      expect(leftResult, equals('Error: error'));
      expect(rightResult, equals('Value: 42'));
    });
  });
}
