// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCY9f2-jZfAlLtYbVG-NsMkzdVOT_SUKFE',
    appId: '1:432462853701:android:94918fb171d7f45c8bd52c',
    messagingSenderId: '432462853701',
    projectId: 'selfeng-dev',
    storageBucket: 'selfeng-dev.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA9s36vROrAkhGG4j1VW9THho5Md9LW07g',
    appId: '1:432462853701:ios:b4075526b2b45bf88bd52c',
    messagingSenderId: '432462853701',
    projectId: 'selfeng-dev',
    storageBucket: 'selfeng-dev.appspot.com',
    iosClientId:
        '432462853701-5ile5iak2643enic8cb5uncs0v4j9p9q.apps.googleusercontent.com',
    iosBundleId: 'com.idwebmobile.selfeng',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAnYAXx6c1tSPsBggjaRnAe41Bo6HQP8-w',
    appId: '1:432462853701:web:b3b0399a00a9a0d68bd52c',
    messagingSenderId: '432462853701',
    projectId: 'selfeng-dev',
    authDomain: 'selfeng-dev.firebaseapp.com',
    storageBucket: 'selfeng-dev.appspot.com',
    measurementId: 'G-83F98KGY33',
  );
}
