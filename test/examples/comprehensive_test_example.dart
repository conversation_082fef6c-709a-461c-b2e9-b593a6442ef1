import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

import '../helpers/test_helpers.dart';
import '../helpers/test_data_builders.dart';
import '../helpers/riverpod_test_utils.dart';
import '../helpers/firebase_test_helpers.dart';

/// Comprehensive test example demonstrating the testing infrastructure
///
/// This example shows how to use all the testing utilities together
/// to create robust, maintainable tests for the SelfEng application.
void main() {
  group('Comprehensive Testing Infrastructure Example', () {
    late ProviderContainer container;

    setUp(() {
      // Initialize test environment
      TestHelpers.setupTestEnvironment();

      // Create test container with overrides
      container = RiverpodTestUtils.createTestContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Model Testing Examples', () {
      test('should create and test UserData with builder pattern', () {
        // Using TestDataBuilders for consistent test data
        final userData =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withCompletedTest()
                .withAllLastCourses()
                .build();

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isTrue);
        expect(userData.lastPronunciation, isNotNull);
        expect(userData.lastConversation, isNotNull);
        expect(userData.lastListening, isNotNull);
        expect(userData.lastSpeaking, isNotNull);
      });

      test('should test LastCourse model with proper constructor', () {
        final testDate = DateTime.now();
        final lastCourse = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'test_path',
          partOrder: 1,
          subpartOrder: 2,
        );

        expect(lastCourse.level, equals('A1'));
        expect(lastCourse.chapter, equals(1));
        expect(lastCourse.section, equals(SectionType.pronunciation));
        expect(lastCourse.partOrder, equals(1));
        expect(lastCourse.subpartOrder, equals(2));
      });
    });

    group('Either Pattern Testing Examples', () {
      test('should handle success cases with Either', () {
        final userData = TestDataBuilders.userDataBuilder().build();
        final successResult = Either<AppException, UserData>.right(userData);

        expect(successResult.isRight(), isTrue);
        expect(successResult.isLeft(), isFalse);

        final result = successResult.fold(
          (error) => 'Error: ${error.message}',
          (user) => 'Success: ${user.email}',
        );

        expect(result, equals('Success: <EMAIL>'));
      });

      test('should handle error cases with Either', () {
        final error = AppException(
          message: 'Test error',
          statusCode: 400,
          identifier: 'TEST_ERROR',
        );
        final errorResult = Either<AppException, UserData>.left(error);

        expect(errorResult.isLeft(), isTrue);
        expect(errorResult.isRight(), isFalse);

        final result = errorResult.fold(
          (error) => 'Error: ${error.message}',
          (user) => 'Success: ${user.email}',
        );

        expect(result, equals('Error: Test error'));
      });
    });

    group('Firebase Testing Examples', () {
      test('should create test user with Firebase helpers', () {
        final testUser = FirebaseTestHelpers.createTestUser(
          uid: 'test_123',
          email: '<EMAIL>',
          displayName: 'Firebase Test User',
          emailVerified: true,
        );

        expect(testUser.uid, equals('test_123'));
        expect(testUser.email, equals('<EMAIL>'));
        expect(testUser.displayName, equals('Firebase Test User'));
        expect(testUser.emailVerified, isTrue);
      });

      test('should create test Firestore data', () {
        final lastCourseData = FirebaseTestHelpers.createTestLastCourse(
          level: 'B1',
          chapter: 2,
          section: 'conversation',
          partOrder: 3,
          subpartOrder: 4,
        );

        final userData = FirebaseTestHelpers.createTestUserData(
          email: '<EMAIL>',
          afterTest: true,
          lastPronunciation: lastCourseData,
        );

        expect(userData['email'], equals('<EMAIL>'));
        expect(userData['is_after_test'], isTrue);
        expect(userData['last_pronunciation'], isNotNull);

        final lastPronunciation =
            userData['last_pronunciation'] as Map<String, dynamic>;
        expect(lastPronunciation['level'], equals('B1'));
        expect(lastPronunciation['chapter'], equals(2));
        expect(lastPronunciation['partOrder'], equals(3));
      });

      test('should handle Firebase exceptions', () {
        final authException = FirebaseTestHelpers.createAuthException(
          code: 'user-not-found',
          message: 'User not found',
        );

        expect(authException.code, equals('user-not-found'));
        expect(authException.message, equals('User not found'));

        final firestoreException = FirebaseTestHelpers.createFirestoreException(
          code: 'permission-denied',
          message: 'Permission denied',
        );

        expect(firestoreException.code, equals('permission-denied'));
        expect(firestoreException.message, equals('Permission denied'));
      });
    });

    group('Async Testing Examples', () {
      test('should handle async operations with timeout', () async {
        // Simulate async operation
        Future<String> asyncOperation() async {
          await Future.delayed(Duration(milliseconds: 100));
          return 'Async result';
        }

        final result = await TestHelpers.waitForCondition(
          () => asyncOperation().then((value) => value == 'Async result'),
          timeout: Duration(seconds: 1),
        );

        expect(result, isTrue);
      });

      test('should test stream operations', () async {
        final testStream = Stream.fromIterable(['a', 'b', 'c']);
        final results = <String>[];

        await for (final value in testStream) {
          results.add(value);
        }

        expect(results, equals(['a', 'b', 'c']));
      });
    });

    group('Error Handling Examples', () {
      test('should handle and test exceptions properly', () {
        expect(
          () =>
              throw AppException(
                message: 'Test exception',
                statusCode: 500,
                identifier: 'TEST_EXCEPTION',
              ),
          throwsA(isA<AppException>()),
        );
      });

      test('should test error recovery patterns', () {
        String handleError(AppException error) {
          switch (error.identifier) {
            case 'NETWORK_ERROR':
              return 'Please check your internet connection';
            case 'AUTH_ERROR':
              return 'Please log in again';
            default:
              return 'An unexpected error occurred';
          }
        }

        final networkError = AppException(
          message: 'Network failed',
          statusCode: 0,
          identifier: 'NETWORK_ERROR',
        );

        final result = handleError(networkError);
        expect(result, equals('Please check your internet connection'));
      });
    });
  });
}
