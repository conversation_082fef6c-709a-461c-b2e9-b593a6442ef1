// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_completion_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$gameCompletionControllerHash() =>
    r'3cccd0665649d3161ab5187629d79b49a7913f74';

/// A provider to handle game completion state across different game screens
/// This replaces the need to return values through navigation
///
/// Copied from [GameCompletionController].
@ProviderFor(GameCompletionController)
final gameCompletionControllerProvider =
    NotifierProvider<GameCompletionController, bool>.internal(
      GameCompletionController.new,
      name: r'gameCompletionControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$gameCompletionControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GameCompletionController = Notifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
