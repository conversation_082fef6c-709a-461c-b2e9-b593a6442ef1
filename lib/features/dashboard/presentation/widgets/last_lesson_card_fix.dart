import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LastLessonCardFix extends StatelessWidget {
  final LastCourseInfo lastCourse;
  final int totalCourses;
  final Function() onTap;

  const LastLessonCardFix({
    super.key,
    required this.lastCourse,
    required this.totalCourses,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final cardWidth = totalCourses > 1 ? size.width * 0.85 : size.width * 0.95;
    final sectionTitle = _getSectionTitle(lastCourse.info.section, context);
    final routerName = _getRouterName(lastCourse.info);
    const cardHeight = 180.0;

    final params = {
      'level': lastCourse.info.level,
      'chapter': lastCourse.info.chapter.toString(),
      'path': base64Url.encode(utf8.encode(lastCourse.info.path)),
    };

    if (routerName == RouterName.speakingArenaStage) {
      params['stage'] = lastCourse.info.speakingStage.name;
    }

    return RepaintBoundary(
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            onTap();
            customNav(context, routerName, params: params);
          },
          borderRadius: BorderRadius.circular(8),
          child: _buildImageAndContent(
            context,
            cardWidth,
            cardHeight,
            sectionTitle,
          ),
        ),
      ),
    );
  }

  Widget _buildImageAndContent(
    BuildContext context,
    double width,
    double height,
    String sectionTitle,
  ) {
    final displayText =
        lastCourse.info.section == SectionType.pronunciation
            ? lastCourse.data.caption
            : lastCourse.data.title;

    Widget imageContent;
    final imageUrl = lastCourse.data.image;

    // Proactively check if the imageUrl is null or empty.
    // Trim to catch strings that are technically not empty but contain only whitespace.
    if (imageUrl == null || imageUrl.trim().isEmpty) {
      debugPrint(
        'Image URL is null or empty. Displaying placeholder directly.',
      );
      imageContent = _buildImageErrorPlaceholder(context);
    } else {
      imageContent = CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        // width and height for CachedNetworkImage are effectively set by its parent's constraints
        // when used within a Stack(fit: StackFit.expand) and a sized parent (SizedBox).
        // Explicitly setting them on CachedNetworkImage can sometimes be helpful for specific `fit` modes
        // or if the parent doesn't provide tight constraints, but here it should be fine.
        memCacheWidth: (width * 2).toInt(), // For caching strategy
        placeholder: (context, url) => const LoadingCircle(),
        errorWidget: (context, url, error) {
          // This will now primarily handle network errors or actual image loading failures
          // for valid (non-null, non-empty) URLs.
          debugPrint(
            'Failed to load image from CachedNetworkImage: $url, Error: $error',
          );
          return _buildImageErrorPlaceholder(context);
        },
      );
    }

    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Image Layer or Placeholder Layer with Clipping
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            // imageContent is now either CachedNetworkImage or our placeholder
            child: imageContent,
          ),
          // Darkening Overlay Layer (always present)
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.0),
              color: Colors.black.withValues(alpha: .3),
            ),
          ),
          // Text Content Layer (always present)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildContentColumn(sectionTitle, displayText),
          ),
        ],
      ),
    );
  }

  Widget _buildImageErrorPlaceholder(BuildContext context) {
    // This placeholder is designed to be a child of ClipRRect,
    // which is constrained by the SizedBox(width: width, height: height) via StackFit.expand.
    // The Container will therefore fill the available space defined by these constraints.
    return Container(
      // Using a specific color for the placeholder background.
      // This matches the "grey background" description and is intentional here.
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            color: Colors.grey[600],
            size: 48.0,
          ),
          const SizedBox(height: 8.0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              context.loc.imageNotAvailable,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[700], fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentColumn(String sectionTitle, String displayText) {
    const textShadow = [
      Shadow(blurRadius: 3.0, color: Colors.black45, offset: Offset(1.0, 1.0)),
    ];

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayText,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
            shadows: textShadow,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${lastCourse.info.level} - $sectionTitle',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white.withValues(alpha: .9),
            fontSize: 11,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _getSectionTitle(SectionType section, BuildContext context) {
    final loc = context.loc;
    return switch (section) {
      SectionType.pronunciation => loc.pronunciationChallenge,
      SectionType.conversation => loc.conversationVideo,
      SectionType.listening => loc.listeningMastery,
      SectionType.speaking => loc.speakingArena,
    };
  }

  String _getRouterName(dynamic info) {
    return switch (info.section as SectionType) {
      SectionType.pronunciation => RouterName.pronunciationChallenge,
      SectionType.conversation => RouterName.conversationVideo,
      SectionType.listening => RouterName.listeningMastery,
      SectionType.speaking =>
        info.speakingStage == SpeakingStage.stage1
            ? RouterName.speakingArena
            : RouterName.speakingArenaStage,
    };
  }
}
