import 'package:freezed_annotation/freezed_annotation.dart';

part 'default_model.freezed.dart';
part 'default_model.g.dart';

typedef DefaultModelList = List<DefaultModel>;

@freezed
sealed class DefaultModel with _$DefaultModel {
  factory DefaultModel({
    @Default('') String title,
    @Default('') String subtitle,
    @Default('') String image,
    @Default('') String route,
    @Default(false) bool isExpanded,
    Map<String, String>? params,
    @JsonKey(includeFromJson: false, includeToJson: false) child,
    onTap,
  }) = _DefaultModel;

  factory DefaultModel.fromJson(dynamic json) => _$DefaultModelFromJson(json);
}
