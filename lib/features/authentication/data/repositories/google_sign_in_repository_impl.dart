import 'dart:async';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';

class GoogleSignInRepositoryImpl implements GoogleSignInRepository {
  final GoogleSignIn _googleSignIn;

  GoogleSignInRepositoryImpl(this._googleSignIn);

  @override
  bool supportsAuthenticate() => _googleSignIn.supportsAuthenticate();

  @override
  Future<GoogleSignInAccount?> authenticate() => _googleSignIn.authenticate();

  @override
  Stream<GoogleSignInAuthenticationEvent> get authenticationEvents =>
      _googleSignIn.authenticationEvents;

  @override
  Future<void> disconnect() => _googleSignIn.disconnect();

  @override
  Future<void> initialize() => _googleSignIn.initialize();

  @override
  Future<void> signOut() => _googleSignIn.signOut();
}
