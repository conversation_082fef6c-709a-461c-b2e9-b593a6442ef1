import 'package:flutter/material.dart';

class PopUpTranslate extends StatelessWidget {
  final String caption;
  final bool closeButton;
  final VoidCallback? onTranslate;

  const PopUpTranslate({
    super.key,
    required this.caption,
    this.closeButton = false,
    this.onTranslate,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      backgroundColor: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.white),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(31), // 0.12 * 255 ≈ 31
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Text(
              caption,
              textAlign: TextAlign.left,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: const Color(0xffB4A9A7)),
            ),
          ),
          const SizedBox(height: 24),
          if (closeButton)
            SizedBox(
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff540005),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
