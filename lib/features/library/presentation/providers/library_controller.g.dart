// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$libraryControllerHash() => r'71436d730a200c3073b72c21a1abd06a2e275b05';

/// See also [LibraryController].
@ProviderFor(LibraryController)
final libraryControllerProvider =
    AutoDisposeAsyncNotifierProvider<LibraryController, LibraryState>.internal(
      LibraryController.new,
      name: r'libraryControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$libraryControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LibraryController = AutoDisposeAsyncNotifier<LibraryState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
