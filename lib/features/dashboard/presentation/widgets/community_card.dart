import 'package:flutter/material.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class CommunityCard extends StatelessWidget {
  const CommunityCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: const DecorationImage(
          image: AssetImage('$assetImageDashboard/header.png'),
          fit: BoxFit.cover,
        ),
        gradient: const LinearGradient(
          colors: [Color(0xffAF1A1D), Color(0xffDD0E22), Color(0xffFE754C)],
          begin: Alignment.bottomRight,
          end: Alignment.topLeft,
        ),
      ),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              width: 140,
              height: 126,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.loc.express_yourself_in_english,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
