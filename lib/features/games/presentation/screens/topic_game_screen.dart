// observe_recall_screen.dart
import 'dart:ui';

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:scaled_size/scaled_size.dart';
import 'package:selfeng/features/games/presentation/providers/game_completion_controller.dart';
import 'package:selfeng/features/games/presentation/providers/state/topic_game_state.dart';
import 'package:selfeng/features/games/presentation/providers/topic_game_controller.dart';
import 'package:selfeng/features/games/presentation/widgets/loading_screen.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class TopicGameScreen extends ConsumerStatefulWidget {
  const TopicGameScreen({super.key, required this.game});
  final String game;

  @override
  ConsumerState<TopicGameScreen> createState() => _TopicGameScreenState();
}

class _TopicGameScreenState extends ConsumerState<TopicGameScreen>
    with TickerProviderStateMixin {
  // State variables
  late AsyncValue<TopicGameState> _viewState;
  late TopicGameController _viewModel;
  late AsyncValue _viewTimerState;

  // Controllers and keys
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late final _pageController;
  final _player = AudioPlayer();

  // Flags and cache
  bool _isInitialized = false;
  bool _isAnimating = false;
  final Set<String> _precachedImages = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  @override
  void dispose() {
    _cleanupResources();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Always ensure landscape orientation on every build to prevent portrait flash
    _setLandscapeOrientation();

    _setupStateAndViewModel();
    _enableWakelock();
    _setupErrorListener();
    _setupGameCompletionListener();

    return _buildScreenBasedOnState();
  }

  // Initialization methods
  void _initializeScreen() {
    _setLandscapeOrientation();
  }

  void _setLandscapeOrientation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void _initializeOnFirstBuild() {
    if (!_isInitialized) {
      _isInitialized = true;
      _pageController = PageController(
        initialPage: _viewState.value!.currentTopicIndex,
      );
      _precacheImages();
    }
  }

  // Cleanup methods
  void _cleanupResources() {
    // Don't reset orientation here - it's handled explicitly in back button
    // This prevents orientation reset when returning from games
    _disposeAudioPlayer();
    WakelockPlus.disable();
  }

  void _resetPortraitOrientation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  void _disposeAudioPlayer() {
    _player.stop().then((_) => _player.dispose());
  }

  // State management
  void _setupStateAndViewModel() {
    final provider = topicGameControllerProvider;
    _viewState = ref.watch(provider);
    _viewModel = ref.watch(provider.notifier);
  }

  void _enableWakelock() {
    WakelockPlus.enable();
  }

  void _setupErrorListener() {
    ref.listen(topicGameControllerProvider.select((value) => value), (
      previous,
      next,
    ) {
      next.maybeWhen(
        error: (error, track) => _showErrorSnackBar(error),
        orElse: () {},
      );
    });
  }

  void _setupGameCompletionListener() {
    ref.listen(gameCompletionControllerProvider, (previous, next) {
      // When game is completed, automatically advance to next topic
      if (next == true) {
        _viewModel.nextTopic();
        // Reset the completion flag after advancing
        ref.read(gameCompletionControllerProvider.notifier).reset();
      }
    });
  }

  void _showErrorSnackBar(Object error) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(error.toString())));
  }

  // UI Building methods
  Widget _buildScreenBasedOnState() {
    return switch (_viewState) {
      AsyncData() => _hasValidTopics() ? _buildMainBody() : LoadingScreen(),
      AsyncError() => _buildMainBody(),
      AsyncLoading() => LoadingScreen(),
      _ => const Text('loading'),
    };
  }

  bool _hasValidTopics() {
    return _viewState.value?.topics.isNotEmpty ?? false;
  }

  Widget _buildMainBody() {
    return ScaledSize(
      builder: () => Scaffold(key: _scaffoldKey, body: _buildGameContainer()),
    );
  }

  Widget _buildGameContainer() {
    return Container(
      decoration: _buildBackgroundDecoration(),
      child: _buildBlurredContent(),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    final imageUrl = _getCurrentTopicImage();
    return BoxDecoration(
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFD84315), Color(0xFFFFA000)],
      ),
      image:
          imageUrl.isNotEmpty
              ? DecorationImage(
                image: CachedNetworkImageProvider(imageUrl),
                fit: BoxFit.fill,
                onError: (exception, stackTrace) {
                  debugPrint('Background image failed to load: $imageUrl');
                },
              )
              : null,
    );
  }

  String _getCurrentTopicImage() {
    final topics = _viewState.value?.topics;
    final currentIndex = _viewState.value?.currentTopicIndex ?? 0;
    return topics?[currentIndex].image ?? '';
  }

  Widget _buildBlurredContent() {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Container(
        color: Colors.grey.withValues(alpha: 0.1),
        alignment: Alignment.center,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 4.vw, vertical: 2.vh),
          child: Stack(children: [_buildMainContent(), _buildBackButton()]),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    if (_viewState.value?.gameStage == null) {
      return LoadingScreen();
    }
    return SafeArea(child: _buildTopicStage());
  }

  Widget _buildBackButton() {
    return SafeArea(
      child: InkWell(
        onTap: () {
          // Reset to portrait orientation before navigating back to game list
          _resetPortraitOrientation();
          context.pop();
        },
        child: Image.asset(
          '$assetImageGames/memory_flash/Navigate back Game.png',
          height: 2.rem,
          width: 2.rem,
        ),
      ),
    );
  }

  Widget _buildTopicStage() {
    _initializeOnFirstBuild();

    return Container(
      alignment: Alignment.center,
      child: Column(
        children: [
          _buildTitle(),
          const SizedBox(height: 20),
          _buildTopicCarousel(),
          SizedBox(height: 5.vh),
          _buildStartButton(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      context.loc.select_topic,
      style: Theme.of(
        context,
      ).textTheme.headlineLarge!.copyWith(color: Colors.white),
      textScaler: const TextScaler.linear(0.8),
    );
  }

  Widget _buildTopicCarousel() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildNavigationButton(
          icon: Icons.chevron_left_rounded,
          onTap: _animateToPreviousTopic,
        ),
        Expanded(child: _buildPageView()),
        _buildNavigationButton(
          icon: Icons.chevron_right_rounded,
          onTap: _animateToNextTopic,
        ),
      ],
    );
  }

  Widget _buildPageView() {
    return Container(
      height: 190,
      margin: EdgeInsets.symmetric(horizontal: 4.vw),
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        itemCount: _viewState.value?.topics.length ?? 0,
        itemBuilder: _buildTopicCard,
      ),
    );
  }

  void _onPageChanged(int index) {
    if (!_isAnimating) {
      // Handle page change if needed
    }
  }

  Widget _buildTopicCard(BuildContext context, int index) {
    return AnimatedBuilder(
      animation: _pageController,
      builder: (context, child) {
        final value = _calculateCardScale(index);
        return Transform.scale(
          scale: Curves.easeOut.transform(value),
          child: Opacity(opacity: value, child: _buildTopicCardContent(index)),
        );
      },
    );
  }

  double _calculateCardScale(int index) {
    double value = 1.0;
    if (_pageController.position.haveDimensions) {
      value = (_pageController.page ?? 0.0) - index;
      value = (1 - (value.abs() * 0.3)).clamp(0.0, 1.0);
    }
    return value;
  }

  Widget _buildTopicCardContent(int index) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(6.br)),
      child: Stack(
        fit: StackFit.expand,
        children: [_buildTopicImage(index), _buildTopicTitle(index)],
      ),
    );
  }

  Widget _buildTopicImage(int index) {
    final topic = _viewState.value?.topics[index];
    final imageUrl = topic?.image ?? '';

    return ClipRRect(
      borderRadius: BorderRadius.circular(6.br),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder:
            (child, animation) =>
                FadeTransition(opacity: animation, child: child),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          key: ValueKey(imageUrl),
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildImagePlaceholder(),
          errorWidget:
              (context, url, error) =>
                  _buildImageErrorWidget(context, error, null),
          fadeInDuration: const Duration(milliseconds: 300),
          fadeOutDuration: const Duration(milliseconds: 300),
          // Cache configuration
          maxHeightDiskCache: 1000,
          maxWidthDiskCache: 1000,
        ),
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      color: Colors.grey.withValues(alpha: 0.3),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ),
    );
  }

  Widget _buildImageErrorWidget(
    BuildContext context,
    Object error,
    StackTrace? stackTrace,
  ) {
    return Container(
      color: Colors.grey.withValues(alpha: 0.3),
      child: const Icon(
        Icons.image_not_supported,
        color: Colors.white,
        size: 50,
      ),
    );
  }

  Widget _buildTopicTitle(int index) {
    final topic = _viewState.value?.topics[index];
    return Align(
      alignment: Alignment.center,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 400),
        transitionBuilder:
            (child, animation) => SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.5),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(parent: animation, curve: Curves.elasticOut),
              ),
              child: FadeTransition(opacity: animation, child: child),
            ),
        child: Text(
          topic?.title ?? '',
          key: ValueKey(topic?.title),
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: const Offset(2.0, 2.0),
                blurRadius: 4.0,
                color: Colors.black.withValues(alpha: 0.8),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: ElevatedButton(
        onPressed: _onStartButtonPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          padding: EdgeInsets.symmetric(horizontal: 5.vw, vertical: 2.vh),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(3.br),
          ),
          elevation: 8,
        ),
        child: Text(
          context.loc.start,
          style: Theme.of(
            context,
          ).textTheme.titleMedium!.copyWith(color: Colors.white),
        ),
      ),
    );
  }

  void _onStartButtonPressed() async {
    final state = _viewState.value;
    if (state == null) return;
    final currentTopic = state.topics[state.currentTopicIndex];

    // Reset the completion flag before navigating
    ref.read(gameCompletionControllerProvider.notifier).reset();

    // Navigate to the game screen without expecting a return value
    // The game completion listener will automatically advance the topic when the game is completed
    await customNav(
      context,
      widget.game,
      params: {"topic": currentTopic.references ?? ''},
    );
  }

  Widget _buildNavigationButton({
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: () => _onNavigationButtonTap(onTap),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        decoration: BoxDecoration(
          color: const Color(0xffD0C4C2).withValues(alpha: 0.8),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 1.6),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        height: 3.rem,
        width: 3.rem,
        child: Icon(icon, size: 2.8.rem, color: Colors.white),
      ),
    );
  }

  void _onNavigationButtonTap(VoidCallback onTap) async {
    await _player.stop();
    _player.play(
      AssetSource('sounds/mixkit-player-jumping-in-a-video-game-2043.wav'),
    );
    onTap();
  }

  // Animation methods
  Future<void> _animateToNextTopic() async {
    if (_isAnimating || _viewState.value == null) return;

    _isAnimating = true;
    final state = _viewState.value!;
    final nextIndex = (state.currentTopicIndex + 1) % state.topics.length;

    await _animateToPage(nextIndex);
    _viewModel.nextTopic();
    _isAnimating = false;
  }

  Future<void> _animateToPreviousTopic() async {
    if (_isAnimating || _viewState.value == null) return;

    _isAnimating = true;
    final state = _viewState.value!;
    final previousIndex =
        state.currentTopicIndex == 0
            ? state.topics.length - 1
            : state.currentTopicIndex - 1;

    await _animateToPage(previousIndex);
    _viewModel.previousTopic();
    _isAnimating = false;
  }

  Future<void> _animateToPage(int index) async {
    await _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOutCubic,
    );
  }

  // Image caching
  Future<void> _precacheImages() async {
    final topics = _viewState.value?.topics;
    if (topics == null) return;

    final futures =
        topics
            .where((topic) => _shouldPrecacheImage(topic.image))
            .map((topic) => _precacheImage(topic.image!))
            .toList();

    await Future.wait(futures);
  }

  bool _shouldPrecacheImage(String? imageUrl) {
    return imageUrl != null &&
        imageUrl.isNotEmpty &&
        !_precachedImages.contains(imageUrl);
  }

  Future<void> _precacheImage(String imageUrl) async {
    try {
      await precacheImage(CachedNetworkImageProvider(imageUrl), context);
      _precachedImages.add(imageUrl);
    } catch (e) {
      debugPrint('Failed to precache image: $imageUrl');
    }
  }
}
