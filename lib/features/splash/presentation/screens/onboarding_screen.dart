import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/splash/presentation/widgets/onboarding_navigation_button.dart';
import 'package:selfeng/features/splash/presentation/widgets/onboarding_page.dart';
import 'package:selfeng/features/splash/presentation/widgets/signup_button.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late int _currentIndex;
  late Size _screenSize;
  late final List<DefaultModel> _onboardingPages;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _currentIndex = 0;
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (_tabController.index != _currentIndex) {
      setState(() {
        _currentIndex = _tabController.index;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _screenSize = MediaQuery.of(context).size;
    _onboardingPages = [
      DefaultModel(
        title: context.loc.onboarding1,
        image: '$assetImageOnboarding/Frame 95.png',
      ),
      DefaultModel(
        title: context.loc.onboarding2,
        image: '$assetImageOnboarding/Frame 96.png',
      ),
      DefaultModel(
        title: context.loc.onboarding3,
        image: '$assetImageOnboarding/Frame 97.png',
      ),
    ];
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children:
                _onboardingPages
                    .map(
                      (item) => OnboardingPage(
                        item: item,
                        screenWidth: _screenSize.width,
                      ),
                    )
                    .toList(),
          ),
          Positioned(
            bottom: 60,
            child: Column(
              children: [
                SizedBox(
                  width: _screenSize.width,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OnboardingNavigationButton(
                        onTap: () {
                          if (_currentIndex > 0) {
                            setState(() => _currentIndex--);
                            _tabController.animateTo(_currentIndex);
                          }
                        },
                        icon: Icons.chevron_left_rounded,
                        margin: const EdgeInsets.only(left: 41),
                      ),
                      OnboardingNavigationButton(
                        onTap: () {
                          if (_currentIndex < 2) {
                            setState(() => _currentIndex++);
                            _tabController.animateTo(_currentIndex);
                          }
                        },
                        icon: Icons.chevron_right_rounded,
                        margin: const EdgeInsets.only(right: 41),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 26),
                if (_currentIndex == 2) SignupButton(width: _screenSize.width),
              ],
            ),
          ),
          Positioned(
            bottom: 26,
            left: 0,
            right: 0,
            child: Center(child: VersionDisplay(textColor: Colors.white)),
          ),
        ],
      ),
    );
  }
}
