// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QuestionModel _$QuestionModelFromJson(Map<String, dynamic> json) =>
    _QuestionModel(
      questionId: json['question_id'] as String?,
      order: (json['order'] as num?)?.toInt() ?? 0,
      question: json['question'] as String? ?? '',
      image: json['image_url'] as String? ?? '',
      isActive: json['is_active'] ?? true,
      choices:
          (json['choices'] as List<dynamic>?)?.map(Choice.fromJson).toList() ??
          const [],
    );

Map<String, dynamic> _$QuestionModelToJson(_QuestionModel instance) =>
    <String, dynamic>{
      'question_id': instance.questionId,
      'order': instance.order,
      'question': instance.question,
      'image_url': instance.image,
      'is_active': instance.isActive,
      'choices': instance.choices,
    };

_Choice _$ChoiceFromJson(Map<String, dynamic> json) => _Choice(
  text: json['text'] as String? ?? '',
  value: json['value'] as String? ?? '',
  isCorrect: json['is_correct'] as bool? ?? false,
);

Map<String, dynamic> _$ChoiceToJson(_Choice instance) => <String, dynamic>{
  'text': instance.text,
  'value': instance.value,
  'is_correct': instance.isCorrect,
};

_QuestionResultModel _$QuestionResultModelFromJson(Map<String, dynamic> json) =>
    _QuestionResultModel(
      point: (json['point'] as num?)?.toInt() ?? 0,
      maxValue: (json['maxValue'] as num?)?.toInt() ?? 0,
      minValue: (json['minValue'] as num?)?.toInt() ?? 0,
      level: json['level'] as Map<String, dynamic>?,
      description: json['description'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestionResultModelToJson(
  _QuestionResultModel instance,
) => <String, dynamic>{
  'point': instance.point,
  'maxValue': instance.maxValue,
  'minValue': instance.minValue,
  'level': instance.level,
  'description': instance.description,
};
