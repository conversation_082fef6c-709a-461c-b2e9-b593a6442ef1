import 'package:freezed_annotation/freezed_annotation.dart';

part 'chapter_data.freezed.dart';
part 'chapter_data.g.dart';

@freezed
sealed class ChapterData with _$ChapterData {
  const factory ChapterData({
    @J<PERSON><PERSON><PERSON>(name: 'chapter') required int chapter,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'label') required String label,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'description') required ChapterDescription description,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'image') required String image,
    @<PERSON>son<PERSON><PERSON>(name: 'image_url') required String imageUrl,
  }) = _ChapterData;

  factory ChapterData.fromJson(Map<String, dynamic> json) =>
      _$ChapterDataFromJson(json);
}

@freezed
sealed class ChapterDescription with _$ChapterDescription {
  const factory ChapterDescription({
    @J<PERSON><PERSON><PERSON>(name: 'en') required String en,
    @J<PERSON><PERSON><PERSON>(name: 'id') required String id,
  }) = _ChapterDescription;

  factory ChapterDescription.fromJson(Map<String, dynamic> json) =>
      _$ChapterDescriptionFromJson(json);
}
