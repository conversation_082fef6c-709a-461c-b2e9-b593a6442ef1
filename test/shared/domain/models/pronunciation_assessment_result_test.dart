import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_assessment_result.dart';

void main() {
  group('PronunciationAssessmentResult Tests', () {
    group('Constructor and Properties', () {
      test(
        'should create PronunciationAssessmentResult with all required properties',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          final phonemeAssessment = PhonemePronunciationAssessment(
            accuracyScore: 90.0,
          );

          final wordAssessment = WordPronunciationAssessment(
            errorType: 'None',
            accuracyScore: 95.0,
            feedback: Feedback(
              prosody: Prosody(
                prosodyBreak: Break(errorTypes: [], breakLength: 0),
                intonation: Intonation(
                  errorTypes: [],
                  monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
                ),
              ),
            ),
          );

          final phoneme = PronunciationAssessmentPhoneme(
            phoneme: 'h',
            pronunciationAssessment: phonemeAssessment,
          );

          final word = PronunciationAssessmentWord(
            word: 'hello',
            pronunciationAssessment: wordAssessment,
            phonemes: [phoneme],
          );

          final priv = PronunciationAssessmentPriv(
            confidence: 0.95,
            lexical: 'hello world',
            itn: 'hello world',
            maskedItn: 'hello world',
            display: 'Hello world',
            pronunciationAssessment: pronunciationAssessment,
            words: [word],
          );

          final data = PronunciationAssessmentResultData(privPronJson: priv);

          final result = PronunciationAssessmentResult(result: data);

          expect(result.result, equals(data));
          expect(result.result.privPronJson.display, equals('Hello world'));
          expect(
            result.result.privPronJson.pronunciationAssessment.accuracyScore,
            equals(95.0),
          );
        },
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);

        final result = PronunciationAssessmentResult(result: data);

        final json = result.toJson();
        expect(json, isA<Map<String, dynamic>>());
        expect(json['result'], isNotNull);
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'result': {
            'privPronJson': {
              'Confidence': 0.95,
              'Lexical': 'hello world',
              'ITN': 'hello world',
              'MaskedITN': 'hello world',
              'Display': 'Hello world',
              'PronunciationAssessment': {
                'AccuracyScore': 95.0,
                'FluencyScore': 90.0,
                'ProsodyScore': 85.0,
                'CompletenessScore': 100.0,
                'PronScore': 92.5,
              },
              'Words': [
                {
                  'Word': 'hello',
                  'PronunciationAssessment': {
                    'ErrorType': 'None',
                    'AccuracyScore': 95.0,
                    'Feedback': {
                      'Prosody': {
                        'Break': {'ErrorTypes': [], 'BreakLength': 0},
                        'Intonation': {
                          'ErrorTypes': [],
                          'Monotone': {'SyllablePitchDeltaConfidence': 0.0},
                        },
                      },
                    },
                  },
                  'Phonemes': [
                    {
                      'Phoneme': 'h',
                      'PronunciationAssessment': {'AccuracyScore': 90.0},
                    },
                  ],
                },
              ],
            },
          },
        };

        final result = PronunciationAssessmentResult.fromJson(json);
        expect(result.result.privPronJson.display, equals('Hello world'));
        expect(
          result.result.privPronJson.pronunciationAssessment.accuracyScore,
          equals(95.0),
        );
        expect(result.result.privPronJson.words.length, equals(1));
        expect(result.result.privPronJson.words[0].word, equals('hello'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv1 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final priv2 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data1 = PronunciationAssessmentResultData(privPronJson: priv1);

        final data2 = PronunciationAssessmentResultData(privPronJson: priv2);

        final result1 = PronunciationAssessmentResult(result: data1);

        final result2 = PronunciationAssessmentResult(result: data2);

        // Note: Since these are freezed classes, we need to compare the JSON representations
        expect(result1.toJson(), equals(result2.toJson()));
      });
    });
  });
}
