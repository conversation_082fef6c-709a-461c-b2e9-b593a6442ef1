import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import '../mocks/http_mocks.dart';

void main() {
  group('HttpMocks Utility Tests', () {
    late Dio dio;
    late DioAdapter dioAdapter;

    setUp(() {
      dio = HttpMocks.createMockDio();
      dioAdapter = HttpMocks.createDioAdapter(dio);
    });

    group('GET Request Mocks', () {
      test('should mock successful GET request', () async {
        // Arrange
        const path = '/api/users';
        final responseData = {
          'users': [
            {'id': 1, 'name': '<PERSON>'},
            {'id': 2, 'name': '<PERSON>'},
          ],
          'total': 2,
        };

        HttpMocks.setupGetSuccess(dioAdapter, path, responseData);

        // Act
        final response = await dio.get(path);

        // Assert
        expect(response.statusCode, equals(200));
        expect(response.data, equals(responseData));
        expect(response.data['users'], isA<List>());
        expect(response.data['users'].length, equals(2));
      });

      test('should mock GET request with query parameters', () async {
        // Arrange
        const path = '/api/search';
        final queryParameters = {'q': 'flutter', 'limit': 10};
        final responseData = {
          'results': [
            {'id': 1, 'title': 'Flutter Development'},
          ],
          'total': 1,
        };

        dioAdapter.onGet(
          path,
          queryParameters: queryParameters,
          (server) => server.reply(200, responseData),
        );

        // Act
        final response = await dio.get(path, queryParameters: queryParameters);

        // Assert
        expect(response.statusCode, equals(200));
        expect(response.data['results'].length, equals(1));
        expect(
          response.data['results'][0]['title'],
          equals('Flutter Development'),
        );
      });
    });

    group('POST Request Mocks', () {
      test('should mock successful POST request', () async {
        // Arrange
        const path = '/api/users';
        final requestData = {'name': 'New User', 'email': '<EMAIL>'};
        final responseData = {
          'id': 3,
          'name': 'New User',
          'email': '<EMAIL>',
          'createdAt': '2023-01-01',
        };

        HttpMocks.setupPostSuccess(
          dioAdapter,
          path,
          responseData,
          requestData: requestData,
        );

        // Act
        final response = await dio.post(path, data: requestData);

        // Assert
        expect(response.statusCode, equals(201));
        expect(response.data['id'], equals(3));
        expect(response.data['name'], equals('New User'));
      });
    });

    group('Error Scenarios', () {
      test('should mock network error', () async {
        // Arrange
        const path = '/api/users';
        HttpMocks.setupNetworkError(dioAdapter, path);

        // Act & Assert
        expect(() => dio.get(path), throwsA(isA<DioException>()));
      });

      test('should mock server error (500)', () async {
        // Arrange
        const path = '/api/users';
        HttpMocks.setupServerError(dioAdapter, path, statusCode: 500);

        // Act
        try {
          await dio.get(path);
          fail('Expected DioException to be thrown');
        } on DioException catch (e) {
          // Assert
          expect(e.response?.statusCode, equals(500));
          expect(e.response?.data['error'], isNotNull);
        }
      });

      test('should mock client error (400)', () async {
        // Arrange
        const path = '/api/users';
        HttpMocks.setupClientError(dioAdapter, path, statusCode: 400);

        // Act
        try {
          await dio.get(path);
          fail('Expected DioException to be thrown');
        } on DioException catch (e) {
          // Assert
          expect(e.response?.statusCode, equals(400));
          expect(e.response?.data['error'], isNotNull);
        }
      });

      test('should mock unauthorized error (401)', () async {
        // Arrange
        const path = '/api/users';
        HttpMocks.setupUnauthorizedError(dioAdapter, path);

        // Act
        try {
          await dio.get(path);
          fail('Expected DioException to be thrown');
        } on DioException catch (e) {
          // Assert
          expect(e.response?.statusCode, equals(401));
          expect(e.response?.data['error'], equals('Unauthorized'));
        }
      });

      test('should mock not found error (404)', () async {
        // Arrange
        const path = '/api/nonexistent';
        HttpMocks.setupNotFoundError(dioAdapter, path);

        // Act
        try {
          await dio.get(path);
          fail('Expected DioException to be thrown');
        } on DioException catch (e) {
          // Assert
          expect(e.response?.statusCode, equals(404));
          expect(e.response?.data['error'], equals('Not found'));
        }
      });
    });

    // group('HTTP Mock Scenarios', () {
    //   test('should setup complete API success scenario', () async {
    //     // Arrange
    //     const baseUrl = 'https://api.example.com';
    //     HttpMockScenarios.setupApiSuccessScenario(dioAdapter, baseUrl: baseUrl);

    //     // Act - Test authentication endpoint
    //     final authResponse = await dio.post('${baseUrl}/auth/login', data: {
    //       'email': '<EMAIL>',
    //       'password': 'password123',
    //     });

    //     // Assert
    //     expect(authResponse.statusCode, equals(200));
    //     expect(authResponse.data['token'], isNotNull);
    //     expect(authResponse.data['user']['email'], equals('<EMAIL>'));

    //     // Act - Test user profile endpoint
    //     final profileResponse = await dio.get('${baseUrl}/user/profile');

    //     // Assert
    //     expect(profileResponse.statusCode, equals(200));
    //     expect(profileResponse.data['email'], equals('<EMAIL>'));
    //   });

    //   test('should setup network failure scenario', () async {
    //     // Arrange
    //     const baseUrl = 'https://api.example.com';
    //     HttpMockScenarios.setupNetworkFailureScenario(dioAdapter, baseUrl: baseUrl);

    //     // Act & Assert
    //     expect(
    //       () => dio.post('${baseUrl}/auth/login', data: {
    //         'email': '<EMAIL>',
    //         'password': 'password123',
    //       }),
    //       throwsA(isA<DioException>()),
    //     );
    //   });
    // });

    // group('Pronunciation API Mocks', () {
    //   test('should mock pronunciation check endpoint', () async {
    //     // Arrange
    //     const baseUrl = 'https://api.example.com';
    //     HttpMockScenarios.setupPronunciationApiMocks(dioAdapter, baseUrl: baseUrl);

    //     // Act
    //     final response = await dio.post('${baseUrl}/pronunciation/check', data: {
    //       'text': 'Hello world',
    //       'audio': 'base64_encoded_audio_data',
    //     });

    //     // Assert
    //     expect(response.statusCode, equals(200));
    //     expect(response.data['score'], isNotNull);
    //     expect(response.data['feedback'], isNotNull);
    //   });
    // });
  });
}
