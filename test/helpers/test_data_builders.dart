import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart'
    as main_lesson;
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart'
    as diagnostic;
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Test data builders for creating consistent test data across the project
/// Uses the Builder pattern to create flexible test data objects

class TestDataBuilders {
  TestDataBuilders._();

  /// Creates a UserData builder
  static UserDataBuilder userDataBuilder() => UserDataBuilder();

  /// Creates a ChapterData builder
  static ChapterDataBuilder chapterDataBuilder() => ChapterDataBuilder();

  /// Creates a MainLesson builder
  static MainLessonBuilder mainLessonBuilder() => MainLessonBuilder();

  /// Creates a QuestionModel builder
  static QuestionModelBuilder questionModelBuilder() => QuestionModelBuilder();

  /// Creates an AppException builder
  static AppExceptionBuilder appExceptionBuilder() => AppExceptionBuilder();

  /// Creates a Level builder
  static LevelBuilder levelBuilder() => LevelBuilder();

  /// Creates a PronunciationResultFormatted builder
  static PronunciationResultBuilder pronunciationResultBuilder() =>
      PronunciationResultBuilder();
}

/// Builder for UserData test objects
class UserDataBuilder {
  String _email = '<EMAIL>';
  bool _afterTest = false;
  LastCourse? _lastPronunciation;
  LastCourse? _lastConversation;
  LastCourse? _lastListening;
  LastCourse? _lastSpeaking;

  UserDataBuilder withEmail(String email) {
    _email = email;
    return this;
  }

  UserDataBuilder withAfterTest(bool afterTest) {
    _afterTest = afterTest;
    return this;
  }

  UserDataBuilder withLastPronunciation(LastCourse? lastPronunciation) {
    _lastPronunciation = lastPronunciation;
    return this;
  }

  UserDataBuilder withLastConversation(LastCourse? lastConversation) {
    _lastConversation = lastConversation;
    return this;
  }

  UserDataBuilder withLastListening(LastCourse? lastListening) {
    _lastListening = lastListening;
    return this;
  }

  UserDataBuilder withLastSpeaking(LastCourse? lastSpeaking) {
    _lastSpeaking = lastSpeaking;
    return this;
  }

  UserDataBuilder withCompletedTest() {
    _afterTest = true;
    return this;
  }

  UserDataBuilder withAllLastCourses() {
    final now = DateTime.now();
    _lastPronunciation = LastCourse(
      accessTime: now,
      level: 'beginner',
      chapter: 1,
      section: SectionType.pronunciation,
      path: 'path1',
    );
    _lastConversation = LastCourse(
      accessTime: now,
      level: 'beginner',
      chapter: 1,
      section: SectionType.conversation,
      path: 'path1',
    );
    _lastListening = LastCourse(
      accessTime: now,
      level: 'beginner',
      chapter: 1,
      section: SectionType.listening,
      path: 'path1',
    );
    _lastSpeaking = LastCourse(
      accessTime: now,
      level: 'beginner',
      chapter: 1,
      section: SectionType.speaking,
      path: 'path1',
    );
    return this;
  }

  UserData build() {
    return UserData(
      email: _email,
      afterTest: _afterTest,
      lastPronunciation: _lastPronunciation,
      lastConversation: _lastConversation,
      lastListening: _lastListening,
      lastSpeaking: _lastSpeaking,
    );
  }
}

/// Builder for ChapterData test objects
class ChapterDataBuilder {
  int _chapter = 1;
  String _label = 'Test Chapter';
  ChapterDescription _description = const ChapterDescription(
    en: 'Test description',
    id: 'Deskripsi test',
  );
  String _image = 'test_image.png';
  String _imageUrl = 'https://example.com/test_image.png';

  ChapterDataBuilder withChapter(int chapter) {
    _chapter = chapter;
    return this;
  }

  ChapterDataBuilder withLabel(String label) {
    _label = label;
    return this;
  }

  ChapterDataBuilder withDescription(ChapterDescription description) {
    _description = description;
    return this;
  }

  ChapterDataBuilder withImage(String image) {
    _image = image;
    return this;
  }

  ChapterDataBuilder withImageUrl(String imageUrl) {
    _imageUrl = imageUrl;
    return this;
  }

  ChapterDataBuilder withEnglishDescription(String description) {
    _description = ChapterDescription(en: description, id: _description.id);
    return this;
  }

  ChapterDataBuilder withIndonesianDescription(String description) {
    _description = ChapterDescription(en: _description.en, id: description);
    return this;
  }

  ChapterData build() {
    return ChapterData(
      chapter: _chapter,
      label: _label,
      description: _description,
      image: _image,
      imageUrl: _imageUrl,
    );
  }
}

/// Builder for MainLesson test objects
class MainLessonBuilder {
  main_lesson.QuestionConcreteState _state =
      main_lesson.QuestionConcreteState.initial;
  String? _questionId = 'test_question_1';
  int _order = 0;
  String _correctAnswer = 'correct answer';
  String _question = 'test question';
  String _answer = '';
  bool _isCorrect = false;

  MainLessonBuilder withState(main_lesson.QuestionConcreteState state) {
    _state = state;
    return this;
  }

  MainLessonBuilder withQuestionId(String? questionId) {
    _questionId = questionId;
    return this;
  }

  MainLessonBuilder withOrder(int order) {
    _order = order;
    return this;
  }

  MainLessonBuilder withCorrectAnswer(String correctAnswer) {
    _correctAnswer = correctAnswer;
    return this;
  }

  MainLessonBuilder withQuestion(String question) {
    _question = question;
    return this;
  }

  MainLessonBuilder withAnswer(String answer) {
    _answer = answer;
    return this;
  }

  MainLessonBuilder withIsCorrect(bool isCorrect) {
    _isCorrect = isCorrect;
    return this;
  }

  MainLessonBuilder asCorrectAnswer() {
    _answer = _correctAnswer;
    _isCorrect = true;
    _state = main_lesson.QuestionConcreteState.answered;
    return this;
  }

  MainLessonBuilder asIncorrectAnswer() {
    _answer = 'wrong answer';
    _isCorrect = false;
    _state = main_lesson.QuestionConcreteState.answered;
    return this;
  }

  main_lesson.MainLesson build() {
    return main_lesson.MainLesson(
      state: _state,
      questionId: _questionId,
      order: _order,
      correctAnswer: _correctAnswer,
      question: _question,
      answer: _answer,
      isCorrect: _isCorrect,
    );
  }
}

/// Builder for QuestionModel test objects
class QuestionModelBuilder {
  String? _questionId = 'test_question_1';
  int _order = 0;
  String _question = 'Test question?';
  String _image = '';
  bool _isActive = true;
  List<diagnostic.Choice> _choices = [];
  diagnostic.QuestionConcreteState _state =
      diagnostic.QuestionConcreteState.initial;
  bool _isCorrect = false;
  diagnostic.Choice? _answer;

  QuestionModelBuilder withQuestionId(String? questionId) {
    _questionId = questionId;
    return this;
  }

  QuestionModelBuilder withOrder(int order) {
    _order = order;
    return this;
  }

  QuestionModelBuilder withQuestion(String question) {
    _question = question;
    return this;
  }

  QuestionModelBuilder withImage(String image) {
    _image = image;
    return this;
  }

  QuestionModelBuilder withIsActive(bool isActive) {
    _isActive = isActive;
    return this;
  }

  QuestionModelBuilder withChoices(List<diagnostic.Choice> choices) {
    _choices = choices;
    return this;
  }

  QuestionModelBuilder withState(diagnostic.QuestionConcreteState state) {
    _state = state;
    return this;
  }

  QuestionModelBuilder withIsCorrect(bool isCorrect) {
    _isCorrect = isCorrect;
    return this;
  }

  QuestionModelBuilder withAnswer(diagnostic.Choice? answer) {
    _answer = answer;
    return this;
  }

  QuestionModelBuilder withMultipleChoices() {
    _choices = [
      diagnostic.Choice(text: 'Option A', value: '1', isCorrect: true),
      diagnostic.Choice(text: 'Option B', value: '2', isCorrect: false),
      diagnostic.Choice(text: 'Option C', value: '3', isCorrect: false),
      diagnostic.Choice(text: 'Option D', value: '4', isCorrect: false),
    ];
    return this;
  }

  diagnostic.QuestionModel build() {
    return diagnostic.QuestionModel(
      questionId: _questionId,
      order: _order,
      question: _question,
      image: _image,
      isActive: _isActive,
      choices: _choices,
      state: _state,
      isCorrect: _isCorrect,
      answer: _answer,
    );
  }
}

/// Builder for AppException test objects
class AppExceptionBuilder {
  String _message = 'Test error message';
  int _statusCode = 500;
  String _identifier = 'test_error';

  AppExceptionBuilder withMessage(String message) {
    _message = message;
    return this;
  }

  AppExceptionBuilder withStatusCode(int statusCode) {
    _statusCode = statusCode;
    return this;
  }

  AppExceptionBuilder withIdentifier(String identifier) {
    _identifier = identifier;
    return this;
  }

  AppExceptionBuilder asNetworkError() {
    _message = 'Network connection failed';
    _statusCode = 0;
    _identifier = 'network_error';
    return this;
  }

  AppExceptionBuilder asServerError() {
    _message = 'Internal server error';
    _statusCode = 500;
    _identifier = 'server_error';
    return this;
  }

  AppExceptionBuilder asUnauthorizedError() {
    _message = 'Unauthorized access';
    _statusCode = 401;
    _identifier = 'unauthorized_error';
    return this;
  }

  AppException build() {
    return AppException(
      message: _message,
      statusCode: _statusCode,
      identifier: _identifier,
    );
  }
}

/// Builder for Level test objects
class LevelBuilder {
  Level _level = Level.a1;

  LevelBuilder withLevel(Level level) {
    _level = level;
    return this;
  }

  LevelBuilder asBeginner() {
    _level = Level.a1;
    return this;
  }

  LevelBuilder asIntermediate() {
    _level = Level.b1;
    return this;
  }

  LevelBuilder asAdvanced() {
    _level = Level.c1;
    return this;
  }

  Level build() {
    return _level;
  }
}

/// Builder for PronunciationResultFormatted test objects
class PronunciationResultBuilder {
  String _originalText = 'Hello world';
  String _recordedInput = 'Hello world';
  double _accuracyScore = 85.0;
  double _fluencyScore = 80.0;
  double _prosodyScore = 75.0;
  double _completenessScore = 90.0;
  double _pronScore = 82.5;
  List<FormattedResult> _formattedResult = [];

  PronunciationResultBuilder withOriginalText(String originalText) {
    _originalText = originalText;
    return this;
  }

  PronunciationResultBuilder withRecordedInput(String recordedInput) {
    _recordedInput = recordedInput;
    return this;
  }

  PronunciationResultBuilder withAccuracyScore(double score) {
    _accuracyScore = score;
    return this;
  }

  PronunciationResultBuilder withFormattedResult(List<FormattedResult> result) {
    _formattedResult = result;
    return this;
  }

  PronunciationResultBuilder asExcellent() {
    _accuracyScore = 95.0;
    _fluencyScore = 95.0;
    _prosodyScore = 95.0;
    _completenessScore = 95.0;
    _pronScore = 95.0;
    return this;
  }

  PronunciationResultBuilder asPoor() {
    _accuracyScore = 45.0;
    _fluencyScore = 40.0;
    _prosodyScore = 35.0;
    _completenessScore = 50.0;
    _pronScore = 42.5;
    return this;
  }

  PronunciationResultFormatted build() {
    return PronunciationResultFormatted(
      originalText: _originalText,
      recordedInput: _recordedInput,
      accuracyScore: _accuracyScore,
      fluencyScore: _fluencyScore,
      prosodyScore: _prosodyScore,
      completenessScore: _completenessScore,
      pronScore: _pronScore,
      formattedResult: _formattedResult,
    );
  }
}
