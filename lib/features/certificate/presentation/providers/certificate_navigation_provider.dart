import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/certificate.dart';
import '../../domain/entities/certificate_level.dart';

part 'certificate_navigation_provider.g.dart';

/// State class to hold certificate navigation data
class CertificateNavigationData {
  final CertificateLevel level;
  final List<Certificate> certificates;
  final String? selectedCertificateId;

  const CertificateNavigationData({
    required this.level,
    required this.certificates,
    this.selectedCertificateId,
  });

  CertificateNavigationData copyWith({
    CertificateLevel? level,
    List<Certificate>? certificates,
    String? selectedCertificateId,
  }) {
    return CertificateNavigationData(
      level: level ?? this.level,
      certificates: certificates ?? this.certificates,
      selectedCertificateId:
          selectedCertificateId ?? this.selectedCertificateId,
    );
  }
}

/// Provider to manage certificate navigation data
/// This allows passing complex objects through GoRouter by storing them in state
@Riverpod(keepAlive: true)
class CertificateNavigationController
    extends _$CertificateNavigationController {
  @override
  CertificateNavigationData? build() {
    return null;
  }

  /// Set the certificate data for navigation
  void setCertificateData({
    required CertificateLevel level,
    required List<Certificate> certificates,
    String? selectedCertificateId,
  }) {
    state = CertificateNavigationData(
      level: level,
      certificates: certificates,
      selectedCertificateId: selectedCertificateId,
    );
  }

  /// Update the selected certificate ID while preserving other data
  void setSelectedCertificate(String certificateId) {
    if (state != null) {
      state = state!.copyWith(selectedCertificateId: certificateId);
    }
  }

  /// Clear the certificate data
  void clearCertificateData() {
    state = null;
  }

  /// Get certificate data for a specific level name
  CertificateNavigationData? getCertificateDataForLevel(String levelName) {
    if (state != null && state!.level.name == levelName) {
      return state;
    }
    return null;
  }
}
