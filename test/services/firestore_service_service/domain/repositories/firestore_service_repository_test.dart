import 'package:cloud_functions/cloud_functions.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import '../../../../mocks/firebase_mocks.dart';

// Additional mock classes for this test
class MockFirebaseFunctions extends Mock implements FirebaseFunctions {}

void main() {
  group('FirestoreServiceRepositoryImpl', () {
    late FirestoreServiceRepositoryImpl repository;
    late FakeFirebaseFirestore fakeFirestore;
    late MockFirebaseAuth mockAuth;
    late MockFirebaseStorage mockStorage;
    late MockFirebaseFunctions mockFunctions;
    late MockUser mockUser;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      mockAuth = MockFirebaseAuth();
      mockStorage = MockFirebaseStorage();
      mockFunctions = MockFirebaseFunctions();
      mockUser = MockUser();

      repository = FirestoreServiceRepositoryImpl(
        fakeFirestore,
        mockAuth,
        mockStorage,
        mockFunctions,
      );

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
    });

    group('dataUser', () {
      test('should return document reference for authenticated user', () {
        // Arrange
        when(() => mockAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('test_user_id');

        // Act
        final result = repository.dataUser();

        // Assert
        expect(result.path, equals('user-data/test_user_id'));
        verify(() => mockAuth.currentUser).called(1);
        verify(() => mockUser.uid).called(1);
      });

      test(
        'should return document reference with generated id when user not authenticated',
        () {
          // Arrange
          when(() => mockAuth.currentUser).thenReturn(null);

          // Act
          final result = repository.dataUser();

          // Assert
          expect(result.parent.path, equals('user-data'));
          expect(
            result.id.isNotEmpty,
            isTrue,
          ); // doc() with null generates an id
          verify(() => mockAuth.currentUser).called(1);
        },
      );
    });

    group('fireStore', () {
      test('should return the Firestore instance', () {
        // Act
        final result = repository.fireStore;

        // Assert
        expect(identical(result, fakeFirestore), isTrue);
      });
    });

    group('firebaseStorage', () {
      test('should return the Firebase Storage instance', () {
        // Act
        final result = repository.firebaseStorage;

        // Assert
        expect(result, equals(mockStorage));
      });
    });

    group('firebaseAuth', () {
      test('should return the Firebase Auth instance', () {
        // Act
        final result = repository.firebaseAuth;

        // Assert
        expect(result, equals(mockAuth));
      });
    });

    group('firebaseFunctions', () {
      test('should return the Firebase Functions instance', () {
        // Act
        final result = repository.firebaseFunctions;

        // Assert
        expect(result, equals(mockFunctions));
      });
    });
  });
}
