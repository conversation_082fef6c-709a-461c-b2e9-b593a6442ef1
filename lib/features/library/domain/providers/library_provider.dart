import 'package:selfeng/features/library/domain/repositories/library_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final libraryRepositoryProvider = Provider<LibraryRepository>((ref) {
  final FirestoreServiceRepository firestore = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return LibraryRepositoryImpl(firestore);
});
