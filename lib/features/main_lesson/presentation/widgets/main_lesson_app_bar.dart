// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MainLessonAppBar extends StatelessWidget implements PreferredSizeWidget {
  const MainLessonAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.isBookmarked = false,
    this.onBookmark,
    this.onHelp,
  });
  final String title;
  final String? subtitle;
  final bool isBookmarked;
  final VoidCallback? onBookmark;
  final VoidCallback? onHelp;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: const Icon(
          Icons.arrow_back_ios,
          // size: 36,
          // color: Colors.black,
        ),
      ),
      title: Text(title, style: Theme.of(context).textTheme.titleMedium),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(
            Icons.help_outline,
            // size: 32,
            // color: Color(0xffD9D9D9),
          ),
          onPressed: onHelp ?? () {},
        ),
        IconButton(
          icon: Icon(
            isBookmarked
                ? Icons.bookmark_outlined
                : Icons.bookmark_border_outlined,
            // size: 32,
            // color: Color(0xff540005),
          ),
          onPressed: onBookmark ?? () {},
        ),
      ],
      bottom:
          subtitle != null
              ? PreferredSize(
                preferredSize: const Size.fromHeight(100.0),
                child: Column(
                  children: [
                    const SizedBox(height: 32),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 22,
                        vertical: 10,
                      ),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xffFA3236),
                            Color(0xffD9353C),
                            Color(0xff8C1412),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        subtitle!,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(color: Colors.white),
                        maxLines: 2,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
              )
              : null,
    );
  }

  @override
  Size get preferredSize {
    final subtitleHeight = subtitle != null ? 100.0 : 0.0;
    return Size.fromHeight(kToolbarHeight + subtitleHeight);
  }
}
