import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/setting/presentation/providers/notification_settings_provider.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

// Define a theme color based on the image (approximation)
const Color kPrimaryThemeColor = Color(0xFFD93622); // Red color from image

class NotificationSettingScreen extends ConsumerWidget {
  const NotificationSettingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationSettings = ref.watch(notificationSettingsProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          context.loc.notification_settings,
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        children: [
          _buildSectionHeader(context, context.loc.notification_preferences),
          _buildNotificationList(context, ref, notificationSettings),
          const SizedBox(height: 24),
          _buildInfoSection(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: kPrimaryThemeColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildNotificationList(
    BuildContext context,
    WidgetRef ref,
    Map<String, bool> settings,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildNotificationTile(
            context: context,
            ref: ref,
            icon: Icons.notifications,
            title: context.loc.general_notification,
            description: context.loc.general_notification_desc,
            topicKey: 'general_notification',
            isEnabled: settings['general_notification'] ?? true,
          ),
          const Divider(height: 1, indent: 56),
          _buildNotificationTile(
            context: context,
            ref: ref,
            icon: Icons.local_offer,
            title: context.loc.promotion,
            description: context.loc.promotion_desc,
            topicKey: 'promotion',
            isEnabled: settings['promotion'] ?? true,
          ),
          const Divider(height: 1, indent: 56),
          _buildNotificationTile(
            context: context,
            ref: ref,
            icon: Icons.campaign,
            title: context.loc.announcement,
            description: context.loc.announcement_desc,
            topicKey: 'announcement',
            isEnabled: settings['announcement'] ?? true,
          ),
          const Divider(height: 1, indent: 56),
          _buildNotificationTile(
            context: context,
            ref: ref,
            icon: Icons.schedule,
            title: context.loc.study_reminder,
            description: context.loc.study_reminder_desc,
            topicKey: 'study_reminder',
            isEnabled: settings['study_reminder'] ?? true,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTile({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String description,
    required String topicKey,
    required bool isEnabled,
  }) {
    return InkWell(
      onTap: () {
        ref
            .read(notificationSettingsProvider.notifier)
            .toggleNotification(topicKey);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: kPrimaryThemeColor, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Switch(
              value: isEnabled,
              onChanged: (bool value) {
                ref
                    .read(notificationSettingsProvider.notifier)
                    .toggleNotification(topicKey);
              },
              // activeThumbColor: kPrimaryThemeColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.loc.notification_info,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.blue.shade700,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
