// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memory_flash_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$memoryFlashControllerHash() =>
    r'8bb558980c5ae93ebd1f1eb614f3b75ce04378bd';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$MemoryFlashController
    extends BuildlessAutoDisposeAsyncNotifier<MemoryFlashState> {
  late final String? topic;

  FutureOr<MemoryFlashState> build({String? topic});
}

/// See also [MemoryFlashController].
@ProviderFor(MemoryFlashController)
const memoryFlashControllerProvider = MemoryFlashControllerFamily();

/// See also [MemoryFlashController].
class MemoryFlashControllerFamily extends Family<AsyncValue<MemoryFlashState>> {
  /// See also [MemoryFlashController].
  const MemoryFlashControllerFamily();

  /// See also [MemoryFlashController].
  MemoryFlashControllerProvider call({String? topic}) {
    return MemoryFlashControllerProvider(topic: topic);
  }

  @override
  MemoryFlashControllerProvider getProviderOverride(
    covariant MemoryFlashControllerProvider provider,
  ) {
    return call(topic: provider.topic);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'memoryFlashControllerProvider';
}

/// See also [MemoryFlashController].
class MemoryFlashControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          MemoryFlashController,
          MemoryFlashState
        > {
  /// See also [MemoryFlashController].
  MemoryFlashControllerProvider({String? topic})
    : this._internal(
        () => MemoryFlashController()..topic = topic,
        from: memoryFlashControllerProvider,
        name: r'memoryFlashControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$memoryFlashControllerHash,
        dependencies: MemoryFlashControllerFamily._dependencies,
        allTransitiveDependencies:
            MemoryFlashControllerFamily._allTransitiveDependencies,
        topic: topic,
      );

  MemoryFlashControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.topic,
  }) : super.internal();

  final String? topic;

  @override
  FutureOr<MemoryFlashState> runNotifierBuild(
    covariant MemoryFlashController notifier,
  ) {
    return notifier.build(topic: topic);
  }

  @override
  Override overrideWith(MemoryFlashController Function() create) {
    return ProviderOverride(
      origin: this,
      override: MemoryFlashControllerProvider._internal(
        () => create()..topic = topic,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        topic: topic,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    MemoryFlashController,
    MemoryFlashState
  >
  createElement() {
    return _MemoryFlashControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MemoryFlashControllerProvider && other.topic == topic;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, topic.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MemoryFlashControllerRef
    on AutoDisposeAsyncNotifierProviderRef<MemoryFlashState> {
  /// The parameter `topic` of this provider.
  String? get topic;
}

class _MemoryFlashControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          MemoryFlashController,
          MemoryFlashState
        >
    with MemoryFlashControllerRef {
  _MemoryFlashControllerProviderElement(super.provider);

  @override
  String? get topic => (origin as MemoryFlashControllerProvider).topic;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
