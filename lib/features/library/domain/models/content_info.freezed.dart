// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'content_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContentInfo {

 String get title; String get image; String get icon; String? get iconSecondary; SectionType get section; int get sectionColor; bool get isExpanded;
/// Create a copy of ContentInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ContentInfoCopyWith<ContentInfo> get copyWith => _$ContentInfoCopyWithImpl<ContentInfo>(this as ContentInfo, _$identity);

  /// Serializes this ContentInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ContentInfo&&(identical(other.title, title) || other.title == title)&&(identical(other.image, image) || other.image == image)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.iconSecondary, iconSecondary) || other.iconSecondary == iconSecondary)&&(identical(other.section, section) || other.section == section)&&(identical(other.sectionColor, sectionColor) || other.sectionColor == sectionColor)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,image,icon,iconSecondary,section,sectionColor,isExpanded);

@override
String toString() {
  return 'ContentInfo(title: $title, image: $image, icon: $icon, iconSecondary: $iconSecondary, section: $section, sectionColor: $sectionColor, isExpanded: $isExpanded)';
}


}

/// @nodoc
abstract mixin class $ContentInfoCopyWith<$Res>  {
  factory $ContentInfoCopyWith(ContentInfo value, $Res Function(ContentInfo) _then) = _$ContentInfoCopyWithImpl;
@useResult
$Res call({
 String title, String image, String icon, String? iconSecondary, SectionType section, int sectionColor, bool isExpanded
});




}
/// @nodoc
class _$ContentInfoCopyWithImpl<$Res>
    implements $ContentInfoCopyWith<$Res> {
  _$ContentInfoCopyWithImpl(this._self, this._then);

  final ContentInfo _self;
  final $Res Function(ContentInfo) _then;

/// Create a copy of ContentInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? image = null,Object? icon = null,Object? iconSecondary = freezed,Object? section = null,Object? sectionColor = null,Object? isExpanded = null,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,icon: null == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String,iconSecondary: freezed == iconSecondary ? _self.iconSecondary : iconSecondary // ignore: cast_nullable_to_non_nullable
as String?,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as SectionType,sectionColor: null == sectionColor ? _self.sectionColor : sectionColor // ignore: cast_nullable_to_non_nullable
as int,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ContentInfo].
extension ContentInfoPatterns on ContentInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ContentInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ContentInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ContentInfo value)  $default,){
final _that = this;
switch (_that) {
case _ContentInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ContentInfo value)?  $default,){
final _that = this;
switch (_that) {
case _ContentInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String title,  String image,  String icon,  String? iconSecondary,  SectionType section,  int sectionColor,  bool isExpanded)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ContentInfo() when $default != null:
return $default(_that.title,_that.image,_that.icon,_that.iconSecondary,_that.section,_that.sectionColor,_that.isExpanded);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String title,  String image,  String icon,  String? iconSecondary,  SectionType section,  int sectionColor,  bool isExpanded)  $default,) {final _that = this;
switch (_that) {
case _ContentInfo():
return $default(_that.title,_that.image,_that.icon,_that.iconSecondary,_that.section,_that.sectionColor,_that.isExpanded);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String title,  String image,  String icon,  String? iconSecondary,  SectionType section,  int sectionColor,  bool isExpanded)?  $default,) {final _that = this;
switch (_that) {
case _ContentInfo() when $default != null:
return $default(_that.title,_that.image,_that.icon,_that.iconSecondary,_that.section,_that.sectionColor,_that.isExpanded);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ContentInfo implements ContentInfo {
   _ContentInfo({required this.title, required this.image, required this.icon, this.iconSecondary, required this.section, required this.sectionColor, this.isExpanded = false});
  factory _ContentInfo.fromJson(Map<String, dynamic> json) => _$ContentInfoFromJson(json);

@override final  String title;
@override final  String image;
@override final  String icon;
@override final  String? iconSecondary;
@override final  SectionType section;
@override final  int sectionColor;
@override@JsonKey() final  bool isExpanded;

/// Create a copy of ContentInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ContentInfoCopyWith<_ContentInfo> get copyWith => __$ContentInfoCopyWithImpl<_ContentInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ContentInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ContentInfo&&(identical(other.title, title) || other.title == title)&&(identical(other.image, image) || other.image == image)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.iconSecondary, iconSecondary) || other.iconSecondary == iconSecondary)&&(identical(other.section, section) || other.section == section)&&(identical(other.sectionColor, sectionColor) || other.sectionColor == sectionColor)&&(identical(other.isExpanded, isExpanded) || other.isExpanded == isExpanded));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,image,icon,iconSecondary,section,sectionColor,isExpanded);

@override
String toString() {
  return 'ContentInfo(title: $title, image: $image, icon: $icon, iconSecondary: $iconSecondary, section: $section, sectionColor: $sectionColor, isExpanded: $isExpanded)';
}


}

/// @nodoc
abstract mixin class _$ContentInfoCopyWith<$Res> implements $ContentInfoCopyWith<$Res> {
  factory _$ContentInfoCopyWith(_ContentInfo value, $Res Function(_ContentInfo) _then) = __$ContentInfoCopyWithImpl;
@override @useResult
$Res call({
 String title, String image, String icon, String? iconSecondary, SectionType section, int sectionColor, bool isExpanded
});




}
/// @nodoc
class __$ContentInfoCopyWithImpl<$Res>
    implements _$ContentInfoCopyWith<$Res> {
  __$ContentInfoCopyWithImpl(this._self, this._then);

  final _ContentInfo _self;
  final $Res Function(_ContentInfo) _then;

/// Create a copy of ContentInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? image = null,Object? icon = null,Object? iconSecondary = freezed,Object? section = null,Object? sectionColor = null,Object? isExpanded = null,}) {
  return _then(_ContentInfo(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,icon: null == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String,iconSecondary: freezed == iconSecondary ? _self.iconSecondary : iconSecondary // ignore: cast_nullable_to_non_nullable
as String?,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as SectionType,sectionColor: null == sectionColor ? _self.sectionColor : sectionColor // ignore: cast_nullable_to_non_nullable
as int,isExpanded: null == isExpanded ? _self.isExpanded : isExpanded // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
