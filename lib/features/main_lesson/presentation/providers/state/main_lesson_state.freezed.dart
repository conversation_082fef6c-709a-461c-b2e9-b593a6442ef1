// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'main_lesson_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MainLessonState {

 LastCourse? get lastPronunciation; LastCourse? get lastConversation; LastCourse? get lastListening; LastCourse? get lastSpeaking; bool get fromLastCourse;
/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MainLessonStateCopyWith<MainLessonState> get copyWith => _$MainLessonStateCopyWithImpl<MainLessonState>(this as MainLessonState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MainLessonState&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking)&&(identical(other.fromLastCourse, fromLastCourse) || other.fromLastCourse == fromLastCourse));
}


@override
int get hashCode => Object.hash(runtimeType,lastPronunciation,lastConversation,lastListening,lastSpeaking,fromLastCourse);

@override
String toString() {
  return 'MainLessonState(lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking, fromLastCourse: $fromLastCourse)';
}


}

/// @nodoc
abstract mixin class $MainLessonStateCopyWith<$Res>  {
  factory $MainLessonStateCopyWith(MainLessonState value, $Res Function(MainLessonState) _then) = _$MainLessonStateCopyWithImpl;
@useResult
$Res call({
 LastCourse? lastPronunciation, LastCourse? lastConversation, LastCourse? lastListening, LastCourse? lastSpeaking, bool fromLastCourse
});


$LastCourseCopyWith<$Res>? get lastPronunciation;$LastCourseCopyWith<$Res>? get lastConversation;$LastCourseCopyWith<$Res>? get lastListening;$LastCourseCopyWith<$Res>? get lastSpeaking;

}
/// @nodoc
class _$MainLessonStateCopyWithImpl<$Res>
    implements $MainLessonStateCopyWith<$Res> {
  _$MainLessonStateCopyWithImpl(this._self, this._then);

  final MainLessonState _self;
  final $Res Function(MainLessonState) _then;

/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,Object? fromLastCourse = null,}) {
  return _then(_self.copyWith(
lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,fromLastCourse: null == fromLastCourse ? _self.fromLastCourse : fromLastCourse // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}
}


/// Adds pattern-matching-related methods to [MainLessonState].
extension MainLessonStatePatterns on MainLessonState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MainLessonState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MainLessonState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MainLessonState value)  $default,){
final _that = this;
switch (_that) {
case _MainLessonState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MainLessonState value)?  $default,){
final _that = this;
switch (_that) {
case _MainLessonState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool fromLastCourse)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MainLessonState() when $default != null:
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.fromLastCourse);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool fromLastCourse)  $default,) {final _that = this;
switch (_that) {
case _MainLessonState():
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.fromLastCourse);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( LastCourse? lastPronunciation,  LastCourse? lastConversation,  LastCourse? lastListening,  LastCourse? lastSpeaking,  bool fromLastCourse)?  $default,) {final _that = this;
switch (_that) {
case _MainLessonState() when $default != null:
return $default(_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking,_that.fromLastCourse);case _:
  return null;

}
}

}

/// @nodoc


class _MainLessonState extends MainLessonState {
   _MainLessonState({this.lastPronunciation, this.lastConversation, this.lastListening, this.lastSpeaking, this.fromLastCourse = false}): super._();
  

@override final  LastCourse? lastPronunciation;
@override final  LastCourse? lastConversation;
@override final  LastCourse? lastListening;
@override final  LastCourse? lastSpeaking;
@override@JsonKey() final  bool fromLastCourse;

/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MainLessonStateCopyWith<_MainLessonState> get copyWith => __$MainLessonStateCopyWithImpl<_MainLessonState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MainLessonState&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking)&&(identical(other.fromLastCourse, fromLastCourse) || other.fromLastCourse == fromLastCourse));
}


@override
int get hashCode => Object.hash(runtimeType,lastPronunciation,lastConversation,lastListening,lastSpeaking,fromLastCourse);

@override
String toString() {
  return 'MainLessonState(lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking, fromLastCourse: $fromLastCourse)';
}


}

/// @nodoc
abstract mixin class _$MainLessonStateCopyWith<$Res> implements $MainLessonStateCopyWith<$Res> {
  factory _$MainLessonStateCopyWith(_MainLessonState value, $Res Function(_MainLessonState) _then) = __$MainLessonStateCopyWithImpl;
@override @useResult
$Res call({
 LastCourse? lastPronunciation, LastCourse? lastConversation, LastCourse? lastListening, LastCourse? lastSpeaking, bool fromLastCourse
});


@override $LastCourseCopyWith<$Res>? get lastPronunciation;@override $LastCourseCopyWith<$Res>? get lastConversation;@override $LastCourseCopyWith<$Res>? get lastListening;@override $LastCourseCopyWith<$Res>? get lastSpeaking;

}
/// @nodoc
class __$MainLessonStateCopyWithImpl<$Res>
    implements _$MainLessonStateCopyWith<$Res> {
  __$MainLessonStateCopyWithImpl(this._self, this._then);

  final _MainLessonState _self;
  final $Res Function(_MainLessonState) _then;

/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,Object? fromLastCourse = null,}) {
  return _then(_MainLessonState(
lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,fromLastCourse: null == fromLastCourse ? _self.fromLastCourse : fromLastCourse // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of MainLessonState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}
}

// dart format on
