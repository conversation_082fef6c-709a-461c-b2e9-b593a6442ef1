import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';
import 'package:selfeng/features/diagnostic_test/domain/providers/diagnostic_test_provider.dart';
import 'package:selfeng/features/diagnostic_test/domain/repositories/diagnostic_test_repository.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/state/timer_state.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';

import 'state/diagnostic_test_state.dart';

part 'diagnostic_test_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class DiagnosticTestController extends _$DiagnosticTestController {
  late DiagnosticTestRepository diagnosticTestRepository;
  late TimerController viewTimerModel;

  @override
  FutureOr<DiagnosticTestState> build() {
    diagnosticTestRepository = ref.watch(diagnosticTestRepositoryProvider);
    viewTimerModel = ref.watch(timerControllerProvider.notifier);
    return init();
  }

  FutureOr<DiagnosticTestState> init() async {
    List<QuestionModel> respond = [];
    state = const AsyncLoading();
    final result = await diagnosticTestRepository.getListDiagnosticTest();
    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) {
        respond = data;
      },
    );
    return DiagnosticTestState(
      questions: respond,
      controllerScroll: ScrollController(),
    );
  }

  Future<void> saveAnswer() async {
    state = const AsyncLoading();
    final answers =
        state.value!.questions
            .map((item) => item.answer)
            .whereType<Choice>()
            .toList();

    final result = await diagnosticTestRepository.saveAnswer(
      answers,
      state.value!.correctCount,
    );
    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) {
        state = AsyncData(state.value!.copyWith(result: data));
      },
    );
  }

  void setTimer() {
    viewTimerModel.setTimerState(TimerConcreteState.start);
    viewTimerModel.startTimer();
  }

  // nextPage({int? pageNumber}) {
  //   if (state.value!.currentPage <= state.value!.questions.length) {
  //     state = AsyncData(state.value!.copyWith(
  //       currentPage: pageNumber ?? state.value!.currentPage + 1,
  //     ));
  //   }
  // }

  Future nextPage(context, double width) async {
    if (state.value!.currentPage < state.value!.questionLength) {
      state = AsyncData(
        state.value!.copyWith(currentPage: state.value!.currentPage + 1),
      );
      state.value?.controllerScroll?.animateTo(
        (state.value?.currentPage ?? 0) * (width - 25),
        duration: const Duration(seconds: 1),
        curve: Curves.ease,
      );
    }
  }

  Future backPage(double width) async {
    if (state.value!.currentPage != 0) {
      state = AsyncData(
        state.value!.copyWith(currentPage: state.value!.currentPage - 1),
      );
    }
    state.value?.controllerScroll?.animateTo(
      (state.value?.currentPage ?? 0) * (width - 25),
      duration: const Duration(seconds: 1),
      curve: Curves.ease,
    );
  }

  void expandResult() {
    state = AsyncData(
      state.value!.copyWith(expandedResult: !state.value!.expandedResult),
    );
  }

  void selectAnser({required int index, required Choice value}) {
    if (state.value!.currentPage <= state.value!.questions.length) {
      List<QuestionModel> tempData = List.from(state.value!.questions);
      QuestionModel temp = tempData[index];
      tempData[index] = temp.copyWith(
        state: QuestionConcreteState.answered,
        answer: value,
        isCorrect: value.isCorrect,
      );
      state = AsyncData(state.value!.copyWith(questions: tempData));
    }
  }
}
