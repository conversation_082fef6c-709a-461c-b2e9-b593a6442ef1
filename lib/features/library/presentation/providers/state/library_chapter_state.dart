import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';

part 'library_chapter_state.freezed.dart';

@freezed
abstract class LibraryChapterState with _$LibraryChapterState {
  factory LibraryChapterState({
    @Default([]) List<ChapterData> chapters,
    LevelInfo? levelInfo,
  }) = _LibraryChapterState;

  // Allow custom getters / setters
  const LibraryChapterState._();
}
