import 'package:flutter/material.dart';
import 'package:selfeng/shared/theme/app_colors.dart';
import 'package:selfeng/shared/theme/text_styles.dart';

class TextThemes {
  /// Main text theme

  static TextTheme get textTheme {
    return TextTheme(
      headlineLarge: AppTextStyles.hLg,
      headlineMedium: AppTextStyles.h,
      headlineSmall: AppTextStyles.hSm,
      bodyLarge: AppTextStyles.bodyLg,
      bodyMedium: AppTextStyles.body,
      bodySmall: AppTextStyles.bodySm,
      titleLarge: AppTextStyles.titleLg,
      titleMedium: AppTextStyles.title,
      titleSmall: AppTextStyles.titleSm,
      labelLarge: AppTextStyles.labelLg,
      labelMedium: AppTextStyles.label,
      labelSmall: AppTextStyles.labelSm,
    );
  }

  /// Dark text theme

  static TextTheme get darkTextTheme {
    return TextTheme(
      headlineLarge: AppTextStyles.hLg.copyWith(color: AppColors.white),
      headlineMedium: AppTextStyles.h.copyWith(color: AppColors.white),
      headlineSmall: AppTextStyles.hSm.copyWith(color: AppColors.white),
      bodyLarge: AppTextStyles.bodyLg.copyWith(color: AppColors.white),
      bodyMedium: AppTextStyles.body.copyWith(color: AppColors.white),
      bodySmall: AppTextStyles.bodySm.copyWith(color: AppColors.white),
      titleLarge: AppTextStyles.titleLg.copyWith(color: AppColors.white),
      titleMedium: AppTextStyles.title.copyWith(color: AppColors.white),
      titleSmall: AppTextStyles.titleSm.copyWith(color: AppColors.white),
      labelLarge: AppTextStyles.labelLg.copyWith(color: AppColors.white),
      labelMedium: AppTextStyles.label.copyWith(color: AppColors.white),
      labelSmall: AppTextStyles.labelSm.copyWith(color: AppColors.white),
    );
  }

  /// Primary text theme

  static TextTheme get primaryTextTheme {
    return TextTheme(
      headlineLarge: AppTextStyles.hLg.copyWith(color: AppColors.primary),
      headlineMedium: AppTextStyles.h.copyWith(color: AppColors.primary),
      headlineSmall: AppTextStyles.hSm.copyWith(color: AppColors.primary),
      bodyLarge: AppTextStyles.bodyLg.copyWith(color: AppColors.primary),
      bodyMedium: AppTextStyles.body.copyWith(color: AppColors.primary),
      bodySmall: AppTextStyles.bodySm.copyWith(color: AppColors.primary),
      titleLarge: AppTextStyles.titleLg.copyWith(color: AppColors.primary),
      titleMedium: AppTextStyles.title.copyWith(color: AppColors.primary),
      titleSmall: AppTextStyles.titleSm.copyWith(color: AppColors.primary),
      labelLarge: AppTextStyles.labelLg.copyWith(color: AppColors.primary),
      labelMedium: AppTextStyles.label.copyWith(color: AppColors.primary),
      labelSmall: AppTextStyles.labelSm.copyWith(color: AppColors.primary),
    );
  }
}
