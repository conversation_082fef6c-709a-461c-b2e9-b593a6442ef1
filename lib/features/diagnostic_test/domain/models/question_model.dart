import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_model.freezed.dart';
part 'question_model.g.dart';

typedef QuestionModelList = List<QuestionModel>;

enum QuestionConcreteState { initial, answered }

@freezed
sealed class QuestionModel with _$QuestionModel {
  factory QuestionModel({
    @JsonKey(name: 'question_id') String? questionId,
    @Default(0) int order,
    @Default('') String question,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url') String image,
    @Default(true) @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') isActive,
    @Default([]) List<Choice> choices,
    @Default(QuestionConcreteState.initial)
    @JsonKey(includeFromJson: false)
    QuestionConcreteState state,
    @Default(false) @JsonKey(includeFromJson: false) bool isCorrect,
    @JsonKey(includeFromJson: false) Choice? answer,
  }) = _QuestionModel;

  factory QuestionModel.fromJson(dynamic json) => _$QuestionModelFromJson(json);
}

@freezed
sealed class Choice with _$Choice {
  factory Choice({
    @Default('') String text,
    @Default('') String value,
    @Default(false) @Json<PERSON><PERSON>(name: 'is_correct') bool isCorrect,
  }) = _Choice;

  factory Choice.fromJson(dynamic json) => _$ChoiceFromJson(json);
}

@freezed
sealed class QuestionResultModel with _$QuestionResultModel {
  factory QuestionResultModel({
    @Default(0) int point,
    @Default(0) int maxValue,
    @Default(0) int minValue,
    Map<String, dynamic>? level,
    Map<String, dynamic>? description,
  }) = _QuestionResultModel;

  factory QuestionResultModel.fromJson(dynamic json) =>
      _$QuestionResultModelFromJson(json);
}
