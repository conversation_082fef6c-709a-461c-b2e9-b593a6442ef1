import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

// StateNotifier for User data
class UserDataNotifier extends StateNotifier<UserData?> {
  UserDataNotifier() : super(null);

  // Method to update user data
  void setUser(UserData user) {
    state = user;
  }

  // Method to clear user data
  void clearUser() {
    state = null;
  }
}

// Riverpod provider for UserNotifier
final userDataProvider = StateNotifierProvider<UserDataNotifier, UserData?>((
  ref,
) {
  return UserDataNotifier();
});
