import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'library_scroll_provider.g.dart';

/// Provider that manages the scroll position for the Library screen.
///
/// This provider persists the scroll position across navigation,
/// allowing users to return to the same position in the library list
/// after navigating to other screens.
@Riverpod(keepAlive: true)
class LibraryScrollPosition extends _$LibraryScrollPosition {
  @override
  double build() {
    return 0.0;
  }

  /// Updates the current scroll position
  void updatePosition(double position) {
    state = position;
  }

  /// Resets the scroll position to the top
  void resetPosition() {
    state = 0.0;
  }
}
