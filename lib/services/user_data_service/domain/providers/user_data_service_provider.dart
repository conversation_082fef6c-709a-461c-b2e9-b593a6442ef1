import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';

final userDataServiceProvider = Provider<UserDataServiceRepository>((ref) {
  final FirestoreServiceRepository firestore = ref.watch(
    firestoreServiceRepositoryProvider,
  );
  return UserDataServiceRepository(firestore);
});
