import 'package:flutter/material.dart';
import 'package:selfeng/shared/theme/app_colors.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Mock search results for UI demonstration
  final List<SearchResultItem> _mockResults = [
    SearchResultItem(
      icon: Icons.play_circle_outline,
      iconColor: AppColors.primary,
      title: 'I enjoy playing soccer with my friends.',
      subtitle: 'Listening Exercise',
    ),
    SearchResultItem(
      icon: Icons.mic_none,
      iconColor: AppColors.primary,
      title: 'Having a picnic and playing games',
      subtitle: 'Speaking Practice',
    ),
    SearchResultItem(
      icon: Icons.movie_outlined,
      iconColor: AppColors.primary,
      title: '... or a movie playing in the background.',
      subtitle: 'Video Content',
    ),
    SearchResultItem(
      icon: Icons.movie_outlined,
      iconColor: AppColors.primary,
      title: '... or a movie playing in the background.',
      subtitle: 'Video Content',
    ),
  ];

  List<SearchResultItem> _filteredResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _filteredResults = _mockResults;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (!mounted) return;
    setState(() {
      _isSearching = query.isNotEmpty;
      if (query.isEmpty) {
        _filteredResults = _mockResults;
      } else {
        _filteredResults =
            _mockResults
                .where(
                  (item) =>
                      item.title.toLowerCase().contains(query.toLowerCase()) ||
                      item.subtitle.toLowerCase().contains(query.toLowerCase()),
                )
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Custom App Bar with Search
            _buildSearchHeader(),
            // Search Results
            Expanded(child: _buildSearchResults()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        children: [
          // Search Bar
          _buildSearchBar(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: AppColors.primary, width: 1.5),
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: _onSearchChanged,
        style:
            Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.black,
              fontSize: 16,
            ) ??
            const TextStyle(color: AppColors.black, fontSize: 16),
        decoration: InputDecoration(
          hintText: 'playing',
          hintStyle:
              Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.lightGrey,
                fontSize: 16,
              ) ??
              const TextStyle(color: AppColors.lightGrey, fontSize: 16),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.lightGrey,
            size: 20,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_filteredResults.isEmpty && _isSearching) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredResults.length,
      itemBuilder: (context, index) {
        final result = _filteredResults[index];
        return _buildSearchResultItem(result, index);
      },
    );
  }

  Widget _buildSearchResultItem(SearchResultItem result, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // TODO: Implement result selection
          },
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primary.withValues(alpha: 0.1),
          highlightColor: AppColors.primary.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.extraLightGrey, width: 1),
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: result.iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(result.icon, color: result.iconColor, size: 20),
                ),
                const SizedBox(width: 16),
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        result.title,
                        style:
                            Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.black,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ) ??
                            const TextStyle(
                              color: AppColors.black,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        result.subtitle,
                        style:
                            Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.lightGrey,
                              fontSize: 14,
                            ) ??
                            const TextStyle(
                              color: AppColors.lightGrey,
                              fontSize: 14,
                            ),
                      ),
                    ],
                  ),
                ),
                // Arrow Icon
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.lightGrey,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: AppColors.lightGrey),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style:
                  Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.lightGrey,
                    fontWeight: FontWeight.w600,
                  ) ??
                  const TextStyle(
                    color: AppColors.lightGrey,
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style:
                  Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.lightGrey,
                  ) ??
                  const TextStyle(color: AppColors.lightGrey, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class SearchResultItem {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;

  SearchResultItem({
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
  });
}
