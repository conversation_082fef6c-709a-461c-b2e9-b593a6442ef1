# Update Priority Implementation - Complete Answer

## 🎯 **Your Question Answered**

You asked: *"Where are the 6 update priority levels (0-5) implemented and how do I set the update priority level?"*

## 📍 **Where Priority Logic is Located**

The priority system is implemented in:

**File:** `/lib/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart`

**Key Methods:**
1. **`get updatePriority`** (line ~287) - Returns the calculated priority (0-5)
2. **`_calculateUpdatePriority()`** (line ~299) - Contains the logic that determines priority
3. **`get isHighPriorityUpdate`** (line ~295) - Determines if update should be forced (priority >= 4)

## 🔧 **How Priority is Currently Calculated**

### Method 1: Version Name Keywords
```dart
// In _calculateUpdatePriority() method
final availableVersionName = updateInfo.availableVersionCode.toString();

// Priority 5 (Critical) - Forces immediate update
if (availableVersionName.contains('critical') || 
    availableVersionName.contains('emergency')) {
  return 5;
}

// Priority 4 (High) - Forces immediate update  
if (availableVersionName.contains('security') || 
    availableVersionName.contains('urgent')) {
  return 4;
}

// Priority 3 (Medium) - Flexible update
if (availableVersionName.contains('hotfix') || 
    availableVersionName.contains('important')) {
  return 3;
}

// Priority 1 (Very Low) - Flexible update
if (availableVersionName.contains('beta') || 
    availableVersionName.contains('alpha')) {
  return 1;
}
```

### Method 2: Version Code Differences
```dart
// Large version jumps indicate major updates
final versionDifference = availableVersionCode - currentVersionCode;

if (versionDifference >= 100) {
  return 3; // Major update - Medium priority
} else if (versionDifference >= 10) {
  return 2; // Minor update - Low priority
}

// Default: Priority 2
return 2;
```

## 🚀 **How to Set Update Priority**

### Option 1: Version Naming (Easiest)

When releasing in Google Play Console, include keywords in your version name:

**Examples:**
- `1.2.3-critical` → **Priority 5** (Critical - Forced update)
- `1.2.4-security` → **Priority 4** (High - Forced update)
- `1.2.5-hotfix` → **Priority 3** (Medium - Flexible update)
- `1.2.6-beta` → **Priority 1** (Very Low - Flexible update)
- `1.2.7` → **Priority 2** (Default - Flexible update)

### Option 2: Version Code Strategy

Use specific version code patterns:
- **Version codes ending in 99**: Critical (e.g., 10099)
- **Large version jumps (+100)**: Medium priority
- **Small version jumps (+10)**: Low priority

### Option 3: Custom Logic

Modify the `_calculateUpdatePriority()` method to add your own rules:

```dart
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  // Your custom logic here
  
  // Example: Time-based priority
  final now = DateTime.now();
  if (now.hour < 9) { // Before 9 AM
    return 1; // Lower priority during early hours
  }
  
  // Example: Day-based priority
  if (now.weekday == DateTime.friday) {
    return 4; // Higher priority on Fridays
  }
  
  // Fall back to existing logic
  return _existingLogic(updateInfo);
}
```

## 📊 **Priority Levels Explained**

| Priority | Level | Update Type | When to Use | User Experience |
|----------|-------|-------------|-------------|-----------------|
| **5** | Critical | Immediate | Security vulnerabilities, app crashes | **Blocks app** until updated |
| **4** | High | Immediate | Important bug fixes, urgent features | **Blocks app** until updated |
| **3** | Medium | Flexible | Major features, significant improvements | Downloads in background |
| **2** | Low | Flexible | Regular updates, minor features | Downloads in background |
| **1** | Very Low | Flexible | Beta features, experimental changes | Downloads in background |
| **0** | None | Flexible | Fallback/error state | Downloads in background |

## 🧪 **Testing Your Priority Settings**

### 1. **Local Testing**
```dart
// Temporarily override priority for testing
@override
int get updatePriority => 5; // Test critical priority
```

### 2. **Play Store Testing**
1. Upload to **Internal Testing** track
2. Use version names like `1.0.1-critical`
3. Install from Play Store and test update flow

### 3. **Debug Logs**
Look for these messages in your console:
```
📋 Update priority: 3
📱 Current version code: 1001
🔍 Available version code: 1002
📊 Version difference: 1
```

## 🎛️ **Customizing Priority Logic**

To modify how priorities are calculated, edit this method in `in_app_update_service_repository.dart`:

```dart
int _calculateUpdatePriority(AppUpdateInfo updateInfo) {
  // Add your custom rules here
  
  // Example: App-specific keywords
  if (updateInfo.availableVersionCode.toString().contains('myapp-urgent')) {
    return 5;
  }
  
  // Example: Remote configuration
  final remoteConfig = FirebaseRemoteConfig.instance;
  final priorityOverride = remoteConfig.getInt('force_update_priority');
  if (priorityOverride > 0) {
    return priorityOverride;
  }
  
  // Keep existing logic as fallback
  return _existingPriorityLogic(updateInfo);
}
```

## ✅ **Quick Start Guide**

1. **For Critical Updates**: Use version name `x.x.x-critical`
2. **For Security Updates**: Use version name `x.x.x-security`  
3. **For Regular Updates**: Use normal version names like `x.x.x`
4. **For Beta Updates**: Use version name `x.x.x-beta`

The system will automatically:
- Force immediate updates for priority 4-5
- Show flexible update banners for priority 0-3
- Handle all the UI and user experience

## 🔍 **Where to Find More Details**

- **Complete Guide**: `UPDATE_PRIORITY_GUIDE.md`
- **Implementation**: `in_app_update_service_repository.dart` (lines 287-352)
- **Usage Examples**: `examples/update_usage_example.dart`
- **UI Components**: `presentation/widgets/update_dialog.dart`

Your update priority system is now **fully implemented and ready to use**! 🚀