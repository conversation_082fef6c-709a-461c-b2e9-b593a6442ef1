import 'dart:async';

import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/widgets/custom_page_transition.dart';
// import 'package:selfeng/features/splash/presentation/screens/playground_screen.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:go_router/go_router.dart';

///////////////////////////Page List/////////////////////////////////////
////////////////////////SplashScreen
import 'package:selfeng/features/splash/presentation/screens/splash_screen.dart';
////////////////////////Onboarding
import 'package:selfeng/features/splash/presentation/screens/onboarding_screen.dart'
    deferred as onboarding;

////////////////////////Setting
import 'package:selfeng/features/setting/presentation/screens/language_screen.dart'
    deferred as language;
import 'package:selfeng/features/setting/presentation/screens/selected_language_screen.dart'
    deferred as selected_language;
import 'package:selfeng/features/setting/presentation/screens/profile_settings_screen.dart'
    deferred as profile_setting;
import 'package:selfeng/features/certificate/presentation/screens/certificate_list_screen.dart'
    deferred as certificate_list;
import 'package:selfeng/features/certificate/presentation/screens/certificate_screen.dart'
    deferred as certificate;
import 'package:selfeng/features/certificate/presentation/screens/certificate_detail_screen.dart'
    deferred as certificate_detail;
import 'package:selfeng/features/setting/presentation/screens/notification_setting_screen.dart'
    deferred as notification_setting;

////////////////////////Dashboard
import 'package:selfeng/features/dashboard/presentation/screens/dashboard_screen.dart'
    deferred as dashboard;

////////////////////////Questionnaire
import 'package:selfeng/features/questionnaire/presentation/screens/questionnaire_screen.dart'
    deferred as questionnaire;
import 'package:selfeng/features/questionnaire/presentation/screens/questionnaire_finish_screen.dart'
    deferred as questionnaire_finish;

////////////////////////Diagnostic Test
import 'package:selfeng/features/diagnostic_test/presentation/screens/diagnostic_test_instruction_screen.dart'
    deferred as diagnostic_test_inst;
import 'package:selfeng/features/diagnostic_test/presentation/screens/diagnostic_test_screen.dart'
    deferred as diagnostic_test;
import 'package:selfeng/features/diagnostic_test/presentation/screens/diagnostic_test_onboarding_screen.dart'
    deferred as diagnostic_test_onboarding;
import 'package:selfeng/features/diagnostic_test/presentation/screens/diagnostic_test_result_screen.dart'
    deferred as diagnostic_test_result;
import 'package:selfeng/features/diagnostic_test/presentation/screens/diagnostic_test_congratulation_screen.dart'
    deferred as diagnostic_test_congratulation;

/// Chapter Title
import 'package:selfeng/features/main_lesson/presentation/screens/chapter_title_screen.dart'
    deferred as chapter_title;

////////////////////////Challange
/////////////////////////////////Pronunciation
import 'package:selfeng/features/main_lesson/presentation/screens/pronunciation_challenge_screen.dart'
    deferred as pronunciation_challenge;
import 'package:selfeng/features/main_lesson/presentation/screens/pronunciation_challenge_onboarding_screen.dart'
    deferred as pronunciation_challenge_onboarding;
import 'package:selfeng/features/main_lesson/presentation/screens/pronunciation_challenge_instruction_screen.dart'
    deferred as pronunciation_challenge_instruction;
import 'package:selfeng/features/main_lesson/presentation/screens/pronunciation_challenge_content_result_screen.dart'
    deferred as pronunciation_challenge_content_result;
import 'package:selfeng/features/main_lesson/presentation/screens/pronunciation_challenge_result_screen.dart'
    deferred as pronunciation_challenge_result;
/////////////////////////////////Conversation video
import 'package:selfeng/features/main_lesson/presentation/screens/conversation_video_screen.dart'
    deferred as conversation_video;
import 'package:selfeng/features/main_lesson/presentation/screens/conversation_video_onboarding_screen.dart'
    deferred as conversation_video_onboarding;
import 'package:selfeng/features/main_lesson/presentation/screens/conversation_video_instruction_screen.dart'
    deferred as conversation_video_instruction;
import 'package:selfeng/features/main_lesson/presentation/screens/conversation_video_result_screen.dart'
    deferred as conversation_video_result;
/////////////////////////////////Listening mastery
import 'package:selfeng/features/main_lesson/presentation/screens/listening_mastery_screen.dart'
    deferred as listening_mastery;
import 'package:selfeng/features/main_lesson/presentation/screens/listening_mastery_onboarding_screen.dart'
    deferred as listening_mastery_onboarding;
import 'package:selfeng/features/main_lesson/presentation/screens/listening_mastery_instruction_screen.dart'
    deferred as listening_mastery_instruction;
// import 'package:selfeng/features/main_lesson/presentation/screens/listening_mastery_content_result_screen.dart'
//     deferred as listening_mastery_content_result_screen;
import 'package:selfeng/features/main_lesson/presentation/screens/listening_mastery_result_screen.dart'
    deferred as listening_mastery_result_screen;
/////////////////////////////////Speaking Arena
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_screen.dart'
    deferred as speaking_arena;
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_stage_screen.dart'
    deferred as speaking_arena_stage;
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_onboarding_screen.dart'
    deferred as speaking_arena_onboarding;
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_instruction_screen.dart'
    deferred as speaking_arena_instruction;
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_content_result_screen.dart'
    deferred as speaking_arena_content_result;
import 'package:selfeng/features/main_lesson/presentation/screens/speaking_arena_result_screen.dart'
    deferred as speaking_arena_result;
import 'package:selfeng/features/main_lesson/presentation/screens/certificate_notification_screen.dart'
    deferred as certificate_notification;

/////////////////////////////////Library
import 'package:selfeng/features/library/presentation/screens/library_chapter_screen.dart'
    deferred as library_chapter;
import 'package:selfeng/features/library/presentation/screens/library_screen.dart'
    deferred as library_screen;
import 'package:selfeng/features/library/presentation/screens/library_chapter_content_screen.dart'
    deferred as library_chapter_content;
/////////////////////////////////games
import 'package:selfeng/features/games/presentation/screens/topic_game_screen.dart'
    deferred as topic_game;
import 'package:selfeng/features/games/presentation/screens/memory_flash_screen.dart'
    deferred as memory_flash;

///// Search
import 'package:selfeng/features/search/presentation/screens/search_screen.dart'
    deferred as search_screen;

////////////////////////////////////////////////////////////////
part 'core_routes.g.dart';

///////////////////////////Router feature/////////////////////////////////////
part 'package:selfeng/features/dashboard/route/dashboard_routes.dart';
part 'package:selfeng/features/questionnaire/route/questionnaire_routes.dart';
part 'package:selfeng/features/diagnostic_test/route/diagnostic_test_routes.dart';
part 'package:selfeng/features/setting/route/setting_routes.dart';
part 'package:selfeng/features/splash/route/splash_routes.dart';
///////////////////////////////////Challange
part 'package:selfeng/features/main_lesson/route/main_lesson_routes.dart';
///////////////////////////////////Library
part 'package:selfeng/features/library/route/library_routes.dart';
///////////////////////////////////Game
part 'package:selfeng/features/games/route/game_routes.dart';
///// Search
part 'package:selfeng/features/search/route/search_route.dart';

////////////////////////////////////////////////////////////////
