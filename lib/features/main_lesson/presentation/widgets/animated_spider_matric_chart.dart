import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' show cos, sin, pi;

class SpiderMetricsChart extends StatelessWidget {
  final double accuracy;
  final double fluency;
  final double rhythm;
  final double completeness;

  const SpiderMetricsChart({
    super.key,
    required this.accuracy,
    required this.fluency,
    required this.rhythm,
    required this.completeness,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    // Determine chart dimensions based on screen width, ensuring it's not too large
    // Let's use 60% of screen width, but cap it to a max of, say, 300 logical pixels
    // and a min of, say, 150 to avoid being too small or too large.
    final double baseChartDimension = (screenSize.width * 0.6).clamp(
      150.0,
      300.0,
    );

    final double chartHeight = baseChartDimension;
    final double chartWidth = baseChartDimension;

    // Adjust titlePadding and titleOffsetX proportionally to the chart size
    // titlePadding could be, for example, 30% of chartWidth
    final double titlePadding = chartWidth * 0.3;
    // titleOffsetX could be, for example, -20% of chartWidth - Reducing this to -12%
    final double titleOffsetX = -(chartWidth * 0.12);

    // Define the size for the IconTitlePositioner's content (icon + text)
    // Let's say it's 30% of the chartWidth - Increasing to 35% and clamping
    final double titleWidgetSize = (chartWidth * 0.35).clamp(85.0, 120.0);

    final double containerWidth = chartWidth + (titlePadding * 2);
    final double containerHeight = chartHeight + (titlePadding * 2);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.1,
        vertical: screenSize.width * 0.12,
      ), // Proportional padding
      alignment: Alignment.center,
      child: Center(
        child: SizedBox(
          width: containerWidth,
          height: containerHeight,
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.center, // This ensures the chart is centered
            children: [
              // Center the chart explicitly
              Center(
                child: SizedBox(
                  width: chartWidth,
                  height: chartHeight,
                  child: RadarChart(
                    RadarChartData(
                      radarShape: RadarShape.polygon,
                      radarBackgroundColor: Color(0xffFFEFD6),
                      ticksTextStyle: const TextStyle(
                        color: Colors.transparent,
                      ),
                      radarBorderData: BorderSide(
                        color: Color(0xffC58940),
                        width: 4,
                      ),
                      gridBorderData: BorderSide(
                        color: Color(0xffF5BE48).withValues(alpha: 0.8),
                        width: 1,
                      ),
                      tickBorderData: BorderSide(
                        color: Color(0xffF5BE48).withValues(alpha: 0.8),
                        width: 1,
                      ),
                      titleTextStyle: const TextStyle(
                        color: Colors.transparent,
                        fontSize: 0,
                      ),
                      dataSets: [
                        RadarDataSet(
                          fillColor: const Color(
                            0xFFD4B675,
                          ).withValues(alpha: 0.5),
                          borderColor: const Color(0xFFD4B675),
                          borderWidth: 2,
                          entryRadius: 5,
                          dataEntries: [
                            RadarEntry(value: accuracy * 10),
                            RadarEntry(value: fluency * 10),
                            RadarEntry(value: rhythm * 10),
                            RadarEntry(value: completeness * 10),
                          ],
                        ),
                      ],
                      tickCount: 5,
                      getTitle: (index, angle) {
                        return RadarChartTitle(text: '', angle: angle);
                      },
                    ),
                  ),
                ),
              ),

              // Titles with left shift applied
              IconTitlePositioner(
                title: 'Accuracy',
                value: (accuracy * 100).toInt(),
                icon: Icons.auto_graph,
                positionAngle: -pi / 2, // Top
                distanceFactor: 1.2,
                offsetX: titleOffsetX,
                offsetY: 0,
                color: Color(0xFF2B77C0),
                chartWidth: chartWidth,
                chartHeight: chartHeight,
                titleWidgetSize: titleWidgetSize,
              ),

              IconTitlePositioner(
                title: 'Fluency',
                value: (fluency * 100).toInt(),
                icon: Icons.chat_bubble_outline,
                positionAngle: 0, // Right
                distanceFactor: 1.1,
                offsetX: titleOffsetX,
                offsetY: 0,
                color: Color(0xFF2A8F2A),
                chartWidth: chartWidth,
                chartHeight: chartHeight,
                titleWidgetSize: titleWidgetSize,
              ),

              IconTitlePositioner(
                title: 'Rhythm',
                value: (rhythm * 100).toInt(),
                icon: Icons.piano,
                positionAngle: pi / 2, // Bottom
                distanceFactor: 1.2,
                offsetX: titleOffsetX,
                offsetY: 0,
                color: Color(0xFF5B3B99),
                chartWidth: chartWidth,
                chartHeight: chartHeight,
                titleWidgetSize: titleWidgetSize,
              ),

              IconTitlePositioner(
                title: 'Completeness',
                value: (completeness * 100).toInt(),
                icon: Icons.check_circle_outline,
                positionAngle: pi, // Left
                distanceFactor: 1.2,
                offsetX: titleOffsetX,
                offsetY: 0,
                color: Color(0xFFD46B2C),
                chartWidth: chartWidth,
                chartHeight: chartHeight,
                titleWidgetSize: titleWidgetSize,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class IconTitlePositioner extends StatelessWidget {
  final String title;
  final int value;
  final IconData icon;
  final double positionAngle;
  final double distanceFactor;
  final double offsetX;
  final double offsetY;
  final Color color;
  final double chartWidth;
  final double chartHeight;
  final double titleWidgetSize; // Size of the container for icon and text

  const IconTitlePositioner({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.positionAngle,
    required this.distanceFactor,
    required this.color,
    required this.chartWidth,
    required this.chartHeight,
    required this.titleWidgetSize,
    this.offsetX = 0.0,
    this.offsetY = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    // The IconTitlePositioner is placed within a Stack that is already sized
    // by containerWidth and containerHeight in the parent SpiderMetricsChart.
    // So, centerX and centerY should be relative to the parent's container dimensions.
    // The parent's containerWidth/Height are chartWidth/Height + 2 * titlePadding.
    // titlePadding is now dynamic, so we need to calculate it here if we want to use it
    // for centering within the *overall* padded container.
    // However, it's simpler to position relative to the chart's center itself.

    // The chart itself is centered in the parent's SizedBox(width: chartWidth, height: chartHeight).
    // The Stack in SpiderMetricsChart has alignment: Alignment.center.
    // The Positioned widget positions its child relative to the Stack.
    // So, 0,0 for Positioned is the top-left of the Stack.
    // The center of the Stack (and thus the chart) is at (parentContainerWidth / 2, parentContainerHeight / 2).

    // Let's use the passed chartWidth and chartHeight for calculations.
    // The center of the chart area (where RadarChart is drawn)
    // within the larger padded container.
    // If the parent Stack is `containerWidth` and `containerHeight` (chart + 2*padding),
    // then its center is `containerWidth/2`, `containerHeight/2`.
    final double parentContainerWidth =
        chartWidth +
        (chartWidth * 0.3 * 2); // chartWidth + 2 * dynamic titlePadding
    final double parentContainerHeight =
        chartHeight +
        (chartHeight * 0.3 * 2); // chartHeight + 2 * dynamic titlePadding

    final double centerX = parentContainerWidth / 2;
    final double centerY = parentContainerHeight / 2;

    // Base distance (half the passed chart width)
    final double baseDistance = chartWidth / 2;

    // Calculate distance from center using the distanceFactor
    final double distance = baseDistance * distanceFactor;

    // Calculate position based on angle and distance, then apply offset
    // These x, y are relative to the center of the parent Stack
    final double x = centerX + distance * cos(positionAngle) + offsetX;
    final double y = centerY + distance * sin(positionAngle) + offsetY;

    // Use the passed titleWidgetSize for the icon/text container
    final double widgetWidth = titleWidgetSize;
    final double widgetHeight = titleWidgetSize;

    return Positioned(
      // Center the widget at calculated position
      left: x - (widgetWidth / 2),
      top: y - (widgetHeight / 2),
      child: Container(
        width: widgetWidth,
        height: widgetHeight,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: .2),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(color: Color(0xFF505050), fontSize: 12),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2),
            Text(
              '$value',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class AnimatedSpiderMetricsChart extends StatefulWidget {
  final int accuracy;
  final int fluency;
  final int rhythm;
  final int completeness;

  const AnimatedSpiderMetricsChart({
    super.key,
    required this.accuracy,
    required this.fluency,
    required this.rhythm,
    required this.completeness,
  });

  @override
  State<AnimatedSpiderMetricsChart> createState() =>
      _AnimatedSpiderMetricsChartState();
}

class _AnimatedSpiderMetricsChartState extends State<AnimatedSpiderMetricsChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SpiderMetricsChart(
          accuracy: widget.accuracy / 100 * _animation.value,
          fluency: widget.fluency / 100 * _animation.value,
          rhythm: widget.rhythm / 100 * _animation.value,
          completeness: widget.completeness / 100 * _animation.value,
        );
      },
    );
  }
}
