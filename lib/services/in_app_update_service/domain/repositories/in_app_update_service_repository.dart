import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Repository for managing in-app updates
class InAppUpdateServiceRepository {
  bool _isInitialized = false;
  AppUpdateInfo? _updateInfo;

  /// Initialize the in-app update service
  Future<Either<AppException, void>> initialize() async {
    try {
      if (_isInitialized) {
        return const Right(null);
      }

      // In-app updates are only supported on Android and iOS
      if (!Platform.isAndroid && !Platform.isIOS) {
        debugPrint('📱 In-app updates not supported on this platform');
        _isInitialized = true;
        return const Right(null);
      }

      debugPrint('🔄 Initializing in-app update service...');

      // Initialize version code cache
      await _initializeVersionCode();

      _isInitialized = true;
      return const Right(null);
    } catch (e) {
      debugPrint('❌ Failed to initialize in-app update service: $e');
      return Left(
        AppException(
          message: 'Failed to initialize in-app update service: $e',
          statusCode: 500,
          identifier: 'in_app_update_init_error',
        ),
      );
    }
  }

  /// Check for available updates
  Future<Either<AppException, AppUpdateInfo?>> checkForUpdate() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // In-app updates are only supported on Android and iOS
      if (!Platform.isAndroid && !Platform.isIOS) {
        debugPrint('📱 In-app updates not supported on this platform');
        return const Right(null);
      }

      debugPrint('🔍 Checking for app updates...');

      final updateInfo = await InAppUpdate.checkForUpdate();
      _updateInfo = updateInfo;

      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        debugPrint('✅ Update available: ${updateInfo.availableVersionCode}');
        debugPrint('📋 Update priority: ${updateInfo.updatePriority}');
        debugPrint(
          '🔧 Immediate update allowed: ${updateInfo.immediateUpdateAllowed}',
        );
        debugPrint(
          '🔄 Flexible update allowed: ${updateInfo.flexibleUpdateAllowed}',
        );
      } else {
        debugPrint('ℹ️ No update available');
      }

      return Right(updateInfo);
    } catch (e) {
      debugPrint('❌ Failed to check for updates: $e');
      return Left(
        AppException(
          message: 'Failed to check for updates: $e',
          statusCode: 500,
          identifier: 'in_app_update_check_error',
        ),
      );
    }
  }

  /// Start an immediate update (blocks the app until update is complete)
  /// Use this for critical updates that must be installed immediately
  Future<Either<AppException, void>> startImmediateUpdate() async {
    try {
      if (_updateInfo == null) {
        final checkResult = await checkForUpdate();
        if (checkResult.isLeft()) {
          return checkResult.fold(
            (error) => Left(error),
            (_) => throw StateError('Should not reach here'),
          );
        }
        final updateInfo = checkResult.fold(
          (_) => throw StateError('Should not reach here'),
          (info) => info,
        );
        if (updateInfo == null) {
          return Left(
            AppException(
              message: 'No update information available',
              statusCode: 404,
              identifier: 'no_update_info',
            ),
          );
        }
      }

      if (_updateInfo!.updateAvailability !=
          UpdateAvailability.updateAvailable) {
        return Left(
          AppException(
            message: 'No update available',
            statusCode: 404,
            identifier: 'no_update_available',
          ),
        );
      }

      if (!_updateInfo!.immediateUpdateAllowed) {
        return Left(
          AppException(
            message: 'Immediate update not allowed',
            statusCode: 403,
            identifier: 'immediate_update_not_allowed',
          ),
        );
      }

      debugPrint('🚀 Starting immediate update...');
      await InAppUpdate.performImmediateUpdate();
      return const Right(null);
    } catch (e) {
      debugPrint('❌ Failed to start immediate update: $e');
      return Left(
        AppException(
          message: 'Failed to start immediate update: $e',
          statusCode: 500,
          identifier: 'immediate_update_failed',
        ),
      );
    }
  }

  /// Start a flexible update (downloads in background, user can continue using app)
  /// Use this for non-critical updates
  Future<Either<AppException, void>> startFlexibleUpdate() async {
    try {
      if (_updateInfo == null) {
        final checkResult = await checkForUpdate();
        if (checkResult.isLeft()) {
          return checkResult.fold(
            (error) => Left(error),
            (_) => throw StateError('Should not reach here'),
          );
        }
        final updateInfo = checkResult.fold(
          (_) => throw StateError('Should not reach here'),
          (info) => info,
        );
        if (updateInfo == null) {
          return Left(
            AppException(
              message: 'No update information available',
              statusCode: 404,
              identifier: 'no_update_info',
            ),
          );
        }
      }

      if (_updateInfo!.updateAvailability !=
          UpdateAvailability.updateAvailable) {
        return Left(
          AppException(
            message: 'No update available',
            statusCode: 404,
            identifier: 'no_update_available',
          ),
        );
      }

      if (!_updateInfo!.flexibleUpdateAllowed) {
        return Left(
          AppException(
            message: 'Flexible update not allowed',
            statusCode: 403,
            identifier: 'flexible_update_not_allowed',
          ),
        );
      }

      debugPrint('📥 Starting flexible update...');
      await InAppUpdate.startFlexibleUpdate();
      return const Right(null);
    } catch (e) {
      debugPrint('❌ Failed to start flexible update: $e');
      return Left(
        AppException(
          message: 'Failed to start flexible update: $e',
          statusCode: 500,
          identifier: 'flexible_update_failed',
        ),
      );
    }
  }

  /// Complete a flexible update (installs the downloaded update)
  /// Call this after a flexible update has been downloaded
  Future<Either<AppException, void>> completeFlexibleUpdate() async {
    try {
      debugPrint('✅ Completing flexible update...');
      await InAppUpdate.completeFlexibleUpdate();
      return const Right(null);
    } catch (e) {
      debugPrint('❌ Failed to complete flexible update: $e');
      return Left(
        AppException(
          message: 'Failed to complete flexible update: $e',
          statusCode: 500,
          identifier: 'complete_flexible_update_failed',
        ),
      );
    }
  }

  /// Check if a flexible update is downloaded and ready to install
  Future<Either<AppException, bool>> isFlexibleUpdateDownloaded() async {
    try {
      if (_updateInfo == null) {
        final checkResult = await checkForUpdate();
        if (checkResult.isLeft()) {
          return checkResult.fold(
            (error) => Left(error),
            (_) => throw StateError('Should not reach here'),
          );
        }
        final updateInfo = checkResult.fold(
          (_) => throw StateError('Should not reach here'),
          (info) => info,
        );
        if (updateInfo == null) {
          return const Right(false);
        }
      }

      final isDownloaded =
          _updateInfo!.installStatus == InstallStatus.downloaded;
      debugPrint('📦 Flexible update downloaded: $isDownloaded');
      return Right(isDownloaded);
    } catch (e) {
      debugPrint('❌ Failed to check flexible update status: $e');
      return Left(
        AppException(
          message: 'Failed to check flexible update status: $e',
          statusCode: 500,
          identifier: 'check_flexible_update_status_failed',
        ),
      );
    }
  }

  /// Get current update information
  AppUpdateInfo? get updateInfo => _updateInfo;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if update is available
  bool get isUpdateAvailable =>
      _updateInfo?.updateAvailability == UpdateAvailability.updateAvailable;

  /// Check if immediate update is allowed
  bool get isImmediateUpdateAllowed =>
      _updateInfo?.immediateUpdateAllowed ?? false;

  /// Check if flexible update is allowed
  bool get isFlexibleUpdateAllowed =>
      _updateInfo?.flexibleUpdateAllowed ?? false;

  /// Check if this is a critical update that requires immediate installation
  /// Critical updates are determined by version code difference > 5
  bool get isCriticalUpdate {
    if (_updateInfo == null) return false;

    final currentVersionCode = _getCurrentVersionCode();
    final availableVersionCode = _updateInfo!.availableVersionCode;

    if (availableVersionCode != null && currentVersionCode > 0) {
      final versionDifference = availableVersionCode - currentVersionCode;
      debugPrint(
        '📊 Version difference: $versionDifference (current: $currentVersionCode, available: $availableVersionCode)',
      );
      return versionDifference > 5;
    }

    return false;
  }

  /// Check if this is a normal update (version code difference <= 5)
  bool get isNormalUpdate => !isCriticalUpdate;

  /// Legacy compatibility - maps to critical/normal system
  /// Returns 5 for critical updates, 2 for normal updates
  int get updatePriority => isCriticalUpdate ? 5 : 2;

  /// Legacy compatibility - critical updates are considered high priority
  bool get isHighPriorityUpdate => isCriticalUpdate;

  /// Get current app version code
  int _getCurrentVersionCode() {
    // This should be called after PackageInfo is available
    // For now, we'll use a cached value or return 1 as fallback
    return _cachedVersionCode ?? 1;
  }

  /// Cache for current version code to avoid repeated async calls
  int? _cachedVersionCode;

  /// Initialize version code cache
  Future<void> _initializeVersionCode() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _cachedVersionCode = int.tryParse(packageInfo.buildNumber) ?? 1;
      debugPrint('📱 Current version code: $_cachedVersionCode');
    } catch (e) {
      debugPrint('❌ Failed to get version code: $e');
      _cachedVersionCode = 1;
    }
  }
}
