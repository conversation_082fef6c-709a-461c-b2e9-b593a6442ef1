import 'package:flutter/material.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class LibraryHeader extends StatelessWidget {
  const LibraryHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final loc = context.loc;

    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(context.loc.explore_the_courses, style: textTheme.titleLarge),
            const SizedBox(height: 10),
            Text(loc.level_pitch_sentences, style: TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }
}
