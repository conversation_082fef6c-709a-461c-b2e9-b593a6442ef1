// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pronunciation_result_formatted.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PronunciationResultFormatted _$PronunciationResultFormattedFromJson(
  Map<String, dynamic> json,
) => _PronunciationResultFormatted(
  originalText: json['originalText'] as String,
  recordedInput: json['recordedInput'] as String,
  accuracyScore: (json['accuracyScore'] as num).toDouble(),
  fluencyScore: (json['fluencyScore'] as num).toDouble(),
  prosodyScore: (json['prosodyScore'] as num).toDouble(),
  completenessScore: (json['completenessScore'] as num).toDouble(),
  pronScore: (json['pronScore'] as num).toDouble(),
  formattedResult:
      (json['formattedResult'] as List<dynamic>)
          .map((e) => FormattedResult.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$PronunciationResultFormattedToJson(
  _PronunciationResultFormatted instance,
) => <String, dynamic>{
  'originalText': instance.originalText,
  'recordedInput': instance.recordedInput,
  'accuracyScore': instance.accuracyScore,
  'fluencyScore': instance.fluencyScore,
  'prosodyScore': instance.prosodyScore,
  'completenessScore': instance.completenessScore,
  'pronScore': instance.pronScore,
  'formattedResult': instance.formattedResult,
};

_FormattedResult _$FormattedResultFromJson(Map<String, dynamic> json) =>
    _FormattedResult(
      text: json['text'] as String,
      accuracyScore: (json['accuracyScore'] as num).toDouble(),
      errorType: json['errorType'] as String,
    );

Map<String, dynamic> _$FormattedResultToJson(_FormattedResult instance) =>
    <String, dynamic>{
      'text': instance.text,
      'accuracyScore': instance.accuracyScore,
      'errorType': instance.errorType,
    };

_ScoreResult _$ScoreResultFromJson(Map<String, dynamic> json) => _ScoreResult(
  prosody: (json['prosody'] as num?)?.toInt() ?? 0,
  phoneme: (json['phoneme'] as num?)?.toInt() ?? 0,
  completeness: (json['completeness'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$ScoreResultToJson(_ScoreResult instance) =>
    <String, dynamic>{
      'prosody': instance.prosody,
      'phoneme': instance.phoneme,
      'completeness': instance.completeness,
    };
