import 'package:freezed_annotation/freezed_annotation.dart';

part 'game_word.freezed.dart';
part 'game_word.g.dart';

@freezed
sealed class WordObject with _$WordObject {
  factory WordObject({
    required String word,
    double? verticalPosition,
    double? horizontalPosition,
    double? speed,
    @Default(false) bool selected,
  }) = _WordObject;
  factory WordObject.fromJson(Map<String, dynamic> json) =>
      _$WordObjectFromJson(json);
}

@freezed
sealed class MemoryFlashTopic with _$MemoryFlashTopic {
  factory MemoryFlashTopic({
    int? order,
    String? title,
    @J<PERSON>Key(name: 'icon_url') String? icon,
    @JsonKey(name: 'image_url') String? image,
    String? references,
  }) = _MemoryFlashTopic;
  factory MemoryFlashTopic.fromJson(Map<String, dynamic> json) =>
      _$MemoryFlashTopicFromJson(json);
}

@freezed
sealed class MemoryFlash with _$MemoryFlash {
  factory MemoryFlash({
    int? order,
    String? topic,
    int? countdown,
    String? references,
    @J<PERSON><PERSON><PERSON>(name: 'list_text', fromJson: fromMapToString)
    @Default([])
    List<String> listText,
  }) = _MemoryFlash;
  factory MemoryFlash.fromJson(Map<String, dynamic> json) =>
      _$MemoryFlashFromJson(json);
}

List<String> fromMapToString(value) {
  List<String> temp = [];
  for (var item in value) {
    temp.add(item['text'].toString());
  }
  return temp;
}
