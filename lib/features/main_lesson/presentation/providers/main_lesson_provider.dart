import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

class MainLessonStateNotifier extends StateNotifier<MainLessonState> {
  MainLessonStateNotifier() : super(MainLessonState());

  void updateLastPronunciation(LastCourse data) {
    state = state.copyWith(lastPronunciation: data);
  }

  void updateLastConversation(LastCourse data) {
    state = state.copyWith(lastConversation: data);
  }

  void updateLastListening(LastCourse data) {
    state = state.copyWith(lastListening: data);
  }

  void updateLastSpeaking(LastCourse data) {
    state = state.copyWith(lastSpeaking: data);
  }

  void updateFromLastCourse(bool fromLastCourse) {
    state = state.copyWith(fromLastCourse: fromLastCourse);
  }

  // Add other update methods as needed
}

@riverpod
StateNotifierProvider<MainLessonStateNotifier, MainLessonState>
mainLessonStateProvider = StateNotifierProvider(
  (ref) => MainLessonStateNotifier(),
);
