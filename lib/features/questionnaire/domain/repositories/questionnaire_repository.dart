import 'package:selfeng/features/questionnaire/domain/models/questionnaire_model.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class QuestionnaireRepository {
  Future<Either<AppException, dynamic>> getListQuestionnaire();

  Future<Either<AppException, dynamic>> saveAnswer(answer);
}

class QuestionnaireRepositoryImpl extends QuestionnaireRepository {
  // final FirebaseFirestore dataSource;
  final FirestoreServiceRepository dataSource;

  QuestionnaireRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppException, dynamic>> getListQuestionnaire() async {
    try {
      final questionsSnapshot =
          await dataSource.fireStore
              .collection('new-self-assesment')
              .where('is_active', isEqualTo: true)
              .orderBy('order')
              .get();

      final q = questionsSnapshot.docs.map((question) async {
        final tmpQuestion = Map<String, dynamic>.from(question.data());
        tmpQuestion['questionId'] = question.reference.path;

        final choices =
            await question.reference
                .collection('choices')
                .orderBy('label')
                .get();

        tmpQuestion['choices'] = choices.docs.map((e) => e.data()).toList();

        return QuestionnaireModel.fromJson(tmpQuestion);
      });

      final questions = await Future.wait(q);

      return Right(questions);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch questionnaire',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, dynamic>> saveAnswer(answer) async {
    try {
      await dataSource
          .dataUser()
          .collection('testResult')
          .doc('selfAssesment')
          .set({'is_done': true})
          .then((e) {
            answer.forEach(
              (item) => dataSource
                  .dataUser()
                  .collection('testResult')
                  .doc('selfAssesment')
                  .collection('answers')
                  .add(item),
            );
          });
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'save questionnaire',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
