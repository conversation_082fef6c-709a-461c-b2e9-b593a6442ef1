import 'package:dio/dio.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:mocktail/mocktail.dart';

/// HTTP client mocking utilities for testing API interactions
/// Uses http_mock_adapter for realistic HTTP mocking scenarios

class HttpMocks {
  HttpMocks._();

  /// Creates a Dio instance with Dio<PERSON><PERSON>pter for mocking
  static Dio createMockDio() {
    final dio = Dio();
    final dioAdapter = DioAdapter(dio: dio);
    return dio;
  }

  /// Creates a DioAdapter for setting up mock responses
  static DioAdapter createDioAdapter(Dio dio) {
    return DioAdapter(dio: dio);
  }

  /// Sets up a successful GET request mock
  static void setupGetSuccess(
    DioAdapter adapter,
    String path,
    Map<String, dynamic> responseData, {
    int statusCode = 200,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) {
    adapter.onGet(
      path,
      queryParameters: queryParameters,
      headers: headers,
      (server) => server.reply(statusCode, responseData),
    );
  }

  /// Sets up a successful POST request mock
  static void setupPostSuccess(
    DioAdapter adapter,
    String path,
    Map<String, dynamic> responseData, {
    int statusCode = 201,
    dynamic requestData,
    Map<String, dynamic>? headers,
  }) {
    adapter.onPost(
      path,
      data: requestData,
      headers: headers,
      (server) => server.reply(statusCode, responseData),
    );
  }

  /// Sets up a successful PUT request mock
  static void setupPutSuccess(
    DioAdapter adapter,
    String path,
    Map<String, dynamic> responseData, {
    int statusCode = 200,
    dynamic requestData,
    Map<String, dynamic>? headers,
  }) {
    adapter.onPut(
      path,
      data: requestData,
      headers: headers,
      (server) => server.reply(statusCode, responseData),
    );
  }

  /// Sets up a successful DELETE request mock
  static void setupDeleteSuccess(
    DioAdapter adapter,
    String path, {
    int statusCode = 204,
    Map<String, dynamic>? headers,
  }) {
    adapter.onDelete(
      path,
      headers: headers,
      (server) => server.reply(statusCode, {}),
    );
  }

  /// Sets up a network error mock
  static void setupNetworkError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
    String errorMessage = 'Network connection failed',
  }) {
    final dioError = DioException(
      requestOptions: RequestOptions(path: path, method: method),
      type: DioExceptionType.connectionTimeout,
      message: errorMessage,
    );

    switch (method.toUpperCase()) {
      case 'GET':
        adapter.onGet(path, (server) => server.throws(500, dioError));
        break;
      case 'POST':
        adapter.onPost(path, (server) => server.throws(500, dioError));
        break;
      case 'PUT':
        adapter.onPut(path, (server) => server.throws(500, dioError));
        break;
      case 'DELETE':
        adapter.onDelete(path, (server) => server.throws(500, dioError));
        break;
    }
  }

  /// Sets up a server error mock (5xx)
  static void setupServerError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
    int statusCode = 500,
    String errorMessage = 'Internal server error',
  }) {
    final errorResponse = {'error': errorMessage, 'statusCode': statusCode};

    switch (method.toUpperCase()) {
      case 'GET':
        adapter.onGet(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'POST':
        adapter.onPost(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'PUT':
        adapter.onPut(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'DELETE':
        adapter.onDelete(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
    }
  }

  /// Sets up a client error mock (4xx)
  static void setupClientError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
    int statusCode = 400,
    String errorMessage = 'Bad request',
  }) {
    final errorResponse = {'error': errorMessage, 'statusCode': statusCode};

    switch (method.toUpperCase()) {
      case 'GET':
        adapter.onGet(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'POST':
        adapter.onPost(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'PUT':
        adapter.onPut(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
      case 'DELETE':
        adapter.onDelete(
          path,
          (server) => server.reply(statusCode, errorResponse),
        );
        break;
    }
  }

  /// Sets up an unauthorized error mock (401)
  static void setupUnauthorizedError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
  }) {
    setupClientError(
      adapter,
      path,
      method: method,
      statusCode: 401,
      errorMessage: 'Unauthorized',
    );
  }

  /// Sets up a forbidden error mock (403)
  static void setupForbiddenError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
  }) {
    setupClientError(
      adapter,
      path,
      method: method,
      statusCode: 403,
      errorMessage: 'Forbidden',
    );
  }

  /// Sets up a not found error mock (404)
  static void setupNotFoundError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
  }) {
    setupClientError(
      adapter,
      path,
      method: method,
      statusCode: 404,
      errorMessage: 'Not found',
    );
  }

  /// Sets up a timeout error mock
  static void setupTimeoutError(
    DioAdapter adapter,
    String path, {
    String method = 'GET',
  }) {
    final dioError = DioException(
      requestOptions: RequestOptions(path: path, method: method),
      type: DioExceptionType.receiveTimeout,
      message: 'Request timeout',
    );

    switch (method.toUpperCase()) {
      case 'GET':
        adapter.onGet(path, (server) => server.throws(408, dioError));
        break;
      case 'POST':
        adapter.onPost(path, (server) => server.throws(408, dioError));
        break;
      case 'PUT':
        adapter.onPut(path, (server) => server.throws(408, dioError));
        break;
      case 'DELETE':
        adapter.onDelete(path, (server) => server.throws(408, dioError));
        break;
    }
  }
}

/// Predefined HTTP mock scenarios for common testing patterns
class HttpMockScenarios {
  HttpMockScenarios._();

  /// Sets up a complete API success scenario
  static void setupApiSuccessScenario(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    // User authentication
    HttpMocks.setupPostSuccess(adapter, '$baseUrl/auth/login', {
      'token': 'mock_jwt_token',
      'user': {
        'id': 'user_123',
        'email': '<EMAIL>',
        'name': 'Test User',
      },
    });

    // User data retrieval
    HttpMocks.setupGetSuccess(adapter, '$baseUrl/user/profile', {
      'id': 'user_123',
      'email': '<EMAIL>',
      'name': 'Test User',
      'preferences': {'language': 'en', 'theme': 'light'},
    });

    // Lesson data
    HttpMocks.setupGetSuccess(adapter, '$baseUrl/lessons', {
      'lessons': [
        {
          'id': 'lesson_1',
          'title': 'Basic Pronunciation',
          'level': 'beginner',
          'chapter': 1,
        },
        {
          'id': 'lesson_2',
          'title': 'Advanced Speaking',
          'level': 'intermediate',
          'chapter': 2,
        },
      ],
    });

    // Progress tracking
    HttpMocks.setupPostSuccess(adapter, '$baseUrl/progress', {
      'success': true,
      'message': 'Progress saved successfully',
    });
  }

  /// Sets up a network failure scenario
  static void setupNetworkFailureScenario(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    HttpMocks.setupNetworkError(adapter, '$baseUrl/auth/login', method: 'POST');
    HttpMocks.setupNetworkError(adapter, '$baseUrl/user/profile');
    HttpMocks.setupNetworkError(adapter, '$baseUrl/lessons');
    HttpMocks.setupNetworkError(adapter, '$baseUrl/progress', method: 'POST');
  }

  /// Sets up an authentication failure scenario
  static void setupAuthFailureScenario(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    HttpMocks.setupUnauthorizedError(
      adapter,
      '$baseUrl/auth/login',
      method: 'POST',
    );
    HttpMocks.setupUnauthorizedError(adapter, '$baseUrl/user/profile');
    HttpMocks.setupUnauthorizedError(adapter, '$baseUrl/lessons');
    HttpMocks.setupUnauthorizedError(
      adapter,
      '$baseUrl/progress',
      method: 'POST',
    );
  }

  /// Sets up a server error scenario
  static void setupServerErrorScenario(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    HttpMocks.setupServerError(adapter, '$baseUrl/auth/login', method: 'POST');
    HttpMocks.setupServerError(adapter, '$baseUrl/user/profile');
    HttpMocks.setupServerError(adapter, '$baseUrl/lessons');
    HttpMocks.setupServerError(adapter, '$baseUrl/progress', method: 'POST');
  }

  /// Sets up pronunciation API mocks
  static void setupPronunciationApiMocks(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    // Pronunciation check endpoint
    HttpMocks.setupPostSuccess(adapter, '$baseUrl/pronunciation/check', {
      'score': 85.5,
      'feedback': 'Good pronunciation overall',
      'errors': ['Slight mispronunciation of "th" sound'],
      'suggestions': ['Practice tongue placement for "th"'],
    });

    // Audio upload endpoint
    HttpMocks.setupPostSuccess(adapter, '$baseUrl/audio/upload', {
      'audioId': 'audio_123',
      'url': 'https://example.com/audio/audio_123.wav',
      'duration': 5.2,
    });
  }

  /// Sets up lesson content API mocks
  static void setupLessonContentMocks(
    DioAdapter adapter, {
    required String baseUrl,
  }) {
    // Chapter content
    HttpMocks.setupGetSuccess(adapter, '$baseUrl/chapters/beginner/1', {
      'chapter': 1,
      'title': 'Introduction to English',
      'description': 'Basic English pronunciation and vocabulary',
      'lessons': [
        {
          'id': 'lesson_1_1',
          'type': 'pronunciation',
          'content': 'Hello, how are you?',
          'audioUrl': 'https://example.com/audio/lesson_1_1.mp3',
        },
      ],
    });

    // Speaking exercises
    HttpMocks.setupGetSuccess(adapter, '$baseUrl/speaking/exercises', {
      'exercises': [
        {
          'id': 'speaking_1',
          'text': 'Repeat after me: Hello world',
          'expectedDuration': 3.0,
          'difficulty': 'easy',
        },
      ],
    });

    // Listening exercises
    HttpMocks.setupGetSuccess(adapter, '$baseUrl/listening/exercises', {
      'exercises': [
        {
          'id': 'listening_1',
          'audioUrl': 'https://example.com/audio/listening_1.mp3',
          'questions': [
            {
              'id': 'q1',
              'question': 'What did you hear?',
              'options': ['Hello', 'Hi', 'Hey', 'Goodbye'],
              'correctAnswer': 'Hello',
            },
          ],
        },
      ],
    });
  }
}

/// Mock Dio class for unit testing without http_mock_adapter
class MockDio extends Mock implements Dio {}

class MockResponse extends Mock implements Response {}

class MockRequestOptions extends Mock implements RequestOptions {}

class MockDioException extends Mock implements DioException {}
