import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/features/authentication/presentation/providers/state/auth_state.dart';
import 'package:selfeng/features/authentication/presentation/providers/auth_controller.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart' as app_user;
import 'package:selfeng/shared/exceptions/http_exception.dart';

// Mock classes
class MockUserRepository extends Mock implements UserRepository {}

class MockAuthRepository extends Mock implements AuthRepository {}

class MockGoogleSignInRepository extends Mock
    implements GoogleSignInRepository {}

void main() {
  group('AuthController Tests', () {
    late MockUserRepository mockUserRepository;
    late MockAuthRepository mockAuthRepository;
    late MockGoogleSignInRepository mockGoogleSignInRepository;

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockAuthRepository = MockAuthRepository();
      mockGoogleSignInRepository = MockGoogleSignInRepository();

      // Register fallback values for mocktail
      registerFallbackValue(const app_user.User());
      registerFallbackValue(AuthConfig());

      // Setup default mock behaviors
      when(() => mockUserRepository.deleteUser()).thenAnswer((_) async => true);
      when(
        () => mockUserRepository.saveUser(user: any(named: 'user')),
      ).thenAnswer((_) async => true);

      // Setup auth repository mocks
      when(() => mockAuthRepository.currentUser).thenReturn(null);
      when(() => mockAuthRepository.signOut()).thenAnswer((_) async {});
      when(
        () => mockAuthRepository.getIdToken(any()),
      ).thenAnswer((_) async => 'test_token');

      // Setup Google Sign In repository mocks
      when(
        () => mockGoogleSignInRepository.supportsAuthenticate(),
      ).thenReturn(true);
      when(
        () => mockGoogleSignInRepository.authenticate(),
      ).thenAnswer((_) async => null);
      when(
        () => mockGoogleSignInRepository.disconnect(),
      ).thenAnswer((_) async {});
      when(
        () => mockGoogleSignInRepository.initialize(),
      ).thenAnswer((_) async {});
      when(() => mockGoogleSignInRepository.signOut()).thenAnswer((_) async {});
      when(
        () => mockGoogleSignInRepository.authenticationEvents,
      ).thenAnswer((_) => const Stream.empty());
    });

    group('AuthState Tests', () {
      test('should create initial state', () {
        const state = AuthState.initial();
        expect(state, isA<Initial>());
      });

      test('should create loading state', () {
        const state = AuthState.loading();
        expect(state, isA<Loading>());
      });

      test('should create signed in state', () {
        const user = app_user.User(
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          image: '',
          token: 'test_token',
        );
        final state = AuthState.signedIn(user: user);
        expect(state, isA<SignedIn>());
        expect((state as SignedIn).user, equals(user));
      });

      test('should create signed out state', () {
        const state = AuthState.signedOut();
        expect(state, isA<SignedOut>());
      });

      test('should create failure state', () {
        final exception = AppException(
          message: 'Test error',
          statusCode: 400,
          identifier: 'TEST_ERROR',
        );
        final state = AuthState.failure(exception: exception);
        expect(state, isA<Failure>());
        expect((state as Failure).exception, equals(exception));
      });

      test('should create success state', () {
        const state = AuthState.success();
        expect(state, isA<Success>());
      });
    });

    group('Repository Integration', () {
      test('should handle user save operation', () async {
        const user = app_user.User(
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          image: '',
          token: 'test_token',
        );

        final result = await mockUserRepository.saveUser(user: user);
        expect(result, isTrue);
        verify(() => mockUserRepository.saveUser(user: user)).called(1);
      });

      test('should handle user delete operation', () async {
        final result = await mockUserRepository.deleteUser();
        expect(result, isTrue);
        verify(() => mockUserRepository.deleteUser()).called(1);
      });
    });

    group('AuthController Functionality', () {
      test('should create AuthController with test constructor', () {
        final controller = AuthController.test(
          userRepository: mockUserRepository,
          authRepository: mockAuthRepository,
          googleSignInRepository: mockGoogleSignInRepository,
        );

        expect(controller, isA<AuthController>());
      });

      test('should verify repository methods are called for logout', () async {
        // Test that the individual methods are called correctly
        // without testing the state management part

        // Test auth repository signOut
        await mockAuthRepository.signOut();
        verify(() => mockAuthRepository.signOut()).called(1);

        // Test google sign in disconnect
        await mockGoogleSignInRepository.disconnect();
        verify(() => mockGoogleSignInRepository.disconnect()).called(1);

        // Test user repository deleteUser
        await mockUserRepository.deleteUser();
        verify(() => mockUserRepository.deleteUser()).called(1);
      });

      test('should verify repository methods are called for login', () async {
        // Test that the individual methods are called correctly

        // Test google sign in supportsAuthenticate
        final supportsAuth = mockGoogleSignInRepository.supportsAuthenticate();
        expect(supportsAuth, isTrue);
        verify(
          () => mockGoogleSignInRepository.supportsAuthenticate(),
        ).called(1);

        // Test google sign in authenticate
        await mockGoogleSignInRepository.authenticate();
        verify(() => mockGoogleSignInRepository.authenticate()).called(1);
      });

      test('should initialize with default config', () {
        final config = AuthConfig();
        expect(config.googleSignInTimeout, const Duration(seconds: 30));
        expect(
          config.googleSignInRetryDelay,
          const Duration(milliseconds: 500),
        );
        expect(config.initializationDelay, const Duration(seconds: 3));
        expect(config.rethrowGoogleSignInErrors, isFalse);
        expect(config.rethrowInitializationErrors, isFalse);
        expect(config.rethrowNotificationErrors, isFalse);
      });

      test('should allow custom config', () {
        final config = AuthConfig(
          googleSignInTimeout: const Duration(seconds: 10),
          googleSignInRetryDelay: const Duration(milliseconds: 10),
          initializationDelay: const Duration(seconds: 1),
          rethrowGoogleSignInErrors: true,
          rethrowInitializationErrors: true,
          rethrowNotificationErrors: true,
        );

        expect(config.googleSignInTimeout, const Duration(seconds: 10));
        expect(config.googleSignInRetryDelay, const Duration(milliseconds: 10));
        expect(config.initializationDelay, const Duration(seconds: 1));
        expect(config.rethrowGoogleSignInErrors, isTrue);
        expect(config.rethrowInitializationErrors, isTrue);
        expect(config.rethrowNotificationErrors, isTrue);
      });
    });
  });
}
