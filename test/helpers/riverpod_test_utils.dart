import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'test_helpers.dart';

/// Utilities for testing Riverpod providers and state management
class RiverpodTestUtils {
  RiverpodTestUtils._();

  /// Creates a ProviderContainer with common overrides for testing
  static ProviderContainer createTestContainer({
    List<Override>? additionalOverrides,
    bool includeCommonMocks = true,
  }) {
    final overrides = <Override>[];

    if (includeCommonMocks) {
      overrides.addAll(_getCommonMockOverrides());
    }

    if (additionalOverrides != null) {
      overrides.addAll(additionalOverrides);
    }

    return ProviderContainer(overrides: overrides);
  }

  /// Creates a test widget with ProviderScope and common overrides
  static Widget createTestWidgetWithProviders({
    required Widget child,
    List<Override>? overrides,
    bool includeCommonMocks = true,
  }) {
    final allOverrides = <Override>[];

    if (includeCommonMocks) {
      allOverrides.addAll(_getCommonMockOverrides());
    }

    if (overrides != null) {
      allOverrides.addAll(overrides);
    }

    return TestHelpers.createTestWidget(child: child, overrides: allOverrides);
  }

  /// Pumps a widget with ProviderScope for testing
  static Future<void> pumpWidgetWithProviders(
    WidgetTester tester, {
    required Widget child,
    List<Override>? overrides,
    bool includeCommonMocks = true,
  }) async {
    await tester.pumpWidget(
      createTestWidgetWithProviders(
        child: child,
        overrides: overrides,
        includeCommonMocks: includeCommonMocks,
      ),
    );
  }

  /// Tests a provider's initial state
  static void testProviderInitialState<T>(
    Provider<T> provider,
    T expectedInitialState, {
    List<Override>? overrides,
  }) {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final state = container.read(provider);
    expect(state, equals(expectedInitialState));
  }

  /// Tests an AsyncNotifier provider's initial state
  static void testAsyncNotifierInitialState<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider, {
    List<Override>? overrides,
  }) {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final state = container.read(provider);
    expect(state, isA<AsyncLoading<T>>());
  }

  /// Tests provider state changes
  static Future<void> testProviderStateChange<T>(
    StateNotifierProvider<StateNotifier<T>, T> provider,
    void Function(StateNotifier<T>) action,
    T expectedState, {
    List<Override>? overrides,
  }) async {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final notifier = container.read(provider.notifier);
    action(notifier);

    await container.pump();
    final state = container.read(provider);
    expect(state, equals(expectedState));
  }

  /// Tests AsyncNotifier state changes
  static Future<void> testAsyncNotifierStateChange<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
    Future<void> Function(AsyncNotifier<T>) action,
    AsyncValue<T> expectedState, {
    List<Override>? overrides,
  }) async {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final notifier = container.read(provider.notifier);
    await action(notifier);

    await container.pump();
    final state = container.read(provider);
    expect(state, equals(expectedState));
  }

  /// Listens to provider changes and collects states
  static Future<List<T>> collectProviderStates<T>(
    Provider<T> provider,
    Future<void> Function() action, {
    List<Override>? overrides,
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final states = <T>[];
    final subscription = container.listen(
      provider,
      (previous, next) => states.add(next),
    );

    try {
      await action();
      await Future.delayed(const Duration(milliseconds: 100));
      return states;
    } finally {
      subscription.close();
    }
  }

  /// Tests provider error handling
  static Future<void> testProviderError<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
    Future<void> Function(AsyncNotifier<T>) action,
    Object expectedError, {
    List<Override>? overrides,
  }) async {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final notifier = container.read(provider.notifier);
    await action(notifier);

    await container.pump();
    final state = container.read(provider);
    expect(state, isA<AsyncError<T>>());
    expect(state.error, equals(expectedError));
  }

  /// Waits for an AsyncNotifier to complete loading
  static Future<void> waitForAsyncNotifier<T>(
    ProviderContainer container,
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final completer = Completer<void>();
    late ProviderSubscription subscription;

    subscription = container.listen(provider, (previous, next) {
      if (!next.isLoading && !completer.isCompleted) {
        completer.complete();
        subscription.close();
      }
    });

    await completer.future.timeout(timeout);
  }

  /// Creates mock overrides for common providers
  static List<Override> _getCommonMockOverrides() {
    // Return empty list for now - specific tests should provide their own overrides
    return [];
  }

  /// Creates a mock override for a specific provider
  static Override createMockOverride<T>(Provider<T> provider, T mockValue) {
    return provider.overrideWithValue(mockValue);
  }

  /// Creates a mock override for a StateNotifier provider
  static Override createStateNotifierMockOverride<
    TNotifier extends StateNotifier<TState>,
    TState
  >(StateNotifierProvider<TNotifier, TState> provider, TNotifier mockNotifier) {
    return provider.overrideWith((ref) => mockNotifier);
  }

  /// Creates a mock override for an AsyncNotifier provider
  static Override createAsyncNotifierMockOverride<
    TNotifier extends AsyncNotifier<TState>,
    TState
  >(AsyncNotifierProvider<TNotifier, TState> provider, TNotifier mockNotifier) {
    return provider.overrideWith(() => mockNotifier);
  }

  /// Verifies that a provider dependency is called
  static void verifyProviderDependency<T>(
    ProviderContainer container,
    Provider<T> provider,
  ) {
    // Read the provider to ensure it's initialized
    container.read(provider);
    // Additional verification logic can be added here
  }

  /// Tests provider disposal
  static void testProviderDisposal<T>(
    Provider<T> provider, {
    List<Override>? overrides,
  }) {
    final container = createTestContainer(additionalOverrides: overrides);

    // Read the provider to initialize it
    container.read(provider);

    // Dispose the container
    container.dispose();

    // Verify that the provider is disposed
    expect(() => container.read(provider), throwsStateError);
  }

  /// Creates a test scenario with multiple provider states
  static Future<void> testProviderScenario<T>({
    required AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
    required Future<void> Function(AsyncNotifier<T>) action,
    required List<AsyncValue<T>> expectedStates,
    List<Override>? overrides,
  }) async {
    final container = createTestContainer(additionalOverrides: overrides);
    addTearDown(container.dispose);

    final states = <AsyncValue<T>>[];
    final subscription = container.listen(
      provider,
      (previous, next) => states.add(next),
    );

    try {
      final notifier = container.read(provider.notifier);
      await action(notifier);
      await container.pump();

      expect(states.length, equals(expectedStates.length));
      for (int i = 0; i < states.length; i++) {
        expect(states[i].runtimeType, equals(expectedStates[i].runtimeType));
      }
    } finally {
      subscription.close();
    }
  }
}

/// Extension methods for ProviderContainer to add testing utilities
extension ProviderContainerTestExtensions on ProviderContainer {
  /// Pumps the container to process pending operations
  Future<void> pump() async {
    await Future.delayed(Duration.zero);
  }

  /// Reads a provider and expects a specific value
  void expectProvider<T>(Provider<T> provider, T expectedValue) {
    final value = read(provider);
    expect(value, equals(expectedValue));
  }

  /// Reads an AsyncNotifier and expects it to be loading
  void expectAsyncLoading<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
  ) {
    final state = read(provider);
    expect(state, isA<AsyncLoading<T>>());
  }

  /// Reads an AsyncNotifier and expects it to have data
  void expectAsyncData<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
    T expectedData,
  ) {
    final state = read(provider);
    expect(state, isA<AsyncData<T>>());
    expect(state.value, equals(expectedData));
  }

  /// Reads an AsyncNotifier and expects it to have an error
  void expectAsyncError<T>(
    AsyncNotifierProvider<AsyncNotifier<T>, T> provider,
    Object expectedError,
  ) {
    final state = read(provider);
    expect(state, isA<AsyncError<T>>());
    expect(state.error, equals(expectedError));
  }
}

/// Test utilities specifically for testing Riverpod code generation
class RiverpodCodegenTestUtils {
  RiverpodCodegenTestUtils._();

  /// Tests a generated provider with family parameters
  static void testFamilyProvider<T, Arg>(
    ProviderFamily<T, Arg> family,
    Arg argument,
    T expectedValue, {
    List<Override>? overrides,
  }) {
    final container = RiverpodTestUtils.createTestContainer(
      additionalOverrides: overrides,
    );
    addTearDown(container.dispose);

    final provider = family(argument);
    final value = container.read(provider);
    expect(value, equals(expectedValue));
  }

  /// Tests a generated AsyncNotifier with family parameters
  /// Note: This is a simplified version - implement specific family testing as needed
  static void testAsyncNotifierFamily<T, Arg>() {
    // Placeholder for family testing - implement specific cases as needed
  }
}
