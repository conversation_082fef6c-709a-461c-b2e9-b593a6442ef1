import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyles {
  static const String fontFamily = 'Lato';

  ////////////////////////////////Headline
  static TextStyle hLg = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 32.0,
      fontWeight: FontWeight.w700,
      height: 1.25,
    ),
  );
  static TextStyle h = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 28.0,
      fontWeight: FontWeight.w600,
      height: 1.28,
    ),
  );
  static TextStyle hSm = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 24.0,
      fontWeight: FontWeight.w700,
      height: 1.3,
    ),
  );

  ////////////////////////////////Title
  static TextStyle titleLg = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 22.0,
      fontWeight: FontWeight.w700,
      height: 1.27,
    ),
  );
  static TextStyle title = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 20.0,
      fontWeight: FontWeight.w700,
      height: 1.5,
      letterSpacing: 0.15,
    ),
  );
  static TextStyle titleSm = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.w600,
      height: 1.42,
      letterSpacing: 0.1,
    ),
  );

  ////////////////////////////////Body
  static TextStyle bodyLg = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 16.0,
      fontWeight: FontWeight.w600,
      height: 1.5,
      letterSpacing: 0.5,
    ),
  );
  static TextStyle body = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.w700,
      height: 1.42,
      letterSpacing: 0.25,
    ),
  );
  static TextStyle bodySm = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 12.0,
      fontWeight: FontWeight.w600,
      height: 1.142,
      letterSpacing: 0.4,
    ),
  );

  ////////////////////////////////Label
  static TextStyle labelLg = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.w500,
      height: 1.42,
      letterSpacing: 0.1,
    ),
  );
  static TextStyle label = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 12.0,
      fontWeight: FontWeight.w700,
      height: 1.33,
      letterSpacing: 0.5,
    ),
  );
  static TextStyle labelSm = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 11.0,
      fontWeight: FontWeight.w700,
      height: 1.45,
      letterSpacing: 0.5,
    ),
  );

  ////////////////////////////////Ad Ons
  static TextStyle addOnsTitle = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 24.0,
      fontWeight: FontWeight.w600,
      height: 1.3,
    ),
  );
  static TextStyle addOnsBody = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 18.0,
      fontWeight: FontWeight.w400,
      height: 1.5,
      letterSpacing: 0.5,
    ),
  );
  static TextStyle addOnsBodySemiBold = GoogleFonts.lato(
    textStyle: const TextStyle(
      fontSize: 18.0,
      fontWeight: FontWeight.w600,
      height: 1.5,
      letterSpacing: 0.5,
    ),
  );
}
