import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/domain/providers/level_info_provider.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_state.dart';

part 'library_controller.g.dart';

@Riverpod(keepAlive: false)
class LibraryController extends _$LibraryController {
  LibraryState? _cachedState;

  @override
  Future<LibraryState> build() async {
    if (_cachedState != null) {
      return _cachedState!;
    }

    final levelInfoRepository = ref.watch(levelInfoRepositoryProvider);
    final levels = await levelInfoRepository.getAllLevelInfo();
    _cachedState = LibraryState(levels: levels);
    return _cachedState!;
  }
}
