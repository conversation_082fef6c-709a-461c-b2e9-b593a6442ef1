import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/level.dart';

void main() {
  group('Level Enum Tests', () {
    test('should have correct values for all levels', () {
      expect(Level.a1.name, equals('A1'));
      expect(Level.a2.name, equals('A2'));
      expect(Level.b1.name, equals('B1'));
      expect(Level.b2.name, equals('B2'));
      expect(Level.c1.name, equals('C1'));
      expect(Level.c2.name, equals('C2'));
    });

    test('should convert to string correctly', () {
      expect(Level.a1.toString(), equals('A1'));
      expect(Level.a2.toString(), equals('A2'));
      expect(Level.b1.toString(), equals('B1'));
      expect(Level.b2.toString(), equals('B2'));
      expect(Level.c1.toString(), equals('C1'));
      expect(Level.c2.toString(), equals('C2'));
    });

    test('should compare levels correctly using index', () {
      expect(
        Level.values.indexOf(Level.a1) < Level.values.indexOf(Level.a2),
        isTrue,
      );
      expect(
        Level.values.indexOf(Level.a2) < Level.values.indexOf(Level.b1),
        isTrue,
      );
      expect(
        Level.values.indexOf(Level.b1) < Level.values.indexOf(Level.b2),
        isTrue,
      );
      expect(
        Level.values.indexOf(Level.b2) < Level.values.indexOf(Level.c1),
        isTrue,
      );
      expect(
        Level.values.indexOf(Level.c1) < Level.values.indexOf(Level.c2),
        isTrue,
      );
    });
  });
}
