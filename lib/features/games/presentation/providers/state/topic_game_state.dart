// observe_recall_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';

part 'topic_game_state.freezed.dart';

enum GameStage { topic, loading, observe, recall, result, finished, gameOver }

@freezed
sealed class TopicGameState with _$TopicGameState {
  factory TopicGameState({
    @Default(GameStage.topic) GameStage gameStage,
    @Default([]) List<MemoryFlashTopic> topics,
    @Default(0) int currentTopicIndex,
    @Default(false) bool shouldAdvanceToNextTopic,
  }) = _TopicGameState;
}
