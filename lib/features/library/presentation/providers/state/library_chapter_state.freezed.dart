// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'library_chapter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$LibraryChapterState {

 List<ChapterData> get chapters; LevelInfo? get levelInfo;
/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LibraryChapterStateCopyWith<LibraryChapterState> get copyWith => _$LibraryChapterStateCopyWithImpl<LibraryChapterState>(this as LibraryChapterState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LibraryChapterState&&const DeepCollectionEquality().equals(other.chapters, chapters)&&(identical(other.levelInfo, levelInfo) || other.levelInfo == levelInfo));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(chapters),levelInfo);

@override
String toString() {
  return 'LibraryChapterState(chapters: $chapters, levelInfo: $levelInfo)';
}


}

/// @nodoc
abstract mixin class $LibraryChapterStateCopyWith<$Res>  {
  factory $LibraryChapterStateCopyWith(LibraryChapterState value, $Res Function(LibraryChapterState) _then) = _$LibraryChapterStateCopyWithImpl;
@useResult
$Res call({
 List<ChapterData> chapters, LevelInfo? levelInfo
});


$LevelInfoCopyWith<$Res>? get levelInfo;

}
/// @nodoc
class _$LibraryChapterStateCopyWithImpl<$Res>
    implements $LibraryChapterStateCopyWith<$Res> {
  _$LibraryChapterStateCopyWithImpl(this._self, this._then);

  final LibraryChapterState _self;
  final $Res Function(LibraryChapterState) _then;

/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chapters = null,Object? levelInfo = freezed,}) {
  return _then(_self.copyWith(
chapters: null == chapters ? _self.chapters : chapters // ignore: cast_nullable_to_non_nullable
as List<ChapterData>,levelInfo: freezed == levelInfo ? _self.levelInfo : levelInfo // ignore: cast_nullable_to_non_nullable
as LevelInfo?,
  ));
}
/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelInfoCopyWith<$Res>? get levelInfo {
    if (_self.levelInfo == null) {
    return null;
  }

  return $LevelInfoCopyWith<$Res>(_self.levelInfo!, (value) {
    return _then(_self.copyWith(levelInfo: value));
  });
}
}


/// Adds pattern-matching-related methods to [LibraryChapterState].
extension LibraryChapterStatePatterns on LibraryChapterState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LibraryChapterState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LibraryChapterState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LibraryChapterState value)  $default,){
final _that = this;
switch (_that) {
case _LibraryChapterState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LibraryChapterState value)?  $default,){
final _that = this;
switch (_that) {
case _LibraryChapterState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ChapterData> chapters,  LevelInfo? levelInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LibraryChapterState() when $default != null:
return $default(_that.chapters,_that.levelInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ChapterData> chapters,  LevelInfo? levelInfo)  $default,) {final _that = this;
switch (_that) {
case _LibraryChapterState():
return $default(_that.chapters,_that.levelInfo);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ChapterData> chapters,  LevelInfo? levelInfo)?  $default,) {final _that = this;
switch (_that) {
case _LibraryChapterState() when $default != null:
return $default(_that.chapters,_that.levelInfo);case _:
  return null;

}
}

}

/// @nodoc


class _LibraryChapterState extends LibraryChapterState {
   _LibraryChapterState({final  List<ChapterData> chapters = const [], this.levelInfo}): _chapters = chapters,super._();
  

 final  List<ChapterData> _chapters;
@override@JsonKey() List<ChapterData> get chapters {
  if (_chapters is EqualUnmodifiableListView) return _chapters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_chapters);
}

@override final  LevelInfo? levelInfo;

/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LibraryChapterStateCopyWith<_LibraryChapterState> get copyWith => __$LibraryChapterStateCopyWithImpl<_LibraryChapterState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LibraryChapterState&&const DeepCollectionEquality().equals(other._chapters, _chapters)&&(identical(other.levelInfo, levelInfo) || other.levelInfo == levelInfo));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_chapters),levelInfo);

@override
String toString() {
  return 'LibraryChapterState(chapters: $chapters, levelInfo: $levelInfo)';
}


}

/// @nodoc
abstract mixin class _$LibraryChapterStateCopyWith<$Res> implements $LibraryChapterStateCopyWith<$Res> {
  factory _$LibraryChapterStateCopyWith(_LibraryChapterState value, $Res Function(_LibraryChapterState) _then) = __$LibraryChapterStateCopyWithImpl;
@override @useResult
$Res call({
 List<ChapterData> chapters, LevelInfo? levelInfo
});


@override $LevelInfoCopyWith<$Res>? get levelInfo;

}
/// @nodoc
class __$LibraryChapterStateCopyWithImpl<$Res>
    implements _$LibraryChapterStateCopyWith<$Res> {
  __$LibraryChapterStateCopyWithImpl(this._self, this._then);

  final _LibraryChapterState _self;
  final $Res Function(_LibraryChapterState) _then;

/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chapters = null,Object? levelInfo = freezed,}) {
  return _then(_LibraryChapterState(
chapters: null == chapters ? _self._chapters : chapters // ignore: cast_nullable_to_non_nullable
as List<ChapterData>,levelInfo: freezed == levelInfo ? _self.levelInfo : levelInfo // ignore: cast_nullable_to_non_nullable
as LevelInfo?,
  ));
}

/// Create a copy of LibraryChapterState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LevelInfoCopyWith<$Res>? get levelInfo {
    if (_self.levelInfo == null) {
    return null;
  }

  return $LevelInfoCopyWith<$Res>(_self.levelInfo!, (value) {
    return _then(_self.copyWith(levelInfo: value));
  });
}
}

// dart format on
