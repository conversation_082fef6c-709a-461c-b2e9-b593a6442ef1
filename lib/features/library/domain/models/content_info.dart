import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

part 'content_info.freezed.dart';
part 'content_info.g.dart';

@freezed
sealed class ContentInfo with _$ContentInfo {
  factory ContentInfo({
    required String title,
    required String image,
    required String icon,
    String? iconSecondary,
    required SectionType section,
    required int sectionColor,
    @Default(false) bool isExpanded,
  }) = _ContentInfo;

  factory ContentInfo.fromJson(dynamic json) => _$ContentInfoFromJson(json);
}
