import 'package:flutter/material.dart';
import 'package:selfeng/shared/globals.dart';

class ScoreItem extends StatelessWidget {
  final String? title; // Value between 0.0 and 1.0
  final int score;
  final bool reverse;

  const ScoreItem({
    super.key,
    this.title,
    required this.score,
    this.reverse = false,
  });

  @override
  Widget build(BuildContext context) {
    String image = '';
    if (score < 30) {
      image = '(0-29%).png';
    } else if (score < 50) {
      image = '(30-49).png';
    } else if (score < 70) {
      image = '(50-69).png';
    } else if (score < 90) {
      image = '(70-89).png';
    } else {
      image = '(90-100).png';
    }
    return Column(
      children: [
        if (title != null)
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xffFE754C),
                  Color(0xffE21F29),
                  Color(0xffC3151F),
                ],
                begin: reverse ? Alignment.bottomCenter : Alignment.topCenter,
                end: reverse ? Alignment.topCenter : Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 5.5, vertical: 2),
            width: 100,
            child: Text(
              title ?? '',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        if (title != null) SizedBox(height: 16),
        Container(
          height: 100,
          width: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xffFF544D).withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 10,
              ),
            ],
            image: DecorationImage(
              image: AssetImage('$assetImageMainLesson/$image'),
              fit: BoxFit.scaleDown,
            ),
          ),
          margin: const EdgeInsets.only(left: 16, right: 8),
          child: Center(
            child: Text(
              '${score.toStringAsFixed(0)}/100',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontSize: 20.0,
                fontWeight: FontWeight.w700,
                height: 1.25,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
