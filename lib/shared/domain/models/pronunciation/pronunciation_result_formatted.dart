import 'package:freezed_annotation/freezed_annotation.dart';

part 'pronunciation_result_formatted.freezed.dart';
part 'pronunciation_result_formatted.g.dart';

@freezed
sealed class PronunciationResultFormatted with _$PronunciationResultFormatted {
  factory PronunciationResultFormatted({
    required String originalText,
    required String recordedInput,
    required double accuracyScore,
    required double fluencyScore,
    required double prosodyScore,
    required double completenessScore,
    required double pronScore,
    required List<FormattedResult> formattedResult,
  }) = _PronunciationResultFormatted;
  factory PronunciationResultFormatted.fromJson(Map<String, dynamic> json) =>
      _$PronunciationResultFormattedFromJson(json);
}

@freezed
sealed class FormattedResult with _$FormattedResult {
  factory FormattedResult({
    required String text,
    required double accuracyScore,
    required String errorType,
  }) = _FormattedResult;
  factory FormattedResult.fromJson(Map<String, dynamic> json) =>
      _$FormattedResultFromJson(json);
}

@freezed
sealed class ScoreResult with _$ScoreResult {
  factory ScoreResult({
    @Default(0) int prosody,
    @Default(0) int phoneme,
    @Default(0) int completeness,
  }) = _ScoreResult;
  factory ScoreResult.fromJson(Map<String, dynamic> json) =>
      _$ScoreResultFromJson(json);
}
