// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ConversationState {

 List<ConversationPart> get conversations; QuestionResultModel? get result; int get currentPage; bool get expandedResult; AudioPath? get audioPath; Map<String, dynamic>? get response; String get stageTalking; int get selectedIndex; bool get nextSection; FlickManager? get flickManager; bool get isIntro;
/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConversationStateCopyWith<ConversationState> get copyWith => _$ConversationStateCopyWithImpl<ConversationState>(this as ConversationState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConversationState&&const DeepCollectionEquality().equals(other.conversations, conversations)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&const DeepCollectionEquality().equals(other.response, response)&&(identical(other.stageTalking, stageTalking) || other.stageTalking == stageTalking)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.flickManager, flickManager) || other.flickManager == flickManager)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(conversations),result,currentPage,expandedResult,audioPath,const DeepCollectionEquality().hash(response),stageTalking,selectedIndex,nextSection,flickManager,isIntro);

@override
String toString() {
  return 'ConversationState(conversations: $conversations, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, stageTalking: $stageTalking, selectedIndex: $selectedIndex, nextSection: $nextSection, flickManager: $flickManager, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class $ConversationStateCopyWith<$Res>  {
  factory $ConversationStateCopyWith(ConversationState value, $Res Function(ConversationState) _then) = _$ConversationStateCopyWithImpl;
@useResult
$Res call({
 List<ConversationPart> conversations, QuestionResultModel? result, int currentPage, bool expandedResult, AudioPath? audioPath, Map<String, dynamic>? response, String stageTalking, int selectedIndex, bool nextSection, FlickManager? flickManager, bool isIntro
});


$QuestionResultModelCopyWith<$Res>? get result;$AudioPathCopyWith<$Res>? get audioPath;

}
/// @nodoc
class _$ConversationStateCopyWithImpl<$Res>
    implements $ConversationStateCopyWith<$Res> {
  _$ConversationStateCopyWithImpl(this._self, this._then);

  final ConversationState _self;
  final $Res Function(ConversationState) _then;

/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? conversations = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? stageTalking = null,Object? selectedIndex = null,Object? nextSection = null,Object? flickManager = freezed,Object? isIntro = null,}) {
  return _then(_self.copyWith(
conversations: null == conversations ? _self.conversations : conversations // ignore: cast_nullable_to_non_nullable
as List<ConversationPart>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,stageTalking: null == stageTalking ? _self.stageTalking : stageTalking // ignore: cast_nullable_to_non_nullable
as String,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,flickManager: freezed == flickManager ? _self.flickManager : flickManager // ignore: cast_nullable_to_non_nullable
as FlickManager?,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}
}


/// Adds pattern-matching-related methods to [ConversationState].
extension ConversationStatePatterns on ConversationState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConversationState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConversationState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConversationState value)  $default,){
final _that = this;
switch (_that) {
case _ConversationState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConversationState value)?  $default,){
final _that = this;
switch (_that) {
case _ConversationState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ConversationPart> conversations,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  String stageTalking,  int selectedIndex,  bool nextSection,  FlickManager? flickManager,  bool isIntro)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConversationState() when $default != null:
return $default(_that.conversations,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.stageTalking,_that.selectedIndex,_that.nextSection,_that.flickManager,_that.isIntro);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ConversationPart> conversations,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  String stageTalking,  int selectedIndex,  bool nextSection,  FlickManager? flickManager,  bool isIntro)  $default,) {final _that = this;
switch (_that) {
case _ConversationState():
return $default(_that.conversations,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.stageTalking,_that.selectedIndex,_that.nextSection,_that.flickManager,_that.isIntro);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ConversationPart> conversations,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  Map<String, dynamic>? response,  String stageTalking,  int selectedIndex,  bool nextSection,  FlickManager? flickManager,  bool isIntro)?  $default,) {final _that = this;
switch (_that) {
case _ConversationState() when $default != null:
return $default(_that.conversations,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.stageTalking,_that.selectedIndex,_that.nextSection,_that.flickManager,_that.isIntro);case _:
  return null;

}
}

}

/// @nodoc


class _ConversationState extends ConversationState {
   _ConversationState({final  List<ConversationPart> conversations = const [], this.result, this.currentPage = 0, this.expandedResult = false, this.audioPath, final  Map<String, dynamic>? response, this.stageTalking = 'stage 1', this.selectedIndex = 0, this.nextSection = false, this.flickManager, this.isIntro = false}): _conversations = conversations,_response = response,super._();
  

 final  List<ConversationPart> _conversations;
@override@JsonKey() List<ConversationPart> get conversations {
  if (_conversations is EqualUnmodifiableListView) return _conversations;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_conversations);
}

@override final  QuestionResultModel? result;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  bool expandedResult;
@override final  AudioPath? audioPath;
 final  Map<String, dynamic>? _response;
@override Map<String, dynamic>? get response {
  final value = _response;
  if (value == null) return null;
  if (_response is EqualUnmodifiableMapView) return _response;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey() final  String stageTalking;
@override@JsonKey() final  int selectedIndex;
@override@JsonKey() final  bool nextSection;
@override final  FlickManager? flickManager;
@override@JsonKey() final  bool isIntro;

/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConversationStateCopyWith<_ConversationState> get copyWith => __$ConversationStateCopyWithImpl<_ConversationState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConversationState&&const DeepCollectionEquality().equals(other._conversations, _conversations)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&const DeepCollectionEquality().equals(other._response, _response)&&(identical(other.stageTalking, stageTalking) || other.stageTalking == stageTalking)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.flickManager, flickManager) || other.flickManager == flickManager)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_conversations),result,currentPage,expandedResult,audioPath,const DeepCollectionEquality().hash(_response),stageTalking,selectedIndex,nextSection,flickManager,isIntro);

@override
String toString() {
  return 'ConversationState(conversations: $conversations, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, stageTalking: $stageTalking, selectedIndex: $selectedIndex, nextSection: $nextSection, flickManager: $flickManager, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class _$ConversationStateCopyWith<$Res> implements $ConversationStateCopyWith<$Res> {
  factory _$ConversationStateCopyWith(_ConversationState value, $Res Function(_ConversationState) _then) = __$ConversationStateCopyWithImpl;
@override @useResult
$Res call({
 List<ConversationPart> conversations, QuestionResultModel? result, int currentPage, bool expandedResult, AudioPath? audioPath, Map<String, dynamic>? response, String stageTalking, int selectedIndex, bool nextSection, FlickManager? flickManager, bool isIntro
});


@override $QuestionResultModelCopyWith<$Res>? get result;@override $AudioPathCopyWith<$Res>? get audioPath;

}
/// @nodoc
class __$ConversationStateCopyWithImpl<$Res>
    implements _$ConversationStateCopyWith<$Res> {
  __$ConversationStateCopyWithImpl(this._self, this._then);

  final _ConversationState _self;
  final $Res Function(_ConversationState) _then;

/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? conversations = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? stageTalking = null,Object? selectedIndex = null,Object? nextSection = null,Object? flickManager = freezed,Object? isIntro = null,}) {
  return _then(_ConversationState(
conversations: null == conversations ? _self._conversations : conversations // ignore: cast_nullable_to_non_nullable
as List<ConversationPart>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self._response : response // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,stageTalking: null == stageTalking ? _self.stageTalking : stageTalking // ignore: cast_nullable_to_non_nullable
as String,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,flickManager: freezed == flickManager ? _self.flickManager : flickManager // ignore: cast_nullable_to_non_nullable
as FlickManager?,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}/// Create a copy of ConversationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}
}

// dart format on
