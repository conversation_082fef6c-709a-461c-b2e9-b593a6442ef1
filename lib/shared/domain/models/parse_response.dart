// To parse this JSON data, do
//
//     final parseResponse = parseResponseFromMap(jsonString);

class ParseResponse<T> {
  ParseResponse({
    this.status,
    this.message,
    this.data,
    this.refreshToken,
    this.success = false,
  });
  final bool success;
  final String? status;
  final String? message;
  final String? refreshToken;
  final T? data;

  factory ParseResponse.fromMap(dynamic json, {T Function(dynamic)? modifier}) {
    return ParseResponse<T>(
      success: json['success'],
      status: json['status'],
      message: json['message'],
      // data: modifier(json),
      data: json['data'],
      refreshToken: json['refresh_token'],
    );
  }
}
