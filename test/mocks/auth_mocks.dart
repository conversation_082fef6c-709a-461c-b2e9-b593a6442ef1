import 'package:mocktail/mocktail.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

class MockGoogleSignInRepository extends Mock
    implements GoogleSignInRepository {}

class MockFirebaseUser extends Mock implements firebase_auth.User {}

class MockUserCredential extends Mock implements firebase_auth.UserCredential {}

class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}

class MockGoogleSignInAuthentication extends Mock
    implements GoogleSignInAuthentication {}
