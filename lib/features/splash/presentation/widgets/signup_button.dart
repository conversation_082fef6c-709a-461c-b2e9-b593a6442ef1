import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/authentication/presentation/providers/auth_controller.dart';
import 'package:selfeng/features/authentication/presentation/providers/state/auth_state.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/theme/text_styles.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

const List<Color> kSignupButtonGradientColors = [
  Color(0xff802115),
  Color(0xffEB1A19),
];

class SignupButton extends ConsumerStatefulWidget {
  const SignupButton({super.key, required this.width});

  final double width;

  @override
  ConsumerState<SignupButton> createState() => _SignupButtonState();
}

class _SignupButtonState extends ConsumerState<SignupButton> {
  bool _isLoading = false;
  bool _dialogShown = false;

  void _showLoadingDialog() {
    if (!_dialogShown && mounted) {
      _dialogShown = true;
      AuthLoadingDialog.show(context);
    }
  }

  void _hideLoadingDialog() {
    if (_dialogShown && mounted) {
      _dialogShown = false;
      AuthLoadingDialog.hide(context);
    }
  }

  Future<void> _handleSignup(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    // Show loading dialog immediately
    _showLoadingDialog();

    try {
      await ref.read(authControllerProvider.notifier).loginUser();
      // Don't navigate here - let the auth state listener handle navigation
      // The router will automatically redirect based on auth state
      // Dialog will be hidden by the auth state listener
    } catch (e) {
      // Handle error if needed
      debugPrint('Sign-up error: $e');
      // Hide dialog on error
      _hideLoadingDialog();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    // Ensure dialog is hidden when widget is disposed
    if (_dialogShown) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
      });
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes to hide dialog when authentication completes
    ref.listen<AsyncValue<AuthState>>(authControllerProvider, (previous, next) {
      if (!mounted) return;

      next.when(
        data: (authState) {
          // Hide dialog when authentication succeeds or fails
          if (_dialogShown && (authState.isAuth || authState is Failure)) {
            _hideLoadingDialog();
          }
        },
        loading: () {
          // Keep dialog shown during loading
        },
        error: (error, stackTrace) {
          // Hide dialog on error
          if (_dialogShown) {
            _hideLoadingDialog();
          }
        },
      );
    });

    return InkWell(
      onTap: _isLoading ? null : () => _handleSignup(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          gradient: const LinearGradient(
            colors: kSignupButtonGradientColors,
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          border: Border.all(color: const Color(0xffFFDAD6), width: 2),
        ),
        width: widget.width - 40,
        padding: const EdgeInsets.symmetric(vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 4,
          children: [
            if (_isLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            else ...[
              Text(
                context.loc.onboardingSingup,
                style: AppTextStyles.addOnsBodySemiBold.copyWith(
                  color: Colors.white,
                ),
              ),
              Image.asset('$assetImageOnboarding/GOOGLE rounded.png'),
            ],
          ],
        ),
      ),
    );
  }
}
