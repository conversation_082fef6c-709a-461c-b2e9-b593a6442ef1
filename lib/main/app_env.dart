// ignore_for_file: constant_identifier_names, use_setters_to_change_properties, avoid_classes_with_only_static_members
import 'package:flutter_dotenv/flutter_dotenv.dart';

enum AppEnvironment { DEV, STAGING, PROD }

abstract class EnvInfo {
  static AppEnvironment _environment = AppEnvironment.DEV;

  static void initialize(AppEnvironment environment) {
    EnvInfo._environment = environment;
  }

  static String get appName => _environment._appName;
  static String get envName => _environment._envName;
  static String get appUrl => _environment._appUrl;
  static AppEnvironment get environment => _environment;
  static bool get isProduction => _environment == AppEnvironment.PROD;

  // Getter for general value
  static String get firebaseProjectId => _environment._firebaseProjectId;
  static String get firebaseAuthDomain => _environment._firebaseAuthDomain;
  static String get firebaseStorageBucket =>
      _environment._firebaseStorageBucket;

  // Getters for Firebase Web options
  static String get firebaseApiKeyWeb => _environment._firebaseApiKeyWeb;
  static String get firebaseAppIdWeb => _environment._firebaseAppIdWeb;
  static String get firebaseMessagingSenderIdWeb =>
      _environment._firebaseMessagingSenderIdWeb;
  static String get firebaseMeasurementIdWeb =>
      _environment._firebaseMeasurementIdWeb;

  // Getters for Firebase Android options
  static String get firebaseApiKeyAndroid =>
      _environment._firebaseApiKeyAndroid;
  static String get firebaseAppIdAndroid => _environment._firebaseAppIdAndroid;
  static String get firebaseMessagingSenderIdAndroid =>
      _environment._firebaseMessagingSenderIdAndroid;

  // Getters for Firebase iOS options
  static String get firebaseApiKeyIos => _environment._firebaseApiKeyIos;
  static String get firebaseAppIdIos => _environment._firebaseAppIdIos;
  static String get firebaseMessagingSenderIdIos =>
      _environment._firebaseMessagingSenderIdIos;

  // Getters for Client IDs and Bundle IDs
  static String get firebaseAndroidClientId =>
      _environment._firebaseAndroidClientId;
  static String get firebaseIosClientId => _environment._firebaseIosClientId;
  static String get firebaseIosBundleId => _environment._firebaseIosBundleId;
}

extension _EnvProperties on AppEnvironment {
  static const _envs = {
    AppEnvironment.DEV: '.env.dev',
    AppEnvironment.STAGING: '.env.stg',
    AppEnvironment.PROD: '.env.prod',
  };

  String get _envName => _envs[this]!;

  String get _appName => dotenv.env['APP_TITLE'] ?? 'APP_TITLE not defined';

  String get _appUrl => dotenv.env['APP_URL'] ?? 'APP_URL not defined';

  // Getter for general value
  String get _firebaseProjectId =>
      dotenv.env['FIREBASE_PROJECT_ID'] ?? 'FIREBASE_PROJECT_ID not defined';
  String get _firebaseStorageBucket =>
      dotenv.env['FIREBASE_STORAGE_BUCKET'] ??
      'FIREBASE_STORAGE_BUCKET not defined';

  // Getter for Firebase Web options
  String get _firebaseApiKeyWeb =>
      dotenv.env['FIREBASE_API_KEY_WEB'] ?? 'FIREBASE_API_KEY_WEB not defined';
  String get _firebaseAppIdWeb =>
      dotenv.env['FIREBASE_APP_ID_WEB'] ?? 'FIREBASE_APP_ID_WEB not defined';
  String get _firebaseMessagingSenderIdWeb =>
      dotenv.env['FIREBASE_MESSAGING_SENDER_ID_WEB'] ??
      'FIREBASE_MESSAGING_SENDER_ID_WEB not defined';
  String get _firebaseAuthDomain =>
      dotenv.env['FIREBASE_AUTH_DOMAIN'] ?? 'FIREBASE_AUTH_DOMAIN not defined';
  String get _firebaseMeasurementIdWeb =>
      dotenv.env['FIREBASE_MEASUREMENT_ID_WEB'] ??
      'FIREBASE_MEASUREMENT_ID_WEB not defined';

  // Getter for Firebase Android options
  String get _firebaseApiKeyAndroid =>
      dotenv.env['FIREBASE_API_KEY_ANDROID'] ??
      'FIREBASE_API_KEY_ANDROID not defined';
  String get _firebaseAppIdAndroid =>
      dotenv.env['FIREBASE_APP_ID_ANDROID'] ??
      'FIREBASE_APP_ID_ANDROID not defined';
  String get _firebaseMessagingSenderIdAndroid =>
      dotenv.env['FIREBASE_MESSAGING_SENDER_ID_ANDROID'] ??
      'FIREBASE_MESSAGING_SENDER_ID_ANDROID not defined';

  // Getter for Firebase iOS options
  String get _firebaseApiKeyIos =>
      dotenv.env['FIREBASE_API_KEY_IOS'] ?? 'FIREBASE_API_KEY_IOS not defined';
  String get _firebaseAppIdIos =>
      dotenv.env['FIREBASE_APP_ID_IOS'] ?? 'FIREBASE_APP_ID_IOS not defined';
  String get _firebaseMessagingSenderIdIos =>
      dotenv.env['FIREBASE_MESSAGING_SENDER_ID_IOS'] ??
      'FIREBASE_MESSAGING_SENDER_ID_IOS not defined';

  // Getter for Client IDs and Bundle IDs
  String get _firebaseAndroidClientId =>
      dotenv.env['FIREBASE_ANDROID_CLIENT_ID'] ??
      'FIREBASE_ANDROID_CLIENT_ID not defined';
  String get _firebaseIosClientId =>
      dotenv.env['FIREBASE_IOS_CLIENT_ID'] ??
      'FIREBASE_IOS_CLIENT_ID not defined';
  String get _firebaseIosBundleId =>
      dotenv.env['FIREBASE_IOS_BUNDLE_ID'] ??
      'FIREBASE_IOS_BUNDLE_ID not defined';
}
