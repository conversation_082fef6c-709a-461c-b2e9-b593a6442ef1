import 'package:flutter/material.dart';

class VDialogAlert extends StatelessWidget {
  const VDialogAlert({
    super.key,
    required this.title,
    this.image,
    this.icon,
    this.child,
    this.barrierDismissible = true,
  });
  final String title;
  final String? image;
  final icon;
  final Widget? child;
  final bool barrierDismissible;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) => showMyDialog(context));
    return Container();
  }

  Future<Future> showMyDialog(context) async {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.symmetric(
            vertical: 52,
            horizontal: 39,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) icon,
              if (image != null)
                Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xffFFB3AC),
                    gradient: LinearGradient(
                      colors: [Color(0xff421507), Color(0xffCA1E23)],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    ),
                  ),
                  padding: const EdgeInsets.all(5),
                  height: 164,
                  width: 164,
                  child: Image.asset(image!),
                ),
              const SizedBox(height: 19),
              Text(title, style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: 10),
              child ?? Container(),
            ],
          ),
        );
      },
    );
  }
}
