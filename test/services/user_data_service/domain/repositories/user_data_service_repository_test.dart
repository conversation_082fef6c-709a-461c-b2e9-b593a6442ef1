import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../../mocks/firebase_mocks.dart';

// Additional mock classes for this test
class MockFirebaseFunctions extends Mock implements FirebaseFunctions {}

class MockHttpsCallable extends Mock implements HttpsCallable {}

class MockHttpsCallableResult extends Mock implements HttpsCallableResult {}

// Firestore wrapper backed by FakeFirebaseFirestore so we avoid mocking sealed SDK classes
class FakeFirestoreServiceRepository implements FirestoreServiceRepository {
  FakeFirestoreServiceRepository(
    this.db,
    this._auth,
    this._storage,
    this._functions,
  );
  final FirebaseFirestore db;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;
  final FirebaseFunctions _functions;

  @override
  DocumentReference<Map<String, dynamic>> dataUser() =>
      db.collection('user-data').doc(_auth.currentUser?.uid);

  @override
  FirebaseFirestore get fireStore => db;

  @override
  FirebaseStorage get firebaseStorage => _storage;

  @override
  FirebaseAuth get firebaseAuth => _auth;

  @override
  FirebaseFunctions get firebaseFunctions => _functions;
}

void main() {
  group('UserDataServiceRepository', () {
    late UserDataServiceRepository repository;
    late FakeFirebaseFirestore fakeDb;
    late MockFirebaseAuth mockAuth;
    late MockFirebaseStorage mockStorage;
    late MockFirebaseFunctions mockFirebaseFunctions;
    late MockHttpsCallable mockHttpsCallable;

    setUp(() {
      fakeDb = FakeFirebaseFirestore();
      mockAuth = MockFirebaseAuth();
      mockStorage = MockFirebaseStorage();
      mockFirebaseFunctions = MockFirebaseFunctions();
      mockHttpsCallable = MockHttpsCallable();

      repository = UserDataServiceRepository(
        FakeFirestoreServiceRepository(
          fakeDb,
          mockAuth,
          mockStorage,
          mockFirebaseFunctions,
        ),
      );

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
      registerFallbackValue(SetOptions(merge: true));
      registerFallbackValue(FieldValue.serverTimestamp());
    });

    group('getUserData', () {
      test('should return user data when document exists', () async {
        // Arrange
        final userData = UserData(email: '<EMAIL>', afterTest: true);
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        // Set the user data in the fake database
        await fakeDb.collection('user-data').doc('u1').set(userData.toJson());

        when(
          () => mockFirebaseFunctions.httpsCallable('reinitUserData'),
        ).thenReturn(mockHttpsCallable);
        when(
          () => mockHttpsCallable.call(),
        ).thenAnswer((_) async => MockHttpsCallableResult());

        // Act
        final result = await repository.getUserData();

        // Assert
        expect(result, isA<Right<AppException, UserData>>());
        expect(
          (result as Right<AppException, UserData>).value.email,
          equals('<EMAIL>'),
        );
      });

      test(
        'should return Left with AppException when getting user data fails',
        () async {
          // Arrange: simulate reinitUserData failure
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);
          when(
            () => mockFirebaseFunctions.httpsCallable('reinitUserData'),
          ).thenReturn(mockHttpsCallable);
          when(
            () => mockHttpsCallable.call(),
          ).thenThrow(Exception('Failed to get user data'));

          // Act
          final result = await repository.getUserData();

          // Assert
          expect(result, isA<Left<AppException, UserData>>());
          expect(
            (result as Left<AppException, UserData>).value.identifier,
            equals('Failed fetch user data'),
          );
        },
      );
    });

    group('updateLastCourse', () {
      test('should update last course for pronunciation section', () async {
        // Arrange
        final lastCourse = LastCourse(
          accessTime: DateTime(2023, 1, 1),
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: '/path/to/pronunciation',
        );
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);
        await fakeDb.collection('user-data').doc('u1').set({});

        // Act
        final result = await repository.updateLastCourse(
          lastCourse: lastCourse,
          section: SectionType.pronunciation,
        );

        // Assert
        expect(result, isA<Right<AppException, LastCourse>>());
        final snap = await fakeDb.collection('user-data').doc('u1').get();
        expect(
          (snap.data()!['last_pronunciation'] as Map)['path'],
          '/path/to/pronunciation',
        );
      });

      test(
        'should return Left with AppException when updating last course fails',
        () async {
          // Arrange: no user
          when(() => mockAuth.currentUser).thenReturn(null);

          // Act
          final result = await repository.updateLastCourse(
            lastCourse: LastCourse(
              accessTime: DateTime(2023, 1, 1),
              level: 'A1',
              chapter: 1,
              section: SectionType.pronunciation,
              path: '/path',
            ),
            section: SectionType.pronunciation,
          );

          // Assert
          expect(result, isA<Left<AppException, LastCourse>>());
        },
      );
    });

    group('saveLessonResult', () {
      test('should save lesson result successfully', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);
        await fakeDb.collection('user-data').doc('u1').set({});

        final lessonResult = LessonResult(
          contentOrder: 1,
          path: '/path/to/content',
          result: <String, dynamic>{},
        );

        // Act
        final res = await repository.saveLessonResult(
          level: 'A1',
          chapter: '1',
          section: SectionType.pronunciation,
          result: lessonResult,
        );

        // Assert
        expect(res, isA<Right<AppException, dynamic>>());
        final resultsColl =
            await fakeDb
                .collection('user-data')
                .doc('u1')
                .collection('lessons')
                .doc('A1')
                .collection('chapters')
                .doc('1')
                .collection('sections')
                .doc('pronunciation')
                .collection('results')
                .get();
        expect(resultsColl.docs.length, 1);
      });

      test(
        'should save lesson result successfully even when user is not authenticated',
        () async {
          // Arrange: no user (should still work with generated document ID)
          when(() => mockAuth.currentUser).thenReturn(null);
          final lessonResult = LessonResult(
            contentOrder: 1,
            path: '/path/to/content',
            result: <String, dynamic>{},
          );

          // Act
          final res = await repository.saveLessonResult(
            level: 'A1',
            chapter: '1',
            section: SectionType.pronunciation,
            result: lessonResult,
          );

          // Assert
          expect(res, isA<Right<AppException, dynamic>>());
        },
      );
    });
  });
}
