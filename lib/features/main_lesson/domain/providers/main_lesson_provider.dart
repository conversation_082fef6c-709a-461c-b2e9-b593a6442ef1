import 'package:selfeng/features/main_lesson/data/datasource/main_lesson_local_datasource.dart';
import 'package:selfeng/features/main_lesson/data/datasource/main_lesson_remote_datasource.dart';
import 'package:selfeng/features/main_lesson/data/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';

final mainLessonRepositoryProvider = Provider<MainLessonRepository>((ref) {
  final firestore = ref.watch(firestoreServiceRepositoryProvider);
  final localStore = ref.watch(storageServiceProvider);
  return MainLessonRepositoryImpl(
    localDatasource: MainLessonLocalDataSourceImpl(localStore),
    remoteDatasource: MainLessonRemoteDatasourceImpl(firestore),
  );
});
